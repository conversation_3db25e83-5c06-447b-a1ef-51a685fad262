#!/bin/bash

# Script để tùy chỉnh mô hình freemium
# Tạo giao diện billing và tùy chỉnh giới hạn

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    Customize Freemium Model - WorkSuite SAAS"
    echo "=================================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

create_freemium_billing_controller() {
    print_step "Creating freemium billing controller..."
    
    cat > app/Http/Controllers/FreemiumBillingController.php << 'EOF'
<?php

namespace App\Http\Controllers;

use App\Models\Package;
use App\Models\Company;
use App\Helper\Reply;
use Illuminate\Http\Request;

class FreemiumBillingController extends AccountBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = 'Billing & Subscription';
    }

    public function index()
    {
        $this->packages = Package::where('is_free', 0)->orderBy('sort')->get();
        $this->currentPackage = company()->package;
        $this->usageStats = $this->getUsageStats();
        
        return view('freemium.billing.index', $this->data);
    }

    public function upgradePlan()
    {
        $this->packages = Package::where('is_free', 0)->orderBy('sort')->get();
        $this->currentPackage = company()->package;
        
        return view('freemium.billing.upgrade', $this->data);
    }

    public function selectPlan(Request $request)
    {
        $package = Package::findOrFail($request->package_id);
        $planType = $request->plan_type; // monthly or annual
        
        $this->package = $package;
        $this->planType = $planType;
        $this->amount = $planType === 'monthly' ? $package->monthly_price : $package->annual_price;
        
        return view('freemium.billing.payment', $this->data);
    }

    public function processPayment(Request $request)
    {
        // Tích hợp với payment gateway ở đây
        // Stripe, PayPal, etc.
        
        $package = Package::findOrFail($request->package_id);
        $company = company();
        
        // Simulate successful payment for demo
        $company->package_id = $package->id;
        $company->package_type = $request->plan_type;
        $company->licence_expire_on = $request->plan_type === 'monthly' 
            ? now()->addMonth() 
            : now()->addYear();
        $company->save();
        
        // Clear cache
        cache()->forget('company_' . $company->id . '_valid_package');
        cache()->forget('company_' . $company->id . '_can_add_more_employees');
        
        return Reply::redirect(route('freemium.billing.success'), 'Payment successful! Your plan has been upgraded.');
    }

    public function paymentSuccess()
    {
        $this->currentPackage = company()->package;
        return view('freemium.billing.success', $this->data);
    }

    public function usage()
    {
        $this->usageStats = $this->getUsageStats();
        $this->limits = $this->getCurrentLimits();
        
        return view('freemium.billing.usage', $this->data);
    }

    private function getUsageStats()
    {
        $company = company();
        
        return [
            'employees' => $company->employees()->count(),
            'projects' => $company->projects()->count(),
            'clients' => $company->clients()->count(),
            'storage_mb' => $this->getStorageUsage(),
            'invoices' => $company->invoices()->count(),
            'tasks' => $company->tasks()->count(),
        ];
    }

    private function getCurrentLimits()
    {
        $package = company()->package;
        
        return [
            'employees' => $package->max_employees,
            'projects' => $package->max_projects ?? 999,
            'clients' => $package->max_clients ?? 999,
            'storage_mb' => $package->storage_unit === 'gb' 
                ? $package->max_storage_size * 1024 
                : $package->max_storage_size,
        ];
    }

    private function getStorageUsage()
    {
        // Calculate storage usage in MB
        $fileStorage = \App\Models\FileStorage::where('company_id', company()->id)->sum('size');
        return round($fileStorage / (1024 * 1024), 2);
    }
}
EOF

    print_success "Freemium billing controller created!"
}

create_freemium_routes() {
    print_step "Creating freemium routes..."
    
    cat > routes/freemium.php << 'EOF'
<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\FreemiumBillingController;

Route::group(['middleware' => ['auth', 'role:admin|employee']], function () {
    
    // Freemium Billing Routes
    Route::prefix('freemium')->name('freemium.')->group(function () {
        
        Route::prefix('billing')->name('billing.')->group(function () {
            Route::get('/', [FreemiumBillingController::class, 'index'])->name('index');
            Route::get('/upgrade', [FreemiumBillingController::class, 'upgradePlan'])->name('upgrade');
            Route::get('/usage', [FreemiumBillingController::class, 'usage'])->name('usage');
            Route::post('/select-plan', [FreemiumBillingController::class, 'selectPlan'])->name('select_plan');
            Route::post('/process-payment', [FreemiumBillingController::class, 'processPayment'])->name('process_payment');
            Route::get('/success', [FreemiumBillingController::class, 'paymentSuccess'])->name('success');
        });
        
    });
    
});
EOF

    # Add to main web.php
    if ! grep -q "freemium.php" routes/web.php; then
        echo "" >> routes/web.php
        echo "// Freemium Routes" >> routes/web.php
        echo "require __DIR__.'/freemium.php';" >> routes/web.php
    fi

    print_success "Freemium routes created!"
}

create_freemium_views() {
    print_step "Creating freemium billing views..."
    
    mkdir -p resources/views/freemium/billing
    
    # Main billing page
    cat > resources/views/freemium/billing/index.blade.php << 'EOF'
@extends('layouts.app')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Billing & Subscription') }}</h1>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            
            <!-- Current Plan -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Current Plan</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>{{ $currentPackage->name }}</h4>
                                    <p class="text-muted">{{ $currentPackage->description }}</p>
                                    @if($currentPackage->is_free)
                                        <span class="badge badge-success">Free Plan</span>
                                    @else
                                        <span class="badge badge-primary">Premium Plan</span>
                                    @endif
                                </div>
                                <div class="col-md-6 text-right">
                                    @if(!$currentPackage->is_free)
                                        <h3>${{ company()->package_type === 'monthly' ? $currentPackage->monthly_price : $currentPackage->annual_price }}</h3>
                                        <p class="text-muted">per {{ company()->package_type === 'monthly' ? 'month' : 'year' }}</p>
                                        <p><small>Expires: {{ company()->licence_expire_on ? company()->licence_expire_on->format('M d, Y') : 'Never' }}</small></p>
                                    @endif
                                    <a href="{{ route('freemium.billing.upgrade') }}" class="btn btn-primary">
                                        <i class="fa fa-arrow-up"></i> Upgrade Plan
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3>{{ $usageStats['employees'] }}/{{ $currentPackage->max_employees }}</h3>
                            <p class="text-muted">Employees</p>
                            <div class="progress">
                                <div class="progress-bar" style="width: {{ ($usageStats['employees'] / $currentPackage->max_employees) * 100 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3>{{ $usageStats['projects'] }}</h3>
                            <p class="text-muted">Projects</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3>{{ $usageStats['clients'] }}</h3>
                            <p class="text-muted">Clients</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3>{{ $usageStats['storage_mb'] }}MB</h3>
                            <p class="text-muted">Storage Used</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Plans -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Available Plans</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($packages as $package)
                                <div class="col-md-4">
                                    <div class="card {{ $package->is_recommended ? 'border-primary' : '' }}">
                                        @if($package->is_recommended)
                                            <div class="card-header bg-primary text-white text-center">
                                                <small>RECOMMENDED</small>
                                            </div>
                                        @endif
                                        <div class="card-body text-center">
                                            <h4>{{ $package->name }}</h4>
                                            <p class="text-muted">{{ $package->description }}</p>
                                            <h3>${{ $package->monthly_price }}<small>/month</small></h3>
                                            <p><small>${{ $package->annual_price }}/year (Save {{ round((1 - ($package->annual_price / ($package->monthly_price * 12))) * 100) }}%)</small></p>
                                            
                                            <ul class="list-unstyled">
                                                <li><i class="fa fa-check text-success"></i> {{ $package->max_employees }} Employees</li>
                                                <li><i class="fa fa-check text-success"></i> {{ $package->max_storage_size }}{{ $package->storage_unit === 'gb' ? 'GB' : 'MB' }} Storage</li>
                                                <li><i class="fa fa-check text-success"></i> All Modules</li>
                                            </ul>
                                            
                                            @if($currentPackage->id === $package->id)
                                                <button class="btn btn-secondary" disabled>Current Plan</button>
                                            @else
                                                <a href="{{ route('freemium.billing.upgrade') }}?package={{ $package->id }}" class="btn btn-primary">
                                                    Choose Plan
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
@endsection
EOF

    # Upgrade page
    cat > resources/views/freemium/billing/upgrade.blade.php << 'EOF'
@extends('layouts.app')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Upgrade Your Plan') }}</h1>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            
            <div class="row justify-content-center">
                @foreach($packages as $package)
                <div class="col-md-4">
                    <div class="card {{ $package->is_recommended ? 'border-primary' : '' }}">
                        @if($package->is_recommended)
                            <div class="card-header bg-primary text-white text-center">
                                <small>MOST POPULAR</small>
                            </div>
                        @endif
                        <div class="card-body text-center">
                            <h4>{{ $package->name }}</h4>
                            <p class="text-muted">{{ $package->description }}</p>
                            
                            <div class="pricing-toggle mb-3">
                                <label class="switch">
                                    <input type="checkbox" class="plan-toggle" data-package="{{ $package->id }}">
                                    <span class="slider"></span>
                                </label>
                                <span class="ml-2">Annual (Save {{ round((1 - ($package->annual_price / ($package->monthly_price * 12))) * 100) }}%)</span>
                            </div>
                            
                            <div class="price-display">
                                <h3 class="monthly-price">${{ $package->monthly_price }}<small>/month</small></h3>
                                <h3 class="annual-price" style="display: none;">${{ $package->annual_price }}<small>/year</small></h3>
                            </div>
                            
                            <ul class="list-unstyled mt-3">
                                <li><i class="fa fa-check text-success"></i> {{ $package->max_employees }} Employees</li>
                                <li><i class="fa fa-check text-success"></i> {{ $package->max_storage_size }}{{ $package->storage_unit === 'gb' ? 'GB' : 'MB' }} Storage</li>
                                <li><i class="fa fa-check text-success"></i> All Premium Features</li>
                                <li><i class="fa fa-check text-success"></i> Priority Support</li>
                            </ul>
                            
                            <form action="{{ route('freemium.billing.select_plan') }}" method="POST">
                                @csrf
                                <input type="hidden" name="package_id" value="{{ $package->id }}">
                                <input type="hidden" name="plan_type" value="monthly" class="plan-type-input">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    Choose This Plan
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

        </div>
    </section>
</div>

<style>
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:checked + .slider:before {
    transform: translateX(26px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggles = document.querySelectorAll('.plan-toggle');
    
    toggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const card = this.closest('.card');
            const monthlyPrice = card.querySelector('.monthly-price');
            const annualPrice = card.querySelector('.annual-price');
            const planTypeInput = card.querySelector('.plan-type-input');
            
            if (this.checked) {
                monthlyPrice.style.display = 'none';
                annualPrice.style.display = 'block';
                planTypeInput.value = 'annual';
            } else {
                monthlyPrice.style.display = 'block';
                annualPrice.style.display = 'none';
                planTypeInput.value = 'monthly';
            }
        });
    });
});
</script>
@endsection
EOF

    print_success "Freemium billing views created!"
}

create_freemium_middleware() {
    print_step "Creating freemium feature middleware..."
    
    cat > app/Http/Middleware/CheckFreemiumFeature.php << 'EOF'
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckFreemiumFeature
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $feature): Response
    {
        if (!checkFreemiumLimit($feature)) {
            $message = getUpgradeMessage($feature);
            
            if ($request->ajax()) {
                return response()->json([
                    'status' => 'upgrade_required',
                    'message' => $message,
                    'redirect_url' => route('freemium.billing.upgrade')
                ]);
            }
            
            return redirect()->route('freemium.billing.upgrade')
                ->with('upgrade_notice', $message);
        }
        
        return $next($request);
    }
}
EOF

    print_success "Freemium feature middleware created!"
}

update_package_model() {
    print_step "Updating Package model for freemium..."
    
    # Add freemium methods to Package model
    cat >> app/Models/Package.php << 'EOF'

    // Freemium helper methods
    public function isFreePlan()
    {
        return $this->is_free == 1;
    }
    
    public function isPremiumPlan()
    {
        return $this->is_free == 0;
    }
    
    public function getMonthlyPriceAttribute($value)
    {
        return $this->is_free ? 0 : $value;
    }
    
    public function getAnnualPriceAttribute($value)
    {
        return $this->is_free ? 0 : $value;
    }
    
    public function getSavingsPercentage()
    {
        if ($this->is_free || $this->monthly_price == 0) {
            return 0;
        }
        
        return round((1 - ($this->annual_price / ($this->monthly_price * 12))) * 100);
    }
EOF

    print_success "Package model updated!"
}

create_freemium_config() {
    print_step "Creating freemium configuration..."
    
    cat > config/freemium.php << 'EOF'
<?php

return [
    
    /*
    |--------------------------------------------------------------------------
    | Freemium Mode
    |--------------------------------------------------------------------------
    |
    | Enable or disable freemium mode
    |
    */
    'enabled' => env('FREEMIUM_MODE', true),
    
    /*
    |--------------------------------------------------------------------------
    | Free Plan Limits
    |--------------------------------------------------------------------------
    |
    | Define the limits for the free plan
    |
    */
    'limits' => [
        'employees' => env('FREEMIUM_MAX_EMPLOYEES', 5),
        'projects' => env('FREEMIUM_MAX_PROJECTS', 3),
        'clients' => env('FREEMIUM_MAX_CLIENTS', 10),
        'storage_mb' => env('FREEMIUM_MAX_STORAGE_MB', 100),
        'file_size_mb' => env('FREEMIUM_MAX_FILE_SIZE_MB', 10),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Premium Features
    |--------------------------------------------------------------------------
    |
    | Features that require premium subscription
    |
    */
    'premium_features' => [
        'time_tracking' => true,
        'invoicing' => true,
        'advanced_reports' => true,
        'api_access' => true,
        'custom_fields' => true,
        'integrations' => true,
        'white_labeling' => true,
        'priority_support' => true,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Trial Period
    |--------------------------------------------------------------------------
    |
    | Number of days for premium trial
    |
    */
    'trial_days' => env('FREEMIUM_TRIAL_DAYS', 14),
    
    /*
    |--------------------------------------------------------------------------
    | Upgrade Messages
    |--------------------------------------------------------------------------
    |
    | Custom messages for different features
    |
    */
    'messages' => [
        'employees' => 'You have reached the maximum number of employees for the free plan. Upgrade to add more team members.',
        'projects' => 'You have reached the maximum number of projects for the free plan. Upgrade to create unlimited projects.',
        'clients' => 'You have reached the maximum number of clients for the free plan. Upgrade to manage unlimited clients.',
        'storage' => 'You have reached the storage limit for the free plan. Upgrade for more storage space.',
        'time_tracking' => 'Time tracking is a premium feature. Upgrade your plan to track time and productivity.',
        'invoicing' => 'Invoicing and billing features are available in premium plans. Upgrade to send professional invoices.',
        'advanced_reports' => 'Advanced reporting and analytics are premium features. Upgrade for detailed insights.',
        'api_access' => 'API access is available in premium plans. Upgrade to integrate with third-party applications.',
    ],
    
];
EOF

    print_success "Freemium configuration created!"
}

print_summary() {
    echo
    echo -e "${GREEN}"
    echo "=================================================="
    echo "    Freemium Customization Completed!"
    echo "=================================================="
    echo -e "${NC}"
    echo
    echo -e "${CYAN}What was created:${NC}"
    echo "  ✓ Freemium billing controller and routes"
    echo "  ✓ Professional billing interface"
    echo "  ✓ Feature-specific middleware"
    echo "  ✓ Freemium configuration file"
    echo "  ✓ Updated Package model"
    echo
    echo -e "${CYAN}Available Routes:${NC}"
    echo "  • /freemium/billing - Main billing page"
    echo "  • /freemium/billing/upgrade - Upgrade plans"
    echo "  • /freemium/billing/usage - Usage statistics"
    echo
    echo -e "${CYAN}Configuration:${NC}"
    echo "  • Edit config/freemium.php for limits"
    echo "  • Edit app/Helper/freemium.php for logic"
    echo "  • Customize views in resources/views/freemium/"
    echo
    echo -e "${CYAN}Next Steps:${NC}"
    echo "  1. Integrate payment gateway (Stripe, PayPal)"
    echo "  2. Customize billing views and branding"
    echo "  3. Set up email notifications"
    echo "  4. Configure webhooks for payments"
    echo
}

# Main execution
main() {
    print_header
    
    create_freemium_billing_controller
    create_freemium_routes
    create_freemium_views
    create_freemium_middleware
    update_package_model
    create_freemium_config
    print_summary
}

# Run main function
main "$@"
