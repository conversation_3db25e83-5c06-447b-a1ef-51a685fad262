#!/bin/bash

# WorkSuite SAAS Troubleshooting Script
# This script helps diagnose and fix common issues

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    WorkSuite SAAS Troubleshooting Script"
    echo "=================================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

check_docker_status() {
    print_step "Checking Docker containers status..."
    
    if ! docker-compose ps; then
        print_error "Docker Compose is not running or not configured properly"
        return 1
    fi
    
    # Check if all containers are running
    local down_containers=$(docker-compose ps --services --filter "status=exited")
    if [ ! -z "$down_containers" ]; then
        print_warning "Some containers are not running:"
        echo "$down_containers"
        return 1
    fi
    
    print_success "All Docker containers are running"
    return 0
}

check_application_health() {
    print_step "Checking application health..."
    
    # Check web application
    local web_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1080 || echo "000")
    if [[ "$web_status" =~ ^(200|302)$ ]]; then
        print_success "Web application is responding (HTTP $web_status)"
    else
        print_error "Web application is not responding (HTTP $web_status)"
        return 1
    fi
    
    # Check phpMyAdmin
    local pma_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1081 || echo "000")
    if [[ "$pma_status" == "200" ]]; then
        print_success "phpMyAdmin is responding"
    else
        print_warning "phpMyAdmin is not responding (HTTP $pma_status)"
    fi
    
    return 0
}

check_database_connection() {
    print_step "Checking database connection..."
    
    if docker-compose exec -T mysql mysql -uworksuite_user -pSecurePass123! -e "SELECT 1" >/dev/null 2>&1; then
        print_success "Database connection is working"
        return 0
    else
        print_error "Cannot connect to database"
        return 1
    fi
}

check_redis_connection() {
    print_step "Checking Redis connection..."
    
    if docker-compose exec -T redis redis-cli -a RedisPass123! ping >/dev/null 2>&1; then
        print_success "Redis connection is working"
        return 0
    else
        print_error "Cannot connect to Redis"
        return 1
    fi
}

check_file_permissions() {
    print_step "Checking file permissions..."
    
    local issues=0
    
    # Check storage directory
    if ! docker-compose exec -T app test -w storage; then
        print_error "Storage directory is not writable"
        issues=$((issues + 1))
    fi
    
    # Check bootstrap/cache directory
    if ! docker-compose exec -T app test -w bootstrap/cache; then
        print_error "Bootstrap cache directory is not writable"
        issues=$((issues + 1))
    fi
    
    if [ $issues -eq 0 ]; then
        print_success "File permissions are correct"
        return 0
    else
        print_error "Found $issues permission issues"
        return 1
    fi
}

fix_permissions() {
    print_step "Fixing file permissions..."
    
    docker-compose exec -T app chown -R worksuite:www-data storage bootstrap/cache
    docker-compose exec -T app chmod -R 775 storage bootstrap/cache
    
    print_success "File permissions fixed"
}

clear_application_cache() {
    print_step "Clearing application cache..."
    
    docker-compose exec -T app php artisan config:clear || true
    docker-compose exec -T app php artisan cache:clear || true
    docker-compose exec -T app php artisan route:clear || true
    docker-compose exec -T app php artisan view:clear || true
    
    print_success "Application cache cleared"
}

restart_services() {
    print_step "Restarting services..."
    
    docker-compose restart
    
    print_info "Waiting for services to start..."
    sleep 30
    
    print_success "Services restarted"
}

show_logs() {
    local service=$1
    print_step "Showing logs for $service..."
    
    if [ -z "$service" ]; then
        docker-compose logs --tail=50
    else
        docker-compose logs --tail=50 $service
    fi
}

check_environment_config() {
    print_step "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        print_error ".env file not found"
        return 1
    fi
    
    # Check required variables
    local required_vars=("DB_HOST" "DB_DATABASE" "DB_USERNAME" "DB_PASSWORD" "REDIS_HOST" "REDIS_PASSWORD")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" .env; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        print_error "Missing environment variables: ${missing_vars[*]}"
        return 1
    fi
    
    print_success "Environment configuration is complete"
    return 0
}

run_migrations() {
    print_step "Running database migrations..."
    
    if docker-compose exec -T app php artisan migrate --force --no-interaction; then
        print_success "Migrations completed successfully"
        return 0
    else
        print_error "Migrations failed"
        return 1
    fi
}

generate_app_key() {
    print_step "Generating application key..."
    
    if ! grep -q "APP_KEY=base64:" .env; then
        docker-compose exec -T app php artisan key:generate --no-interaction
        print_success "Application key generated"
    else
        print_info "Application key already exists"
    fi
}

show_system_info() {
    print_step "System Information..."
    
    echo "Docker version:"
    docker --version
    echo
    
    echo "Docker Compose version:"
    docker-compose --version
    echo
    
    echo "Container status:"
    docker-compose ps
    echo
    
    echo "Resource usage:"
    docker stats --no-stream
    echo
}

interactive_menu() {
    while true; do
        echo
        echo -e "${CYAN}WorkSuite SAAS Troubleshooting Menu:${NC}"
        echo "1. Check all systems"
        echo "2. Check Docker containers"
        echo "3. Check application health"
        echo "4. Check database connection"
        echo "5. Check Redis connection"
        echo "6. Check file permissions"
        echo "7. Fix file permissions"
        echo "8. Clear application cache"
        echo "9. Restart services"
        echo "10. Show logs (all services)"
        echo "11. Show logs (specific service)"
        echo "12. Run migrations"
        echo "13. Generate app key"
        echo "14. Show system info"
        echo "15. Exit"
        echo
        read -p "Select an option (1-15): " choice
        
        case $choice in
            1)
                check_all_systems
                ;;
            2)
                check_docker_status
                ;;
            3)
                check_application_health
                ;;
            4)
                check_database_connection
                ;;
            5)
                check_redis_connection
                ;;
            6)
                check_file_permissions
                ;;
            7)
                fix_permissions
                ;;
            8)
                clear_application_cache
                ;;
            9)
                restart_services
                ;;
            10)
                show_logs
                ;;
            11)
                read -p "Enter service name (app/nginx/mysql/redis/queue/scheduler): " service
                show_logs $service
                ;;
            12)
                run_migrations
                ;;
            13)
                generate_app_key
                ;;
            14)
                show_system_info
                ;;
            15)
                echo "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please try again."
                ;;
        esac
    done
}

check_all_systems() {
    print_step "Running comprehensive system check..."
    
    local issues=0
    
    check_docker_status || issues=$((issues + 1))
    check_environment_config || issues=$((issues + 1))
    check_database_connection || issues=$((issues + 1))
    check_redis_connection || issues=$((issues + 1))
    check_file_permissions || issues=$((issues + 1))
    check_application_health || issues=$((issues + 1))
    
    echo
    if [ $issues -eq 0 ]; then
        print_success "All systems are working correctly!"
    else
        print_warning "Found $issues issues. Use the menu options to fix them."
    fi
}

auto_fix() {
    print_step "Running automatic fixes..."
    
    fix_permissions
    clear_application_cache
    generate_app_key
    
    print_info "Checking if restart is needed..."
    if ! check_application_health >/dev/null 2>&1; then
        restart_services
    fi
    
    print_success "Automatic fixes completed"
}

# Main execution
main() {
    print_header
    
    if [ "$1" == "--auto-fix" ]; then
        auto_fix
    elif [ "$1" == "--check" ]; then
        check_all_systems
    elif [ "$1" == "--logs" ]; then
        show_logs $2
    else
        interactive_menu
    fi
}

# Show usage if --help
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    echo "Usage: $0 [option]"
    echo
    echo "Options:"
    echo "  --check      Run comprehensive system check"
    echo "  --auto-fix   Run automatic fixes"
    echo "  --logs [service]  Show logs for all services or specific service"
    echo "  --help       Show this help message"
    echo
    echo "If no option is provided, interactive menu will be shown."
    exit 0
fi

# Run main function
main "$@"
