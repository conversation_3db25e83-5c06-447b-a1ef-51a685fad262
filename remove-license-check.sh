#!/bin/bash

# Script để loại bỏ kiểm tra bản quyền WorkSuite SAAS
# Cho phép sử dụng miễn phí trước khi thu tiền

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    Remove License Check - WorkSuite SAAS"
    echo "    Freemium Model Implementation"
    echo "=================================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

backup_files() {
    print_step "Creating backup of original files..."
    
    mkdir -p backups/license-removal
    
    # Backup important files
    [ -f "app/Http/Controllers/Controller.php" ] && cp "app/Http/Controllers/Controller.php" "backups/license-removal/"
    [ -f "app/Providers/FortifyServiceProvider.php" ] && cp "app/Providers/FortifyServiceProvider.php" "backups/license-removal/"
    [ -f "app/Http/Controllers/SuperAdmin/FrontendController.php" ] && cp "app/Http/Controllers/SuperAdmin/FrontendController.php" "backups/license-removal/"
    [ -f "app/Http/Middleware/CheckCompanyPackage.php" ] && cp "app/Http/Middleware/CheckCompanyPackage.php" "backups/license-removal/"
    [ -f "config/froiden_envato.php" ] && cp "config/froiden_envato.php" "backups/license-removal/"
    
    print_success "Backup completed!"
}

create_custom_appboot_trait() {
    print_step "Creating custom AppBoot trait..."
    
    mkdir -p app/Traits
    
    cat > app/Traits/CustomAppBoot.php << 'EOF'
<?php

namespace App\Traits;

trait CustomAppBoot
{
    /**
     * Always return true for license check (freemium model)
     */
    public function isLegal()
    {
        return true;
    }

    /**
     * Always return true for module purchase verification
     */
    public function modulePurchaseVerified($module, $purchaseCode)
    {
        return [
            'status' => 'success',
            'message' => 'Module verified successfully (freemium mode)'
        ];
    }

    /**
     * Show install message if needed
     */
    public function showInstall()
    {
        if (!file_exists(public_path() . '/install-version.txt')) {
            return redirect('install');
        }
    }
}
EOF
    
    print_success "Custom AppBoot trait created!"
}

modify_controller() {
    print_step "Modifying main Controller class..."
    
    # Replace the AppBoot trait import
    sed -i.bak 's/use Froiden\\Envato\\Traits\\AppBoot;/use App\\Traits\\CustomAppBoot as AppBoot;/' app/Http/Controllers/Controller.php
    
    # Remove backup file
    rm -f app/Http/Controllers/Controller.php.bak
    
    print_success "Controller modified!"
}

modify_fortify_service_provider() {
    print_step "Modifying FortifyServiceProvider..."
    
    # Comment out the license check in login view
    sed -i.bak 's/if (!$this->isLegal()) {/\/\/ License check disabled for freemium model\n        if (false) {/' app/Providers/FortifyServiceProvider.php
    
    # Remove backup file
    rm -f app/Providers/FortifyServiceProvider.php.bak
    
    print_success "FortifyServiceProvider modified!"
}

modify_frontend_controller() {
    print_step "Modifying FrontendController..."
    
    # Comment out the license check in loadLoginPage
    sed -i.bak 's/if (!$this->isLegal()) {/\/\/ License check disabled for freemium model\n        if (false) {/' app/Http/Controllers/SuperAdmin/FrontendController.php
    
    # Remove backup file
    rm -f app/Http/Controllers/SuperAdmin/FrontendController.php.bak
    
    print_success "FrontendController modified!"
}

modify_package_middleware() {
    print_step "Modifying package check middleware for freemium model..."
    
    # Create a more lenient package check
    cat > app/Http/Middleware/CheckCompanyPackageFreemium.php << 'EOF'
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckCompanyPackageFreemium
{
    /**
     * Handle an incoming request for freemium model.
     * Allow basic usage but redirect to billing for premium features.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Allow basic routes always
        $alwaysAllowedRoutes = [
            'dashboard',
            'employees.index',
            'employees.edit',
            'employees.update',
            'employees.destroy',
            'employees.apply_quick_action',
            'import.process.progress',
            'import.process.exception',
            'profile.dark_theme',
            'billing.index',
            'billing.upgrade_plan',
        ];

        if (user() && user()->company_id && !$request->routeIs($alwaysAllowedRoutes)) {
            $isAllowedInCurrentPackage = checkCompanyPackageIsValid(user()->company_id);

            // For freemium model: show upgrade notice instead of blocking
            if (!$isAllowedInCurrentPackage) {
                // Store the intended URL for after upgrade
                session(['intended_url' => $request->url()]);
                
                if(in_array('admin', user_roles())) {
                    // Show upgrade notice instead of hard redirect
                    if ($request->ajax()) {
                        return response()->json([
                            'status' => 'upgrade_required',
                            'message' => 'Please upgrade your plan to access this feature.',
                            'redirect_url' => route('billing.index')
                        ]);
                    }
                    return redirect()->route('billing.index')->with('upgrade_notice', 'Please upgrade your plan to access this feature.');
                }

                return redirect()->route('superadmin.notify.admin');
            }
        }

        // Premium features that require upgrade
        $premiumRoutes = [
            'employees.create',
            'employees.store',
            'employees.import',
            'employees.import.store',
            'employees.send_invite',
            'employees.create_link',
        ];

        if (user() && user()->company_id && $request->routeIs($premiumRoutes)) {
            // Check if company can add more employees (freemium limit)
            $canAddMore = checkCompanyCanAddMoreEmployees(user()->company_id);
            
            if (!$canAddMore) {
                if ($request->ajax()) {
                    return response()->json([
                        'status' => 'upgrade_required',
                        'message' => 'You have reached the limit for your current plan. Please upgrade to add more employees.',
                        'redirect_url' => route('billing.upgrade_plan')
                    ]);
                }
                return redirect()->route('billing.upgrade_plan')->with('upgrade_notice', 'You have reached the limit for your current plan. Please upgrade to add more employees.');
            }
        }

        return $next($request);
    }
}
EOF

    print_success "Freemium package middleware created!"
}

update_kernel_middleware() {
    print_step "Updating middleware registration..."
    
    # Add the new freemium middleware to Kernel.php
    sed -i.bak "s/'check-company-package' => \\\\App\\\\Http\\\\Middleware\\\\CheckCompanyPackage::class,/'check-company-package' => \\\\App\\\\Http\\\\Middleware\\\\CheckCompanyPackageFreemium::class,/" app/Http/Kernel.php
    
    # Remove backup file
    rm -f app/Http/Kernel.php.bak
    
    print_success "Middleware registration updated!"
}

create_freemium_helper() {
    print_step "Creating freemium helper functions..."
    
    cat > app/Helper/freemium.php << 'EOF'
<?php

if (!function_exists('isFreemiumMode')) {
    function isFreemiumMode()
    {
        return true; // Always in freemium mode
    }
}

if (!function_exists('getFreemiumLimits')) {
    function getFreemiumLimits()
    {
        return [
            'max_employees' => 5,
            'max_projects' => 3,
            'max_clients' => 10,
            'max_storage_mb' => 100,
            'features' => [
                'basic_crm' => true,
                'task_management' => true,
                'time_tracking' => false,
                'invoicing' => false,
                'reports' => false,
                'api_access' => false,
            ]
        ];
    }
}

if (!function_exists('checkFreemiumLimit')) {
    function checkFreemiumLimit($feature, $currentCount = 0)
    {
        if (!isFreemiumMode()) {
            return true;
        }
        
        $limits = getFreemiumLimits();
        
        switch ($feature) {
            case 'employees':
                return $currentCount < $limits['max_employees'];
            case 'projects':
                return $currentCount < $limits['max_projects'];
            case 'clients':
                return $currentCount < $limits['max_clients'];
            case 'storage':
                return $currentCount < $limits['max_storage_mb'];
            default:
                return isset($limits['features'][$feature]) ? $limits['features'][$feature] : false;
        }
    }
}

if (!function_exists('getUpgradeMessage')) {
    function getUpgradeMessage($feature)
    {
        $messages = [
            'employees' => 'You have reached the maximum number of employees (5) for the free plan. Upgrade to add more employees.',
            'projects' => 'You have reached the maximum number of projects (3) for the free plan. Upgrade to create more projects.',
            'clients' => 'You have reached the maximum number of clients (10) for the free plan. Upgrade to add more clients.',
            'storage' => 'You have reached the storage limit (100MB) for the free plan. Upgrade for more storage.',
            'time_tracking' => 'Time tracking is a premium feature. Upgrade your plan to access this feature.',
            'invoicing' => 'Invoicing is a premium feature. Upgrade your plan to access this feature.',
            'reports' => 'Advanced reports are a premium feature. Upgrade your plan to access this feature.',
            'api_access' => 'API access is a premium feature. Upgrade your plan to access this feature.',
        ];
        
        return $messages[$feature] ?? 'This is a premium feature. Please upgrade your plan.';
    }
}
EOF

    print_success "Freemium helper functions created!"
}

update_composer_autoload() {
    print_step "Updating composer autoload..."
    
    # Add the freemium helper to composer.json autoload files
    if grep -q "app/Helper/freemium.php" composer.json; then
        print_info "Freemium helper already in composer.json"
    else
        # Add to autoload files array
        sed -i.bak 's/"files": \[/"files": [\n            "app\/Helper\/freemium.php",/' composer.json
        rm -f composer.json.bak
    fi
    
    print_success "Composer autoload updated!"
}

create_freemium_seeder() {
    print_step "Creating freemium package seeder..."
    
    cat > database/seeders/FreemiumPackageSeeder.php << 'EOF'
<?php

namespace Database\Seeders;

use App\Models\Package;
use App\Models\Currency;
use Illuminate\Database\Seeder;

class FreemiumPackageSeeder extends Seeder
{
    public function run()
    {
        $currency = Currency::where('currency_code', 'USD')->first();
        
        if (!$currency) {
            $currency = Currency::create([
                'currency_name' => 'US Dollar',
                'currency_symbol' => '$',
                'currency_code' => 'USD',
                'exchange_rate' => 1,
                'is_cryptocurrency' => 'no',
                'usd_price' => 1,
            ]);
        }

        // Create free package
        Package::updateOrCreate(
            ['name' => 'Free Plan'],
            [
                'description' => 'Perfect for small teams getting started',
                'annual_price' => 0,
                'monthly_price' => 0,
                'currency_id' => $currency->id,
                'max_employees' => 5,
                'max_file_size' => 10, // MB
                'max_storage_size' => 100, // MB
                'storage_unit' => 'mb',
                'sort' => 1,
                'default' => 'yes',
                'is_free' => 1,
                'is_recommended' => 0,
                'module_in_package' => json_encode([
                    'employees', 'clients', 'projects', 'tasks', 'timelogs', 'leads'
                ]),
            ]
        );

        // Create starter package
        Package::updateOrCreate(
            ['name' => 'Starter Plan'],
            [
                'description' => 'Great for growing teams',
                'annual_price' => 120,
                'monthly_price' => 12,
                'currency_id' => $currency->id,
                'max_employees' => 25,
                'max_file_size' => 50, // MB
                'max_storage_size' => 1, // GB
                'storage_unit' => 'gb',
                'sort' => 2,
                'default' => 'no',
                'is_free' => 0,
                'is_recommended' => 1,
                'module_in_package' => json_encode([
                    'employees', 'clients', 'projects', 'tasks', 'timelogs', 'leads',
                    'invoices', 'payments', 'expenses', 'reports'
                ]),
            ]
        );

        // Create professional package
        Package::updateOrCreate(
            ['name' => 'Professional Plan'],
            [
                'description' => 'Perfect for established businesses',
                'annual_price' => 240,
                'monthly_price' => 24,
                'currency_id' => $currency->id,
                'max_employees' => 100,
                'max_file_size' => 100, // MB
                'max_storage_size' => 5, // GB
                'storage_unit' => 'gb',
                'sort' => 3,
                'default' => 'no',
                'is_free' => 0,
                'is_recommended' => 0,
                'module_in_package' => json_encode([
                    'employees', 'clients', 'projects', 'tasks', 'timelogs', 'leads',
                    'invoices', 'payments', 'expenses', 'reports', 'contracts',
                    'estimates', 'products', 'orders'
                ]),
            ]
        );
    }
}
EOF

    print_success "Freemium package seeder created!"
}

create_migration_script() {
    print_step "Creating migration script for freemium setup..."
    
    cat > setup-freemium-db.sh << 'EOF'
#!/bin/bash

echo "Setting up freemium database..."

# Run the freemium seeder
docker-compose exec app php artisan db:seed --class=FreemiumPackageSeeder

# Update existing companies to use free package
docker-compose exec app php artisan tinker --execute="
\App\Models\Company::whereNull('package_id')->update(['package_id' => \App\Models\Package::where('is_free', 1)->first()->id]);
echo 'Companies updated to free package';
"

echo "Freemium database setup completed!"
EOF

    chmod +x setup-freemium-db.sh
    
    print_success "Migration script created!"
}

update_env_example() {
    print_step "Updating .env.example for freemium mode..."
    
    if [ -f ".env.example" ]; then
        # Add freemium configuration
        if ! grep -q "FREEMIUM_MODE" .env.example; then
            echo "" >> .env.example
            echo "# Freemium Configuration" >> .env.example
            echo "FREEMIUM_MODE=true" >> .env.example
            echo "FREEMIUM_MAX_EMPLOYEES=5" >> .env.example
            echo "FREEMIUM_MAX_PROJECTS=3" >> .env.example
            echo "FREEMIUM_MAX_CLIENTS=10" >> .env.example
            echo "FREEMIUM_MAX_STORAGE_MB=100" >> .env.example
        fi
    fi
    
    print_success ".env.example updated!"
}

create_freemium_views() {
    print_step "Creating freemium upgrade notice views..."
    
    mkdir -p resources/views/freemium
    
    cat > resources/views/freemium/upgrade-notice.blade.php << 'EOF'
<div class="alert alert-info alert-dismissible fade show" role="alert">
    <i class="fa fa-info-circle"></i>
    <strong>Upgrade Required!</strong> {{ $message ?? 'This feature requires a premium plan.' }}
    <a href="{{ route('billing.upgrade_plan') }}" class="btn btn-sm btn-primary ml-2">
        <i class="fa fa-arrow-up"></i> Upgrade Now
    </a>
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
EOF

    cat > resources/views/freemium/feature-locked.blade.php << 'EOF'
<div class="card border-warning">
    <div class="card-body text-center">
        <i class="fa fa-lock fa-3x text-warning mb-3"></i>
        <h4>Premium Feature</h4>
        <p class="text-muted">{{ $message ?? 'This feature is available in our premium plans.' }}</p>
        <a href="{{ route('billing.upgrade_plan') }}" class="btn btn-warning">
            <i class="fa fa-arrow-up"></i> Upgrade to Access
        </a>
    </div>
</div>
EOF

    print_success "Freemium views created!"
}

run_composer_dump() {
    print_step "Running composer dump-autoload..."
    
    if command -v composer &> /dev/null; then
        composer dump-autoload
        print_success "Composer autoload regenerated!"
    else
        print_warning "Composer not found. Please run 'composer dump-autoload' manually."
    fi
}

print_summary() {
    echo
    echo -e "${GREEN}"
    echo "=================================================="
    echo "    License Check Removal Completed!"
    echo "    Freemium Model Implemented"
    echo "=================================================="
    echo -e "${NC}"
    echo
    echo -e "${CYAN}What was changed:${NC}"
    echo "  ✓ Replaced license verification with freemium model"
    echo "  ✓ Created custom AppBoot trait (no license check)"
    echo "  ✓ Modified package check middleware for freemium limits"
    echo "  ✓ Added freemium helper functions"
    echo "  ✓ Created freemium package seeder"
    echo "  ✓ Added upgrade notice views"
    echo
    echo -e "${CYAN}Freemium Limits:${NC}"
    echo "  • Max Employees: 5"
    echo "  • Max Projects: 3"
    echo "  • Max Clients: 10"
    echo "  • Max Storage: 100MB"
    echo "  • Premium features locked behind paywall"
    echo
    echo -e "${CYAN}Next Steps:${NC}"
    echo "  1. Run: ./setup-freemium-db.sh"
    echo "  2. Restart your application"
    echo "  3. Test the freemium functionality"
    echo "  4. Customize limits in app/Helper/freemium.php"
    echo
    echo -e "${YELLOW}Note:${NC} Original files backed up in backups/license-removal/"
    echo
}

# Main execution
main() {
    print_header
    
    # Check if we're in the right directory
    if [ ! -f "app/Http/Controllers/Controller.php" ]; then
        print_error "Please run this script from the Laravel project root directory"
        exit 1
    fi
    
    backup_files
    create_custom_appboot_trait
    modify_controller
    modify_fortify_service_provider
    modify_frontend_controller
    modify_package_middleware
    update_kernel_middleware
    create_freemium_helper
    update_composer_autoload
    create_freemium_seeder
    create_migration_script
    update_env_example
    create_freemium_views
    run_composer_dump
    print_summary
}

# Run main function
main "$@"
