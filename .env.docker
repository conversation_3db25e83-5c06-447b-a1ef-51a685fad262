# WorkSuite SAAS Docker Environment Configuration
# Generated on: $(date)

# Database Configuration
DB_DATABASE=worksuite_saas
DB_USERNAME=worksuite_user
DB_PASSWORD=WS_SecurePass_2024!
DB_ROOT_PASSWORD=WS_RootPass_2024!

# Redis Configuration
REDIS_PASSWORD=WS_RedisPass_2024!

# Application Configuration
APP_NAME=WORKSUITE-SAAS-DOCKER
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:1080

# Security Keys (Generate new ones for production)
APP_KEY=base64:$(openssl rand -base64 32)

# Mail Configuration (Update with your SMTP settings)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Cache and Session Configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120
QUEUE_CONNECTION=redis

# File Storage Configuration
FILESYSTEM_DISK=local

# Logging Configuration
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Broadcasting Configuration
BROADCAST_DRIVER=log
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Additional Security Settings
SANCTUM_STATEFUL_DOMAINS=localhost:1080
SESSION_DOMAIN=localhost
