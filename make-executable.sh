#!/bin/bash

# Script to make all bash scripts executable
# Run this after downloading/cloning the repository

echo "Making all scripts executable..."

# Main scripts
chmod +x worksuite.sh
chmod +x setup.sh
chmod +x create-docker-configs.sh
chmod +x troubleshoot.sh
chmod +x maintenance.sh

# Utility scripts (if they exist)
[ -f "docker-start.sh" ] && chmod +x docker-start.sh
[ -f "docker-stop.sh" ] && chmod +x docker-stop.sh
[ -f "docker-logs.sh" ] && chmod +x docker-logs.sh
[ -f "docker-backup.sh" ] && chmod +x docker-backup.sh

# Docker entrypoint (if it exists)
[ -f "docker/entrypoint.sh" ] && chmod +x docker/entrypoint.sh

echo "All scripts are now executable!"
echo
echo "Quick start:"
echo "  ./worksuite.sh install    # Install the system"
echo "  ./worksuite.sh            # Interactive menu"
echo
