#!/bin/bash

# WorkSuite SAAS Docker Startup Script
# This script helps you start the WorkSuite SAAS application with Docker Compose

set -e

echo "🚀 Starting WorkSuite SAAS with Docker..."

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file from .env.docker if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.docker template..."
    cp .env.docker .env
    
    # Generate secure random passwords
    DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    DB_ROOT_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    APP_KEY="base64:$(openssl rand -base64 32)"
    
    # Update passwords in .env file
    sed -i.bak "s/DB_PASSWORD=WS_SecurePass_2024!/DB_PASSWORD=${DB_PASSWORD}/" .env
    sed -i.bak "s/DB_ROOT_PASSWORD=WS_RootPass_2024!/DB_ROOT_PASSWORD=${DB_ROOT_PASSWORD}/" .env
    sed -i.bak "s/REDIS_PASSWORD=WS_RedisPass_2024!/REDIS_PASSWORD=${REDIS_PASSWORD}/" .env
    sed -i.bak "s|APP_KEY=base64:.*|APP_KEY=${APP_KEY}|" .env
    
    # Remove backup file
    rm -f .env.bak
    
    echo "✅ Generated secure passwords and saved to .env file"
    echo "📋 Database Credentials:"
    echo "   Database: worksuite_saas"
    echo "   Username: worksuite_user"
    echo "   Password: ${DB_PASSWORD}"
    echo "   Root Password: ${DB_ROOT_PASSWORD}"
    echo ""
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p docker/nginx/ssl
mkdir -p docker/mysql/init
mkdir -p script/storage/logs

# Set executable permissions for entrypoint script
chmod +x docker/entrypoint.sh

# Build and start containers
echo "🔨 Building and starting Docker containers..."
if command -v docker-compose &> /dev/null; then
    docker-compose up -d --build
else
    docker compose up -d --build
fi

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Show status
echo "📊 Container Status:"
if command -v docker-compose &> /dev/null; then
    docker-compose ps
else
    docker compose ps
fi

echo ""
echo "🎉 WorkSuite SAAS is now running!"
echo ""
echo "📱 Access URLs:"
echo "   🌐 Application: http://localhost:1080"
echo "   🗄️  PhpMyAdmin: http://localhost:1081"
echo "   🔧 MySQL Port: 1306"
echo "   📦 Redis Port: 1379"
echo ""
echo "🔐 Database Connection (from host):"
echo "   Host: localhost"
echo "   Port: 1306"
echo "   Database: worksuite_saas"
echo "   Username: worksuite_user"
echo "   Password: (check .env file)"
echo ""
echo "📝 To view logs:"
if command -v docker-compose &> /dev/null; then
    echo "   docker-compose logs -f"
else
    echo "   docker compose logs -f"
fi
echo ""
echo "🛑 To stop:"
if command -v docker-compose &> /dev/null; then
    echo "   docker-compose down"
else
    echo "   docker compose down"
fi
