#!/bin/bash

# WorkSuite SAAS Maintenance Script
# This script provides maintenance and update functionality

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)

print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    WorkSuite SAAS Maintenance Script"
    echo "=================================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

create_backup() {
    print_step "Creating backup..."
    
    mkdir -p $BACKUP_DIR
    
    # Database backup
    print_info "Backing up database..."
    if docker-compose exec -T mysql mysqldump -uworksuite_user -pSecurePass123! worksuite_saas > $BACKUP_DIR/database_$DATE.sql; then
        print_success "Database backup created: $BACKUP_DIR/database_$DATE.sql"
    else
        print_error "Database backup failed"
        return 1
    fi
    
    # Files backup
    print_info "Backing up files..."
    if tar -czf $BACKUP_DIR/files_$DATE.tar.gz storage/ public/uploads/ .env 2>/dev/null; then
        print_success "Files backup created: $BACKUP_DIR/files_$DATE.tar.gz"
    else
        print_warning "Files backup completed with warnings"
    fi
    
    # Docker volumes backup
    print_info "Backing up Docker volumes..."
    docker run --rm -v worksuite-saas-new-5492_mysql_data:/data -v $(pwd)/$BACKUP_DIR:/backup alpine tar czf /backup/mysql_volume_$DATE.tar.gz -C /data .
    docker run --rm -v worksuite-saas-new-5492_redis_data:/data -v $(pwd)/$BACKUP_DIR:/backup alpine tar czf /backup/redis_volume_$DATE.tar.gz -C /data .
    
    print_success "Backup completed successfully!"
    
    # List backups
    echo
    print_info "Available backups:"
    ls -la $BACKUP_DIR/
}

restore_backup() {
    local backup_date=$1
    
    if [ -z "$backup_date" ]; then
        print_error "Please specify backup date (format: YYYYMMDD_HHMMSS)"
        echo "Available backups:"
        ls -la $BACKUP_DIR/ | grep -E "(database_|files_)" | awk '{print $9}' | sed 's/.*_\([0-9_]*\)\..*/\1/' | sort -u
        return 1
    fi
    
    print_step "Restoring backup from $backup_date..."
    
    # Confirm restoration
    read -p "This will overwrite current data. Are you sure? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        print_info "Restoration cancelled"
        return 0
    fi
    
    # Stop services
    print_info "Stopping services..."
    docker-compose down
    
    # Restore database
    if [ -f "$BACKUP_DIR/database_$backup_date.sql" ]; then
        print_info "Restoring database..."
        docker-compose up -d mysql
        sleep 30
        docker-compose exec -T mysql mysql -uworksuite_user -pSecurePass123! worksuite_saas < $BACKUP_DIR/database_$backup_date.sql
        print_success "Database restored"
    else
        print_warning "Database backup not found: $BACKUP_DIR/database_$backup_date.sql"
    fi
    
    # Restore files
    if [ -f "$BACKUP_DIR/files_$backup_date.tar.gz" ]; then
        print_info "Restoring files..."
        tar -xzf $BACKUP_DIR/files_$backup_date.tar.gz
        print_success "Files restored"
    else
        print_warning "Files backup not found: $BACKUP_DIR/files_$backup_date.tar.gz"
    fi
    
    # Start all services
    print_info "Starting all services..."
    docker-compose up -d
    
    print_success "Restoration completed!"
}

update_application() {
    print_step "Updating application..."
    
    # Create backup before update
    print_info "Creating backup before update..."
    create_backup
    
    # Pull latest code (if using git)
    if [ -d ".git" ]; then
        print_info "Pulling latest code..."
        git pull
    else
        print_warning "Not a git repository. Please update code manually."
    fi
    
    # Rebuild containers
    print_info "Rebuilding containers..."
    docker-compose build --no-cache
    
    # Stop services
    print_info "Stopping services..."
    docker-compose down
    
    # Start services
    print_info "Starting services..."
    docker-compose up -d
    
    # Wait for services
    print_info "Waiting for services to start..."
    sleep 30
    
    # Run migrations
    print_info "Running migrations..."
    docker-compose exec -T app php artisan migrate --force --no-interaction
    
    # Clear cache
    print_info "Clearing cache..."
    docker-compose exec -T app php artisan config:clear
    docker-compose exec -T app php artisan cache:clear
    docker-compose exec -T app php artisan route:clear
    docker-compose exec -T app php artisan view:clear
    
    # Set permissions
    print_info "Setting permissions..."
    docker-compose exec -T app chown -R worksuite:www-data storage bootstrap/cache
    docker-compose exec -T app chmod -R 775 storage bootstrap/cache
    
    print_success "Application updated successfully!"
}

optimize_application() {
    print_step "Optimizing application..."
    
    # Cache configuration
    print_info "Caching configuration..."
    docker-compose exec -T app php artisan config:cache
    
    # Cache routes
    print_info "Caching routes..."
    docker-compose exec -T app php artisan route:cache
    
    # Cache views
    print_info "Caching views..."
    docker-compose exec -T app php artisan view:cache
    
    # Optimize autoloader
    print_info "Optimizing autoloader..."
    docker-compose exec -T app composer dump-autoload --optimize
    
    print_success "Application optimized!"
}

clean_system() {
    print_step "Cleaning system..."
    
    # Clean Docker
    print_info "Cleaning Docker images and containers..."
    docker system prune -f
    
    # Clean application cache
    print_info "Cleaning application cache..."
    docker-compose exec -T app php artisan cache:clear
    docker-compose exec -T app php artisan config:clear
    docker-compose exec -T app php artisan route:clear
    docker-compose exec -T app php artisan view:clear
    
    # Clean logs
    print_info "Cleaning old logs..."
    docker-compose exec -T app find storage/logs -name "*.log" -mtime +30 -delete 2>/dev/null || true
    
    # Clean old backups
    print_info "Cleaning old backups (older than 30 days)..."
    find $BACKUP_DIR -name "*.sql" -mtime +30 -delete 2>/dev/null || true
    find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete 2>/dev/null || true
    
    print_success "System cleaned!"
}

monitor_resources() {
    print_step "Monitoring system resources..."
    
    echo "Docker container stats:"
    docker stats --no-stream
    echo
    
    echo "Disk usage:"
    df -h
    echo
    
    echo "Memory usage:"
    free -h
    echo
    
    echo "Docker images:"
    docker images
    echo
    
    echo "Docker volumes:"
    docker volume ls
    echo
}

check_security() {
    print_step "Checking security configuration..."
    
    local issues=0
    
    # Check .env file permissions
    if [ -f ".env" ]; then
        local env_perms=$(stat -c "%a" .env)
        if [ "$env_perms" != "600" ] && [ "$env_perms" != "644" ]; then
            print_warning ".env file permissions are too open: $env_perms"
            issues=$((issues + 1))
        fi
    fi
    
    # Check for default passwords
    if grep -q "SecurePass123!" .env; then
        print_warning "Using default database password. Consider changing it."
        issues=$((issues + 1))
    fi
    
    if grep -q "RedisPass123!" .env; then
        print_warning "Using default Redis password. Consider changing it."
        issues=$((issues + 1))
    fi
    
    # Check SSL configuration
    if ! grep -q "HTTPS" .env; then
        print_warning "HTTPS not configured. Consider enabling SSL in production."
        issues=$((issues + 1))
    fi
    
    if [ $issues -eq 0 ]; then
        print_success "Security configuration looks good!"
    else
        print_warning "Found $issues security issues to review."
    fi
}

interactive_menu() {
    while true; do
        echo
        echo -e "${CYAN}WorkSuite SAAS Maintenance Menu:${NC}"
        echo "1. Create backup"
        echo "2. Restore backup"
        echo "3. Update application"
        echo "4. Optimize application"
        echo "5. Clean system"
        echo "6. Monitor resources"
        echo "7. Check security"
        echo "8. View backup list"
        echo "9. Exit"
        echo
        read -p "Select an option (1-9): " choice
        
        case $choice in
            1)
                create_backup
                ;;
            2)
                read -p "Enter backup date (YYYYMMDD_HHMMSS): " backup_date
                restore_backup $backup_date
                ;;
            3)
                update_application
                ;;
            4)
                optimize_application
                ;;
            5)
                clean_system
                ;;
            6)
                monitor_resources
                ;;
            7)
                check_security
                ;;
            8)
                if [ -d "$BACKUP_DIR" ]; then
                    echo "Available backups:"
                    ls -la $BACKUP_DIR/
                else
                    print_info "No backups found"
                fi
                ;;
            9)
                echo "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please try again."
                ;;
        esac
    done
}

# Main execution
main() {
    print_header
    
    case "$1" in
        "backup")
            create_backup
            ;;
        "restore")
            restore_backup $2
            ;;
        "update")
            update_application
            ;;
        "optimize")
            optimize_application
            ;;
        "clean")
            clean_system
            ;;
        "monitor")
            monitor_resources
            ;;
        "security")
            check_security
            ;;
        *)
            interactive_menu
            ;;
    esac
}

# Show usage if --help
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    echo "Usage: $0 [command] [options]"
    echo
    echo "Commands:"
    echo "  backup           Create backup of database and files"
    echo "  restore [date]   Restore backup from specific date"
    echo "  update           Update application and rebuild containers"
    echo "  optimize         Optimize application performance"
    echo "  clean            Clean system and remove old files"
    echo "  monitor          Monitor system resources"
    echo "  security         Check security configuration"
    echo
    echo "If no command is provided, interactive menu will be shown."
    exit 0
fi

# Run main function
main "$@"
