[mysqld]
# Basic settings
default-authentication-plugin=mysql_native_password
skip-name-resolve
explicit_defaults_for_timestamp=1

# Connection settings
max_connections=200
max_connect_errors=1000
wait_timeout=28800
interactive_timeout=28800

# Buffer settings
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_log_buffer_size=16M
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT

# Query cache (disabled in MySQL 8.0)
# query_cache_type=1
# query_cache_size=32M
# query_cache_limit=2M

# Temporary tables
tmp_table_size=64M
max_heap_table_size=64M

# MyISAM settings
key_buffer_size=32M
myisam_sort_buffer_size=8M

# Logging
general_log=0
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# Character set
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# SQL Mode
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Binary logging
server-id=1
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
