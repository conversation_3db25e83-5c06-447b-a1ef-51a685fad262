version: '3.8'

services:
  # PHP-FPM Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: worksuite_app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./script:/var/www/html
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - worksuite_network
    depends_on:
      - mysql
      - redis
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379

  # Nginx Service
  nginx:
    image: nginx:alpine
    container_name: worksuite_nginx
    restart: unless-stopped
    ports:
      - "1080:80"
      - "1443:443"
    volumes:
      - ./script:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - worksuite_network
    depends_on:
      - app

  # MySQL Service
  mysql:
    image: mysql:8.0
    container_name: worksuite_mysql
    restart: unless-stopped
    ports:
      - "1306:3306"
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - worksuite_network
    command: --default-authentication-plugin=mysql_native_password

  # Redis Service
  redis:
    image: redis:7-alpine
    container_name: worksuite_redis
    restart: unless-stopped
    ports:
      - "1379:6379"
    volumes:
      - redis_data:/data
    networks:
      - worksuite_network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}

  # PhpMyAdmin Service (Optional)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: worksuite_phpmyadmin
    restart: unless-stopped
    ports:
      - "1081:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${DB_USERNAME}
      PMA_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    networks:
      - worksuite_network
    depends_on:
      - mysql

  # Queue Worker Service
  queue:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: worksuite_queue
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./script:/var/www/html
    networks:
      - worksuite_network
    depends_on:
      - mysql
      - redis
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    command: php artisan queue:work --sleep=3 --tries=3

  # Scheduler Service
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: worksuite_scheduler
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./script:/var/www/html
    networks:
      - worksuite_network
    depends_on:
      - mysql
      - redis
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    command: /bin/sh -c "while true; do php artisan schedule:run; sleep 60; done"

# Docker Networks
networks:
  worksuite_network:
    driver: bridge

# Volumes
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
