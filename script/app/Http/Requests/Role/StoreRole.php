<?php

namespace App\Http\Requests\Role;

use App\Http\Requests\CoreRequest;

class StoreRole extends CoreRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|unique:roles,name,null,id,company_id,' . company()->id
        ];
    }

}
