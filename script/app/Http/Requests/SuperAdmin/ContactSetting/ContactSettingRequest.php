<?php

namespace App\Http\Requests\SuperAdmin\ContactSetting;

use Illuminate\Foundation\Http\FormRequest;

class ContactSettingRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => 'nullable|email',
        ];

    }

}
