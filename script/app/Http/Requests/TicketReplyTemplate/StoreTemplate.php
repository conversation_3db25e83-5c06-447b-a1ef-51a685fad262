<?php

namespace App\Http\Requests\TicketReplyTemplate;

use App\Http\Requests\CoreRequest;

class StoreTemplate extends CoreRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'reply_heading' => 'required',
            'description' => [
                'required',
                function ($attribute, $value, $fail) {
                    if (trim_editor($value) == '') {
                        $fail(__('validation.required'));
                    }
                }
            ]
        ];
    }

}
