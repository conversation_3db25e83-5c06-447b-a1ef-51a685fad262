<?php
namespace App\Http\Requests\Holiday;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Class CreateRequest
 * @package App\Http\Requests\Admin\Employee
 */
class CreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */

    public function authorize()
    {
        // If admin
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'date.*'  => 'required',
            'occassion.*'  => 'required',
        ];
    }

    public function messages()
    {
        return [
            'date.*.required' => 'Date is a require field.',
            'occassion.*.required' => 'Occassion is a require field.',
        ];
    }

}
