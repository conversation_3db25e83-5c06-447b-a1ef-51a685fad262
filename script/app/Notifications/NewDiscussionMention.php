<?php

namespace App\Notifications;

use App\Models\Discussion;
use App\Models\EmailNotificationSetting;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class NewDiscussionMention extends BaseNotification
{


    /**
     * Create a new notification instance.
     *
     * @return void
     */
    private $discussion;
    private $emailSetting;

    public function __construct(Discussion $discussion)
    {

        $this->discussion = $discussion;
        $this->company = $this->discussion->company;
        $this->emailSetting = EmailNotificationSetting::where('company_id', $this->company->id)->where('slug', 'project-mention-notification')->first();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        $via = ['database'];

        if ($this->emailSetting->send_email == 'yes' && $notifiable->email_notifications && $notifiable->email != '') {
            array_push($via, 'mail');
        }

        if ($this->emailSetting->send_slack == 'yes' && $this->company->slackSetting->status == 'active') {
            $this->slackUserNameCheck($notifiable) ? array_push($via, 'slack') : null;
        }

        if ($this->emailSetting->send_push == 'yes' && push_setting()->status == 'active') {
            array_push($via, OneSignalChannel::class);
        }

        if ($this->emailSetting->send_push == 'yes' && push_setting()->beams_push_status == 'active') {
            $pushNotification = new \App\Http\Controllers\DashboardController();
            $pushUsersIds = [[$notifiable->id]];
            $pushNotification->sendPushNotifications($pushUsersIds, __('email.discussion.mentionSubject'), $this->discussion->title);
        }

        return $via;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = route('discussion.show', $this->discussion->id);
        $url = getDomainSpecificUrl($url, $this->company);
        $content = __('email.discussion.mentionContent') . ' ' . $this->discussion->title . '<br>' . __('app.projectName') . ':' . $this->discussion->project->project_name;

        return parent::build($notifiable)
            ->subject(__('email.discussion.mentionSubject') . ':' . $this->discussion->title . ' - ' . config('app.name') . '.')
            ->markdown('mail.email', [
                'url' => $url,
                'content' => $content,
                'themeColor' => $this->company->header_color,
                'actionText' => __('email.discussion.action'),
                'notifiableName' => $notifiable->name
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
//phpcs:ignore
    public function toArray($notifiable)
    {
        return [
            'id' => $this->discussion->id,
            'title' => $this->discussion->title,
            'project_id' => $this->discussion->project_id,
            'user' => $this->discussion->user->name
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\SlackMessage
     */
    public function toSlack($notifiable)
    {

        return $this->slackBuild($notifiable)
            ->content('*' . __('email.discussion.mentionSubject') . '*' . "\n" . $this->discussion->title);


    }

    // phpcs:ignore
    public function toOneSignal($notifiable)
    {
        return OneSignalMessage::create()
            ->subject(__('email.discussion.subject'))
            ->body($this->discussion->title);
    }

}
