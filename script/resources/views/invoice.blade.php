<!doctype html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="{{ asset('vendor/css/all.min.css') }}">

    <link rel="stylesheet" href="{{ asset('vendor/css/select2.min.css') }}">

    <!-- Simple Line Icons -->
    <link rel="stylesheet" href="{{ asset('vendor/css/simple-line-icons.css') }}">

    <!-- Template CSS -->
    <link type="text/css" rel="stylesheet" media="all" href="{{ asset('css/main.css') }}">

    <title>@lang($pageTitle)</title>
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="{{ $company->favicon_url }}">
    <meta name="theme-color" content="#ffffff">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ $company->favicon_url }}">

    @include('sections.theme_css', ['company' => $company])

    @isset($activeSettingMenu)
        <style>
            .preloader-container {
                margin-left: 510px;
                width: calc(100% - 510px)
            }

        </style>
    @endisset

    @stack('styles')

    <style>
        :root {
            --fc-border-color: #E8EEF3;
            --fc-button-text-color: #99A5B5;
            --fc-button-border-color: #99A5B5;
            --fc-button-bg-color: #ffffff;
            --fc-button-active-bg-color: #171f29;
            --fc-today-bg-color: #f2f4f7;
        }

        .preloader-container {
            height: 100vh;
            width: 100%;
            margin-left: 0;
            margin-top: 0;
        }

        .rtl .preloader-container {
            margin-right: 0;
        }

        .fc a[data-navlink] {
            color: #99a5b5;
        }

    </style>
    <style>
        #logo {
            height: 50px;
        }

    </style>


    <script src="{{ asset('vendor/jquery/jquery.min.js') }}"></script>
    <script src="{{ asset('vendor/jquery/modernizr.min.js') }}"></script>

    <script>
        var checkMiniSidebar = localStorage.getItem("mini-sidebar");
    </script>

</head>

<body id="body" class="h-100 bg-additional-grey {{ isRtl('rtl') }}">

<!-- BODY WRAPPER START -->
<div class="clearfix body-wrapper">

    <!-- MAIN CONTAINER START -->
    <section class="bg-additional-grey" id="fullscreen">

        <div class="preloader-container d-flex justify-content-center align-items-center">
            <div class="spinner-border" role="status" aria-hidden="true"></div>
        </div>

        <x-app-title class="d-block d-lg-none" :pageTitle="$pageTitle"></x-app-title>

        <!-- CONTENT WRAPPER START -->
        <div class="container content-wrapper">
            <!-- INVOICE CARD START -->
            @if (!is_null($invoice->project) && !is_null($invoice->project->client) && !is_null($invoice->project->client->clientDetails))
                @php
                    $client = $invoice->project->client;
                @endphp
            @elseif(!is_null($invoice->client_id) && !is_null($invoice->clientDetails))
                @php
                    $client = $invoice->client;
                @endphp
            @endif
            <div class="border-0 card invoice">
                <!-- CARD BODY START -->
                <div class="card-body">

                    @if ($message = Session::get('success'))
                        <div class="alert alert-success alert-dismissable">
                            <button type="button" class="close" data-dismiss="alert"
                                    aria-hidden="true"></button>
                            <i class="fa fa-check"></i> {!! $message !!}
                        </div>
                            <?php Session::forget('success'); ?>
                    @endif

                    @if ($message = Session::get('error'))
                        <div class="alert alert-danger">
                            <button type="button" class="close" data-dismiss="alert"
                                    aria-hidden="true"></button>
                            {!! $message !!}
                        </div>
                            <?php Session::forget('error'); ?>
                    @endif

                    <div class="invoice-table-wrapper">
                        <table width="100%" class="___class_+?14___">
                            <tr class="inv-logo-heading">
                                <td><img src="{{ $company->invoiceSetting->logo_url }}"
                                         alt="{{ $company->company_name }}" id="logo"/></td>
                                <td align="right"
                                    class="mt-4 font-weight-bold f-21 text-dark text-uppercase mt-lg-0 mt-md-0">
                                    @lang('app.invoice')</td>
                            </tr>
                            <tr class="inv-num">
                                <td class="f-14 text-dark">
                                    <p class="mt-3 mb-0">
                                        {{ $company->company_name }}<br>
                                        @if (!is_null($company))
                                            {!! $invoice->address ? nl2br($invoice->address->address) : '' !!}<br>
                                            {{ $company->company_phone }}
                                        @endif
                                        @if ($invoiceSetting->show_gst == 'yes' && $invoice->address->tax_number)
                                            <br>{{ $invoice->address->tax_name }}:
                                            {{ $invoice->address->tax_number }}
                                        @endif
                                    </p><br>
                                </td>
                                <td align="right">
                                    <table class="mt-3 inv-num-date text-dark f-13">
                                        <tr>
                                            <td class="bg-light-grey border-right-0 f-w-500">
                                                @lang('modules.invoices.invoiceNumber')</td>
                                            <td class="border-left-0">{{ $invoice->invoice_number }}</td>
                                        </tr>
                                        <tr>
                                            <td class="bg-light-grey border-right-0 f-w-500">
                                                @lang('modules.invoices.invoiceDate')</td>
                                            <td class="border-left-0">
                                                {{ $invoice->issue_date->translatedFormat($company->date_format) }}
                                            </td>
                                        </tr>
                                        @if (empty($invoice->order_id) && $invoice->status === 'unpaid' && $invoice->due_date->year > 1)
                                            <tr>
                                                <td class="bg-light-grey border-right-0 f-w-500">@lang('app.dueDate')
                                                </td>
                                                <td class="border-left-0">
                                                    {{ $invoice->due_date->translatedFormat($company->date_format) }}
                                                </td>
                                            </tr>
                                        @endif
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td height="20"></td>
                            </tr>
                        </table>
                        <table width="100%">
                            <tr class="inv-unpaid">
                                <td class="f-14 text-dark">
                                    <p class="mb-0 text-left">

                                        <span
                                            class="text-dark-grey ">@lang('modules.invoices.billedTo')</span><br>

                                        @if ($invoice->client && $invoice->client->name && $invoice->company->invoiceSetting->show_client_name == 'yes')
                                            {{ $invoice->client->name_salutation }}<br>
                                        @endif

                                        @if ($invoice->client && $invoice->client->email && $invoice->company->invoiceSetting->show_client_email == 'yes')
                                            {{ $invoice->client->email }}<br>
                                        @endif

                                        @if ($invoice->client && $invoice->client->mobile && $invoice->company->invoiceSetting->show_client_phone == 'yes')
                                            {{ $invoice->client->mobile_with_phonecode }}<br>
                                        @endif

                                        @if ($invoice->clientDetails && $invoice->clientDetails->company_name && $invoice->company->invoiceSetting->show_client_company_name == 'yes')
                                            {{ $invoice->clientDetails->company_name }}<br>
                                        @endif

                                        @if ($invoice->clientDetails && $invoice->clientDetails->address && $invoice->company->invoiceSetting->show_client_company_address == 'yes')
                                            {!! nl2br($invoice->clientDetails->address) !!}
                                        @endif


                                        @if ($invoiceSetting->show_gst == 'yes' && !is_null($client->clientDetails->gst_number))
                                            <br>
                                            @if ($client->clientDetails->tax_name)
                                                {{$client->clientDetails->tax_name}}
                                                : {{$client->clientDetails->gst_number}}
                                            @else
                                                @lang('app.gstIn'): {{ $client->clientDetails->gst_number }}
                                            @endif
                                        @endif

                                    </p>
                                </td>

                                @if ($invoice->show_shipping_address == 'yes')
                                    <td class="text-black f-14">
                                        <p class="mb-0 text-left"><span
                                                class="text-dark-grey ">@lang('app.shippingAddress')</span><br>
                                            {!! nl2br($client->clientDetails->shipping_address) !!}</p>
                                    </td>
                                @endif

                                <td align="right" class="mt-4 mt-lg-0 mt-md-0">
                                    @if($invoice->clientDetails->company_logo)
                                        <img src="{{ $invoice->clientDetails->image_url }}"
                                             alt="{{ $invoice->clientDetails->company_name }}" class="logo"
                                             style="height:50px;"/>
                                        <br><br><br>
                                    @endif
                                    <span
                                        class="unpaid {{ $invoice->status == 'partial' ? 'text-primary border-primary' : '' }} {{ $invoice->status == 'paid' ? 'text-success border-success' : '' }} rounded f-15 ">@lang('modules.invoices.' . $invoice->status)</span>
                                </td>
                            </tr>
                            <tr>
                                <td height="30" colspan="2"></td>
                            </tr>
                        </table>

                        <p class="mb-0">
                            @if (($invoiceSetting->show_project == 1) && isset($invoice->project->project_name))
                                <span
                                    class="text-dark-grey ">@lang('modules.invoices.projectName')</span>
                                <br>
                                {{ $invoice->project->project_name }}
                            @endif
                        </p>
                        <br><br>

                        <table width="100%" class="inv-desc d-none d-lg-table d-md-table">
                            <tr>
                                <td colspan="2">
                                    <table class="inv-detail f-14 table-responsive-sm" width="100%">
                                        <tr class="i-d-heading bg-light-grey text-dark-grey font-weight-bold">
                                            <td class="border-right-0">@lang('app.description')</td>
                                            @if ($invoiceSetting->hsn_sac_code_show)
                                                <td class="border-right-0 border-left-0"
                                                    align="right">@lang('app.hsnSac')</td>
                                            @endif
                                            <td class="border-right-0 border-left-0" align="right">
                                                @lang('modules.invoices.qty')
                                            </td>
                                            <td class="border-right-0 border-left-0" align="right">
                                                @lang('modules.invoices.unitPrice')
                                                ({{ $invoice->currency->currency_code }})
                                            </td>
                                            <td class="border-left-0 border-right-0 "
                                                align="right">@lang('modules.invoices.tax')</td>
                                            <td class="border-left-0" align="right">
                                                @lang('modules.invoices.amount')
                                                ({{ $invoice->currency->currency_code }})
                                            </td>
                                        </tr>
                                        @foreach ($invoice->items->sortBy('field_order') as $item)
                                            @if ($item->type == 'item')
                                                <tr class="text-dark font-weight-semibold f-13">
                                                    <td>{{ $item->item_name }}</td>
                                                    @if ($invoiceSetting->hsn_sac_code_show)
                                                        <td align="right">{{ $item->hsn_sac_code }}</td>
                                                    @endif
                                                    <td align="right">{{ $item->quantity }} @if($item->unit)
                                                            <br><span
                                                                class="f-11 text-dark-grey">{{ $item->unit->unit_type }}</span>
                                                        @endif</td>
                                                    <td align="right">
                                                        {{ currency_format($item->unit_price, $invoice->currency_id, false) }}
                                                    </td>
                                                    <td align="right">{{ $item->tax_list }}</td>
                                                    <td align="right">
                                                        {{ currency_format($item->amount, $invoice->currency_id, false) }}
                                                    </td>
                                                </tr>
                                                @if ($item->item_summary || $item->invoiceItemImage)
                                                    <tr class="text-dark f-12">
                                                        <td colspan="{{ $invoiceSetting->hsn_sac_code_show ? '6' : '5' }}"
                                                            class="border-bottom-0">
                                                            {!! nl2br(strip_tags($item->item_summary)) !!}
                                                            @if ($item->invoiceItemImage)
                                                                <p class="mt-2">
                                                                    <a href="javascript:;" class="img-lightbox"
                                                                       data-image-url="{{ $item->invoiceItemImage->file_url }}">
                                                                        <img
                                                                            src="{{ $item->invoiceItemImage->file_url }}"
                                                                            width="80" height="80"
                                                                            class="img-thumbnail">
                                                                    </a>
                                                                </p>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endif
                                        @endforeach


                                        <tr>
                                            <td colspan="{{ $invoiceSetting->hsn_sac_code_show ? '4' : '3' }}"
                                                class="blank-td border-bottom-0 border-left-0 border-right-0"></td>
                                            <td class="p-0 border-right-0" align="right">
                                                <table width="100%">
                                                    <tr class="text-dark-grey" align="right">
                                                        <td class="w-50 border-top-0 border-left-0">
                                                            @lang('modules.invoices.subTotal')</td>
                                                    </tr>
                                                    @if ($discount != 0 && $discount != '')
                                                        <tr class="text-dark-grey" align="right">
                                                            <td class="w-50 border-top-0 border-left-0">
                                                                @lang('modules.invoices.discount')</td>
                                                        </tr>
                                                    @endif
                                                    @foreach ($taxes as $key => $tax)
                                                        <tr class="text-dark-grey" align="right">
                                                            <td class="w-50 border-top-0 border-left-0">
                                                                {{ $key }}</td>
                                                        </tr>
                                                    @endforeach
                                                    <tr class=" text-dark-grey font-weight-bold" align="right">
                                                        <td class="w-50 border-bottom-0 border-left-0">
                                                            @lang('modules.invoices.total')</td>
                                                    </tr>
                                                    <tr class="bg-light-grey text-dark f-w-500 f-16" align="right">
                                                        <td class="w-50 border-bottom-0 border-left-0">
                                                            @lang('modules.invoices.total')
                                                            @lang('modules.invoices.due')</td>
                                                    </tr>
                                                </table>
                                            </td>
                                            <td class="p-0 border-left-0" align="right">
                                                <table width="100%">
                                                    <tr class="text-dark-grey" align="right">
                                                        <td class="border-top-0 border-right-0">
                                                            {{ currency_format($invoice->sub_total, $invoice->currency_id, false) }}
                                                        </td>
                                                    </tr>
                                                    @if ($discount != 0 && $discount != '')
                                                        <tr class="text-dark-grey" align="right">
                                                            <td class="border-top-0 border-right-0">
                                                                {{ currency_format($discount, $invoice->currency_id, false) }}</td>
                                                        </tr>
                                                    @endif
                                                    @foreach ($taxes as $key => $tax)
                                                        <tr class="text-dark-grey" align="right">
                                                            <td class="border-top-0 border-right-0">
                                                                {{ currency_format($tax, $invoice->currency_id, false) }}</td>
                                                        </tr>
                                                    @endforeach
                                                    <tr class=" text-dark-grey font-weight-bold" align="right">
                                                        <td class="border-bottom-0 border-right-0">
                                                            {{ currency_format($invoice->total, $invoice->currency_id, false) }}
                                                        </td>
                                                    </tr>
                                                    <tr class="bg-light-grey text-dark f-w-500 f-16" align="right">
                                                        <td class="border-bottom-0 border-right-0">
                                                            {{ currency_format($invoice->amountDue(), $invoice->currency_id, false) }}
                                                            {{ $invoice->currency->currency_code }}</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>

                            </tr>
                        </table>
                        <table width="100%" class="inv-desc-mob d-lg-none d-md-none">

                            @foreach ($invoice->items->sortBy('field_order') as $item)
                                @if ($item->type == 'item')
                                    <tr>
                                        <th width="50%" class="bg-light-grey text-dark-grey font-weight-bold">
                                            @lang('app.description')</th>
                                        <td class="p-0 ">
                                            <table>
                                                <tr width="100%" class="font-weight-semibold f-13">
                                                    <td class="border-left-0 border-right-0 border-top-0">
                                                        {{ $item->item_name }}</td>
                                                </tr>
                                                @if ($item->item_summary != '' || $item->invoiceItemImage)
                                                    <tr>
                                                        <td
                                                            class="border-left-0 border-right-0 border-bottom-0 f-12">
                                                            {!! nl2br(strip_tags($item->item_summary)) !!}
                                                            @if ($item->invoiceItemImage)
                                                                <p class="mt-2">
                                                                    <a href="javascript:;" class="img-lightbox"
                                                                       data-image-url="{{ $item->invoiceItemImage->file_url }}">
                                                                        <img
                                                                            src="{{ $item->invoiceItemImage->file_url }}"
                                                                            width="80" height="80"
                                                                            class="img-thumbnail">
                                                                    </a>
                                                                </p>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endif
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th width="50%" class="bg-light-grey text-dark-grey font-weight-bold">
                                            @lang('modules.invoices.qty')</th>
                                        <td width="50%">{{ $item->quantity }}</td>
                                    </tr>
                                    <tr>
                                        <th width="50%" class="bg-light-grey text-dark-grey font-weight-bold">
                                            @lang('modules.invoices.unitPrice')
                                            ({{ $invoice->currency->currency_code }})
                                        </th>
                                        <td width="50%">
                                            {{ currency_format($item->unit_price, $invoice->currency_id, false) }}</td>
                                    </tr>
                                    <tr>
                                        <th width="50%" class="bg-light-grey text-dark-grey font-weight-bold">
                                            @lang('modules.invoices.amount')
                                            ({{ $invoice->currency->currency_code }})
                                        </th>
                                        <td width="50%">{{ currency_format($item->amount, $invoice->currency_id, false) }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td height="3" class="p-0 " colspan="2"></td>
                                    </tr>
                                @endif
                            @endforeach

                            <tr>
                                <th width="50%" class="text-dark-grey font-weight-normal">
                                    @lang('modules.invoices.subTotal')
                                </th>
                                <td width="50%" class="text-dark-grey font-weight-normal">
                                    {{ currency_format($invoice->sub_total, $invoice->currency_id, false) }}</td>
                            </tr>
                            @if ($discount != 0 && $discount != '')
                                <tr>
                                    <th width="50%" class="text-dark-grey font-weight-normal">
                                        @lang('modules.invoices.discount')
                                    </th>
                                    <td width="50%" class="text-dark-grey font-weight-normal">
                                        {{ currency_format($discount, $invoice->currency_id, false) }}</td>
                                </tr>
                            @endif

                            @foreach ($taxes as $key => $tax)
                                <tr>
                                    <th width="50%" class="text-dark-grey font-weight-normal">
                                        {{ $key }}</th>
                                    <td width="50%" class="text-dark-grey font-weight-normal">
                                        {{ currency_format($tax, $invoice->currency_id, false) }}</td>
                                </tr>
                            @endforeach
                            <tr>
                                <th width="50%" class="text-dark-grey font-weight-bold">
                                    @lang('modules.invoices.total')</th>
                                <td width="50%" class="text-dark-grey font-weight-bold">
                                    {{ currency_format($invoice->total, $invoice->currency_id, false) }}</td>
                            </tr>
                            <tr>
                                <th width="50%" class="f-16 bg-light-grey text-dark font-weight-bold">
                                    @lang('modules.invoices.total')
                                    @lang('modules.invoices.due')</th>
                                <td width="50%" class="f-16 bg-light-grey text-dark font-weight-bold">
                                    {{ currency_format($invoice->amountDue(), $invoice->currency_id, false) }}
                                    {{ $invoice->currency->currency_code }}</td>
                            </tr>
                        </table>

                        @includeIf('invoices.payment_details')


                        <table class="inv-note">
                            <tr>
                                <td height="30" colspan="2"></td>
                            </tr>
                            <tr>
                                <td style="vertical-align: text-top">
                                    <table>
                                        <tr>@lang('app.note')</tr>
                                        <tr>
                                            <p class="text-dark-grey">{!! !empty($invoice->note) ? nl2br($invoice->note) : '--' !!}</p>
                                        </tr>
                                    </table>
                                </td>
                                <td align="right">
                                    <table>
                                        <tr>@lang('modules.invoiceSettings.invoiceTerms')</tr>
                                        <tr>
                                            <p class="text-dark-grey">{!! nl2br($invoiceSetting->invoice_terms) !!}
                                            </p>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            @if (isset($invoiceSetting->other_info))
                                <tr>
                                    <td align="vertical-align: text-top">
                                        <table>
                                            <tr>
                                                <p class="text-dark-grey">{!! nl2br($invoiceSetting->other_info) !!}
                                                </p>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            @endif
                        </table>
                    </div>
                </div>
                <!-- CARD BODY END -->
                <!-- CARD FOOTER START -->
                <div
                    class="py-0 mb-4 bg-white border-0 card-footer d-flex justify-content-end py-lg-4 py-md-4 mb-lg-3 mb-md-3 ">


                    <div class="d-flex">

                        <x-forms.link-secondary icon="download"
                                                :link="route('front.invoice_download', [md5($invoice->id)])"
                                                class="mr-3">
                            @lang('app.download')</x-forms.link-secondary>
                        @if ($invoice->status == 'paid' && $invoice->file != null)
                            <x-forms.link-secondary icon="download"
                                                    :link="route('front.invoice_download', [md5($invoice->id), 'download-uploaded' => 'true'])"
                                                    class="mr-3">
                                @lang('app.download') @lang('app.uploadedFile')</x-forms.link-secondary>
                        @endif

                        @if ($invoice->total > 0 && $invoice->status != 'paid' && $invoice->status != 'pending-confirmation' && $credentials->show_pay)
                            <div class="mr-3 inv-action mr-lg-3 mr-md-3 dropup">


                                <button class="dropdown-toggle btn-secondary" type="button" id="dropdownMenuButton"
                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    @lang('modules.invoices.payNow')
                                    <span><i class="fa fa-chevron-down f-15"></i></span>
                                </button>
                                <!-- DROPDOWN - INFORMATION -->
                                <ul class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton"
                                    tabindex="0">
                                    @if ($credentials->paypal_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark"
                                               href="{{ route('paypal_public', [$invoice->id]) }}">
                                                <i class="mr-2 fab fa-paypal f-w-500 f-11"></i>
                                                @lang('modules.invoices.payPaypal')
                                            </a>
                                        </li>
                                    @endif
                                    @if ($credentials->razorpay_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark" href="javascript:;"
                                               id="razorpayPaymentButton">
                                                <i class="mr-2 fa fa-credit-card f-w-500 f-11"></i>
                                                @lang('modules.invoices.payRazorpay')
                                            </a>
                                        </li>
                                    @endif
                                    @if ($credentials->stripe_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark" href="javascript:;"
                                               data-invoice-id="{{ $invoice->id }}" id="stripeModal">
                                                <i class="mr-2 fab fa-stripe-s f-w-500 f-11"></i>
                                                @lang('modules.invoices.payStripe')
                                            </a>
                                        </li>
                                    @endif
                                    @if ($credentials->paystack_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark" href="javascript:void(0);"
                                               data-invoice-id="{{ $invoice->id }}" id="paystackModal">
                                                <img style="height: 15px;" src="{{ asset('img/paystack.jpg') }}">
                                                @lang('modules.invoices.payPaystack')</a>
                                        </li>
                                    @endif

                                    @if ($credentials->flutterwave_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark" href="javascript:void(0);"
                                               data-invoice-id="{{ $invoice->id }}" id="flutterwaveModal">
                                                <img style="height: 15px;"
                                                     src="{{ asset('img/flutterwave.png') }}">
                                                @lang('modules.invoices.payFlutterwave')</a>
                                        </li>
                                    @endif
                                    @if ($credentials->payfast_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark" href="javascript:void(0);"
                                               id="payfastModal">
                                                <img style="height: 15px;" src="{{ asset('img/payfast.png') }}">
                                                @lang('modules.invoices.payPayfast')</a>
                                        </li>
                                    @endif
                                    @if ($credentials->square_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark" href="javascript:void(0);"
                                               id="squareModal">
                                                <img style="height: 15px;" src="{{ asset('img/square.svg') }}">
                                                @lang('modules.invoices.paySquare')</a>
                                        </li>
                                    @endif

                                    @if ($credentials->authorize_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark" href="javascript:void(0);"
                                               data-invoice-id="{{ $invoice->id }}" id="authorizeModal">
                                                <img style="height: 15px;"
                                                     src="{{ asset('img/authorize.png') }}">
                                                @lang('modules.invoices.payAuthorize')</a>
                                        </li>
                                    @endif

                                    @if ($credentials->mollie_status == 'active')
                                        <li>
                                            <a class="dropdown-item f-14 text-dark" href="javascript:void(0);"
                                               data-invoice-id="{{ $invoice->id }}" id="mollieModal">
                                                <img style="height: 20px;" src="{{ asset('img/mollie.png') }}">
                                                @lang('modules.invoices.payMollie')</a>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        @endif
                    </div>
                </div>
                <!-- CARD FOOTER END -->
            </div>
            <!-- INVOICE CARD END -->

        </div>
        <!-- CONTENT WRAPPER END -->


    </section>
    <!-- MAIN CONTAINER END -->
</div>
<!-- BODY WRAPPER END -->

<!-- also the modal itself -->
<div id="myModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog d-flex justify-content-center align-items-center modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modelHeading">Modal title</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
            </div>
            <div class="modal-body">
                {{__('app.loading')}}
            </div>
            <div class="modal-footer">
                <button type="button" class="mr-3 rounded btn-cancel" data-dismiss="modal">Close</button>
                <button type="button" class="rounded btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Global Required Javascript -->
<script src="{{ asset('js/main.js') }}"></script>
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script src="https://js.stripe.com/v3/"></script>

<script>
    const MODAL_LG = '#myModal';
    const MODAL_HEADING = '#modelHeading';
    document.loading = '@lang('app.loading')';
    const dropifyMessages = {
        default: '@lang('app.dragDrop')',
        replace: '@lang('app.dragDropReplace')',
        remove: '@lang('app.remove')',
        error: '@lang('app.largeFile')'
    };

    $('body').on('click', '.img-lightbox', function () {
        var imageUrl = $(this).data('image-url');
        const url = "{{ route('front.public.show_image') . '?image_url=' }}" + imageUrl;

        $(MODAL_LG + ' ' + MODAL_HEADING).html('...');
        $.ajaxModal(MODAL_LG, url);
    });

    $(window).on('load', function () {
        // Animate loader off screen
        init();
        $(".preloader-container").fadeOut("slow", function () {
            $(this).removeClass("d-flex");
        });
    });

    @if ($credentials->stripe_status == 'active')
    $('body').on('click', '#stripeModal', function () {
        let invoiceId = $(this).data('invoice-id');
        let queryString = "?invoice_id=" + invoiceId;
        let url = "{{ route('front.stripe_modal') }}" + queryString;

        $(MODAL_LG + ' ' + MODAL_HEADING).html('...');
        $.ajaxModal(MODAL_LG, url);
    })
    @endif

    @if ($credentials->paystack_status == 'active')
    $('body').on('click', '#paystackModal', function () {
        let id = $(this).data('invoice-id');
        let queryString = "?id=" + id + "&type=invoice";
        let url = "{{ route('front.paystack_modal') }}" + queryString;

        $(MODAL_LG + ' ' + MODAL_HEADING).html('...');
        $.ajaxModal(MODAL_LG, url);
    })
    @endif

    @if ($credentials->flutterwave_status == 'active')
    $('body').on('click', '#flutterwaveModal', function () {
        let id = $(this).data('invoice-id');
        let queryString = "?id=" + id + "&type=invoice";
        let url = "{{ route('front.flutterwave_modal') }}" + queryString;

        $(MODAL_LG + ' ' + MODAL_HEADING).html('...');
        $.ajaxModal(MODAL_LG, url);
    })
    @endif

    @if ($credentials->authorize_status == 'active')
    $('body').on('click', '#authorizeModal', function () {
        let id = $(this).data('invoice-id');
        let queryString = "?id=" + id + "&type=invoice";
        let url = "{{ route('front.authorize_modal') }}" + queryString;

        $(MODAL_LG + ' ' + MODAL_HEADING).html('...');
        $.ajaxModal(MODAL_LG, url);
    })
    @endif

    @if ($credentials->mollie_status == 'active')
    $('body').on('click', '#mollieModal', function () {
        let id = $(this).data('invoice-id');
        let queryString = "?id=" + id + "&type=invoice";
        let url = "{{ route('front.mollie_modal') }}" + queryString;

        $(MODAL_LG + ' ' + MODAL_HEADING).html('...');
        $.ajaxModal(MODAL_LG, url);
    })
    @endif

    @if ($credentials->payfast_status == 'active')
    $('body').on('click', '#payfastModal', function () {

        // Block model UI until payment happens
        $.easyBlockUI();

        $.easyAjax({
            url: "{{ route('payfast_public') }}",
            type: "POST",
            blockUI: true,
            data: {
                id: '{{ $invoice->id }}',
                type: 'invoice',
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                if (response.status == 'success') {
                    $('body').append(response.form);
                    $('#payfast-pay-form').submit();
                }
            }
        });
    })
    @endif

    @if ($credentials->square_status == 'active')
    $('body').on('click', '#squareModal', function () {
        // Block model UI until payment happens
        $.easyBlockUI();

        $.easyAjax({
            url: "{{ route('square_public') }}",
            type: "POST",
            blockUI: true,
            data: {
                id: '{{ $invoice->id }}',
                type: 'invoice',
                _token: '{{ csrf_token() }}'
            }
        });
    });
    @endif

    @if ($credentials->razorpay_status == 'active')
    $('body').on('click', '#razorpayPaymentButton', function () {
        // var amount = {{ number_format((float) $invoice->amountDue(), 2, '.', '') * 100 }};
        var amount = {{ intval($invoice->amountDue() * 100) }};


        var invoiceId = {{ $invoice->id }};
        @if ($invoice->project && $invoice->project->client)
        var clientEmail = "{{ $invoice->project->client->email }}";
        @else
        var clientEmail = "{{ $invoice->client->email }}";
        @endif

        var options = {
            "key": "{{ $credentials->razorpay_mode == 'test' ? $credentials->test_razorpay_key : $credentials->live_razorpay_key }}",
            "amount": amount,
            "currency": '{{ $invoice->currency->currency_code }}',
            "name": "{{ $companyName }}",
            "description": "Invoice Payment",
            "image": "{{ $company->logo_url }}",
            "handler": function (response) {
                confirmRazorpayPayment(response.razorpay_payment_id, invoiceId);
            },
            "payment": {
                "capture": "automatic",
                "capture_options": {
                    "automaticexpiryperiod": 12,
                    "manualexpiryperiod": 7200,
                    "refund_speed": "optimum"
                },
            },
            "modal": {
                "ondismiss": function () {
                    // On dismiss event
                }
            },
            "prefill": {
                "email": clientEmail
            },
            "notes": {
                "purchase_id": invoiceId, //invoice ID
                "type": "invoice"
            }
        };

        var rzp1 = new Razorpay(options);

        /* Make an entry to payment table when payment fails */
        rzp1.on('payment.failed', function (response) {
            /* Payment Failed Response will be something like this - code: "BAD_REQUEST_ERROR", reason: "payment_failed"
                , description: "Payment failed"
            */
            url = "{{ route('front.invoice_payment_failed', ':id') }}";
            url = url.replace(':id', invoiceId);

            $.easyAjax({
                url: url,
                type: "POST",
                data: {
                    errorMessage: response.error,
                    gateway: 'Razorpay',
                    "_token": "{{ csrf_token() }}"
                },
            })
        });

        rzp1.open();

    })

    //Confirmation after transaction
    function confirmRazorpayPayment(id, invoiceId) {

        // Block model UI until payment happens
        $.easyBlockUI();

        $.easyAjax({
            type: 'POST',
            url: "{{ route('pay_with_razorpay',[$invoice->company->hash]) }}",
            data: {
                paymentId: id,
                invoiceId: invoiceId,
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                // Unblock Modal UI when got error response
                $.easyUnblockUI('#stripeAddress');
            }
        });
    }
    @endif
</script>

</body>

</html>
