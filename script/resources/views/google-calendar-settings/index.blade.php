@extends('layouts.app')

@section('content')

    <!-- SETTINGS START -->
    <div class="w-100 d-flex ">

        @include('sections.setting-sidebar')

        <x-setting-card method="POST">
            <x-slot name="header">
                <div class="s-b-n-header" id="tabs">
                    <h2 class="mb-0 p-20 f-21 font-weight-normal  border-bottom-grey">
                        @lang($pageTitle)</h2>
                </div>
            </x-slot>

            @if(user()->is_superadmin)
            <div class="col-lg-12 col-md-12 ntfcn-tab-content-left w-100 p-4">
            @else
            <div class="col-lg-8 col-md-8 ntfcn-tab-content-left w-100 p-4">
            @endif
                <div class="row">
                    <div class="col-lg-12">
                        <x-forms.checkbox :fieldLabel="__('app.status')" fieldName="status" fieldId="google_status"
                                          fieldValue="active" fieldRequired="true"
                                          :checked="($companyOrGlobalSetting->google_calendar_status === 'active' || $companyOrGlobalSetting->google_calendar_status==1)"/>
                    </div>
                </div>
                <div class="row google_details mt-3 @if ($companyOrGlobalSetting->google_calendar_status == 'inactive') d-none @endif">
                    {{-- SAAS --}}
                    @if(user()->is_superadmin)
                    <div class="col-lg-12 col-md-12">
                        <x-forms.text
                            :fieldLabel="__('modules.googleCalendar.clientId')"
                            :fieldPlaceholder="__('placeholders.id')" fieldName="google_client_id"
                            fieldId="google_client_id"
                            :fieldValue="$globalSetting->google_client_id"
                            fieldRequired="true"/>
                    </div>

                    <div class="col-lg-12">
                        <x-forms.label class="mt-3" fieldId="password"
                                       :fieldLabel="__('modules.googleCalendar.clientSecret')"
                                       fieldRequired="true">
                        </x-forms.label>
                        <x-forms.input-group>
                            <input type="password" name="google_client_secret" id="google_client_secret"
                                   class="form-control height-35 f-14" value="{{ $globalSetting->google_client_secret }}">
                            <x-slot name="preappend">
                                <button type="button" data-toggle="tooltip"
                                        data-original-title="@lang('app.viewPassword')"
                                        class="btn btn-outline-secondary border-grey height-35 toggle-password"><i
                                        class="fa fa-eye"></i></button>
                            </x-slot>
                        </x-forms.input-group>
                    </div>

                    <div class="col-lg-12 mt-4">
                        <x-forms.label fieldId="" for="mail_from_name"
                                       :fieldLabel="__('messages.googleCalendar.AuthorizedRedirectURI')"
                                       :popover="__('messages.googleCalendar.AuthorizedRedirectURIInfoMessage')">
                        </x-forms.label>
                        <p class="text-bold"><span id="google-calendar-link-text">{{ config('services.google.redirect_uri') }}</span>
                            <a href="javascript:;" class="btn-copy btn-secondary f-12 rounded p-1 py-2 ml-1"
                               data-clipboard-target="#google-calendar-link-text">
                                <i class="fa fa-copy mx-1"></i>@lang('app.copy')</a>
                        </p>
                        <p class="text-primary">(@lang('messages.googleCalendar.addGoogleCalendarUrl'))</p>
                    </div>
                    @else
                        <div class="w-100 h-100 d-flex align-items-center justify-content-center">
                            <div>
                                @lang('superadmin.googleCalendarConfig')
                            </div>
                        </div>
                    @endif
                </div>
            </div>


            {{-- SAAS --}}
            @if(!user()->is_superadmin && user()->permission('manage_google_calendar_setting') == 'all')
            <div class="col-xl-4 col-lg-12 col-md-12 ntfcn-tab-content-right border-left-grey p-4">
                <h4 class="f-16  f-w-500 text-dark-grey">@lang("messages.googleCalendar.notificationTitle")</h4>
                <div class="mb-3 d-flex">
                    <x-forms.checkbox :checked="$module->lead_status == '1'"
                                      :fieldLabel="__('app.menu.leads')"
                                      fieldName="lead_status" fieldId="lead_status" :fieldValue="$module->lead_status"/>
                </div>
                <div class="mb-3 d-flex">
                    <x-forms.checkbox :checked="$module->leave_status == '1'"
                                      :fieldLabel="__('app.menu.leaves')"
                                      fieldName="leave_status" fieldId="leave_status"
                                      :fieldValue="$module->leave_status"/>
                </div>
                <div class="mb-3 d-flex">
                    <x-forms.checkbox :checked="$module->invoice_status == '1'"
                                      :fieldLabel="__('app.menu.invoices')"
                                      fieldName="invoice_status" fieldId="invoice_status"
                                      :fieldValue="$module->invoice_status"/>
                </div>
                <div class="mb-3 d-flex">
                    <x-forms.checkbox :checked="$module->contract_status == '1'"
                                      :fieldLabel="__('app.menu.contracts')"
                                      fieldName="contract_status" fieldId="contract_status"
                                      :fieldValue="$module->contract_status"/>
                </div>
                <div class="mb-3 d-flex">
                    <x-forms.checkbox :checked="$module->task_status == '1'"
                                      :fieldLabel="__('app.menu.tasks')"
                                      fieldName="task_status" fieldId="task_status" :fieldValue="$module->task_status"/>
                </div>
                <div class="mb-3 d-flex">
                    <x-forms.checkbox :checked="$module->event_status == '1'"
                                      :fieldLabel="__('app.menu.events')"
                                      fieldName="event_status" fieldId="event_status"
                                      :fieldValue="$module->event_status"/>
                </div>
                <div class="mb-3 d-flex">
                    <x-forms.checkbox :checked="$module->holiday_status == '1'"
                                      :fieldLabel="__('app.menu.holiday')"
                                      fieldName="holiday_status" fieldId="holiday_status"
                                      :fieldValue="$module->holiday_status"/>
                </div>
            </div>
            @endif

            <x-slot name="action">
                <!-- Buttons Start -->
                <div class="w-100 border-top-grey">
                    <x-setting-form-actions>
                        <x-forms.button-primary id="save-form" class="mr-3" icon="check">@lang('app.save')
                        </x-forms.button-primary>
                        @if (($globalSetting->google_calendar_status == 'active' && !empty($globalSetting->google_client_id) && !empty($globalSetting->google_client_secret)) && $setting && $setting->google_calendar_verification_status == 'non_verified' && empty($setting->token) && empty($setting->name) && empty($setting->google_id))
                            <x-forms.button-secondary class="mr-3" id="verify-google-calendar">
                                @lang('app.verify')
                            </x-forms.button-secondary>
                        @endif

                        @if (($globalSetting->google_calendar_status == 'active' && !empty($globalSetting->google_client_id) && !empty($globalSetting->google_client_secret)) && $setting && $setting->google_calendar_verification_status == 'verified' && !empty($setting->token) && !empty($setting->name) && !empty($setting->google_id))
                            <x-forms.button-secondary id="disable-google-calendar" class="mr-3">
                                @lang('app.disable')
                            </x-forms.button-secondary>
                        @endif

                    </x-setting-form-actions>
                </div>
                <!-- Buttons End -->
            </x-slot>

        </x-setting-card>

    </div>
    <!-- SETTINGS END -->
@endsection

@push('scripts')

    <script>

        const clipboard = new ClipboardJS('.btn-copy');

        clipboard.on('success', function (e) {
            Swal.fire({
                icon: 'success',
                text: '@lang("app.webhookUrlCopied")',
                toast: true,
                position: 'top-end',
                timer: 3000,
                timerProgressBar: true,
                showConfirmButton: false,
                customClass: {
                    confirmButton: 'btn btn-primary',
                },
                showClass: {
                    popup: 'swal2-noanimation',
                    backdrop: 'swal2-noanimation'
                },
            })
        });

        $('#save-form').click(function () {
            $.easyAjax({
                url: "{{ route('google-calendar-settings.store') }}",
                container: '#editSettings',
                blockUI: true,
                type: "POST",
                file: true,
                data: $('#editSettings').serialize(),
                success: function (response) {
                    if (response.status == 'success') {
                        window.location.reload();
                    }
                }
            })
        });

        // Show/hide google calendar details
        $(document).on('change', '#google_status', function () {
            $('.google_details').toggleClass('d-none');
        });

        // Show/hide google calendar details
        $(document).on('change', '.form-check-input', function () {
            $(this).is(':checked') ? $(this).val('1') : $(this).val('0');
        });

        $(document).on('click', '#verify-google-calendar', function () {
            window.location.href = "{{ route('googleAuth') }}";
        });

        // Show/hide google calendar details
        $(document).on('click', '#disable-google-calendar', function () {
            Swal.fire({
                title: "@lang('messages.sweetAlertTitle')",
                text: "@lang('messages.googleCalendar.confirmRemove')",
                icon: 'warning',
                showCancelButton: true,
                focusConfirm: false,
                confirmButtonText: "@lang('messages.googleCalendar.yesRemove')",
                cancelButtonText: "@lang('app.cancel')",
                customClass: {
                    confirmButton: 'btn btn-primary mr-3',
                    cancelButton: 'btn btn-secondary'
                },
                showClass: {
                    popup: 'swal2-noanimation',
                    backdrop: 'swal2-noanimation'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    const url = "{{ route('googleAuth.destroy') }}";
                    const token = "{{ csrf_token() }}";

                    $.easyAjax({
                        type: 'POST',
                        url: url,
                        data: {
                            '_token': token,
                            '_method': 'DELETE'
                        },
                        success: function (response) {
                            if (response.status === "success") {
                                location.reload();
                            }
                        }
                    });
                }
            });
        });

    </script>
@endpush
