@extends('layouts.app')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/jquery-migrate-3.0.0.min.js"></script>
<script src="https://code.jscharting.com/latest/jscharting.js"></script>

@push('datatable-styles')
    @include('sections.datatable_css')
    <style>
        #chartdiv {
            width: 100%;
            background-color: #fff;
            position: relative;
        }

        #brandingLogo {
            display: none !important;
        }

        tspan {
            font-size: 14px;
        }

        .no-select() {
            -moz-user-select: none;
            -ms-user-select: none;
            -webkit-user-select: none;
            user-select: none;
        }

        .no-wrap() {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        #dragRoot ul {
            display: block;
            margin: 0;
            padding: 0 0 0 20px;
        }

        #dragRoot {
            .no-select();
            cursor: default;
            background-color: #fff;
            width: 100%;
            height: 100%;
        }

        #dragRoot li {
            display: block;
            margin: 2px;
            padding: 2px 2px 2px 0;

            [class*="node"] {
                display: inline-block;

                &.hover {
                    background-color: navy;
                    color: white;
                }
            }

        }

        #dragRoot li li {
            border-left: 1px solid silver;
        }

        #dragRoot li li:before {
            color: silver;
            font-weight: 300;
            content: "— ";
        }

        .node-facility {
            color: navy;
            font-weight: bold;
        }

        .node-cpe {
            color: #000;
            cursor: pointer;
        }

        #drophere li{
            color: rgb(5, 5, 34);
            font-weight:bold;
        }

        #dragRoot ul li > span {
            color:rgb(6, 6, 44);
            padding: 6px;
            letter-spacing: 2px;

        }

        #drophere li > span {
            cursor: default;
        }

        .chartHeading-org{
            position: relative;
            padding: 20px;
        }

        .setOpacity{
            opacity: 0;
        }

        .unsetOpacity{
            opacity: 1;
        }

        #resize,#full_view
        {
            cursor: pointer;
        }

        #dragRoot{
            overflow-y: auto;
            overflow-x: hidden;
        }

    </style>
    @if (!user()->dark_theme)
        <style>
            .chartHeading-org{
                background: #fff;
            }
        </style>
    @else
        <style>
            #dragRoot {
                margin-top: 1.5rem !important;
            }
        </style>
    @endif
@endpush

@php
$addDesignationPermission = user()->permission('add_designation');
$editDesignationPermission = user()->permission('edit_designation');
@endphp

@section('filter-section')
    <x-filters.filter-box>

        <!-- SEARCH BY TASK START -->
        <div class="task-search d-flex  py-1 pr-lg-3 px-0 border-right-grey align-items-center">
            <form class="w-100 mr-1 mr-lg-0 mr-md-1 ml-md-1 ml-0 ml-lg-0">
                <div class="input-group bg-grey rounded">
                    <div class="input-group-prepend">
                        <span class="input-group-text border-0 bg-additional-grey">
                            <i class="fa fa-search f-13 text-dark-grey"></i>
                        </span>
                    </div>
                    <input type="text" class="form-control f-14 p-1 border-additional-grey" id="search-text-field"
                        placeholder="@lang('modules.department.searchValidation')">
                </div>
            </form>
        </div>
        <!-- SEARCH BY TASK END -->

        <!-- RESET START -->
        <div class="select-box d-flex py-1 px-lg-2 px-md-2 px-0">
            <x-forms.button-secondary class="btn-xs d-none" id="reset-filters" icon="times-circle">
                @lang('app.clearFilters')
            </x-forms.button-secondary>
        </div>
        <!-- RESET END -->
    </x-filters.filter-box>
@endsection

@section('content')
    <!-- CONTENT WRAPPER START -->
    <div class="content-wrapper">

        <!-- Add Task Export Buttons Start -->
        <div class="d-grid d-lg-flex d-md-flex action-bar">
            <div id="table-actions" class="flex-grow-1 align-items-center">
                @if ($addDesignationPermission == 'all' || $addDesignationPermission == 'added')
                <x-forms.link-primary :link="route('designations.create')" class="mr-3 openRightModal float-left" icon="plus" data-redirect-url="{{ route('designation.hierarchy') }}">
                    @lang('app.menu.addDesignation')
                </x-forms.link-primary>
                @endif
            </div>

            <div class="btn-group mt-2 mt-lg-0 mt-md-0 ml-0 ml-lg-3 ml-md-3" role="group">
                <a href="{{ route('designations.index') }}" class="btn btn-secondary f-14" data-toggle="tooltip"
                data-original-title="@lang('modules.leaves.tableView')"><i class="side-icon bi bi-list-ul"></i></a>

                <a href="{{ route('designation.hierarchy') }}" class="btn btn-secondary f-14 btn-active" data-toggle="tooltip"
                    data-original-title="@lang('app.hierarchy')"><i class="bi bi-diagram-3"></i></a>
            </div>
        </div>

        <div class="row">
            <div class="col-12 chart-section">
                <div class="row">
                    <div class="col-md-6" id="chartTree">
                        @include('designations-hierarchy.chart_tree')
                    </div>

                    <div class="col-md-6" id="chartOrganization">
                        @include('designations-hierarchy.chart_organization')
                    </div>
                </div>
            </div>
        </div>

    </div>


    <!-- CONTENT WRAPPER END -->
@endsection

@push('scripts')
<script>
    var bsTooltip = $.fn.tooltip;
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js" type="text/javascript"></script>
<script>

// Initialize tooltips
bsTooltip.call( $( "[data-toggle='tooltip']" ) );

var editDepartment = <?php echo json_encode($editDesignationPermission); ?>;

// Zoom in effect Zoom out effect
    scrHeight = $(window).height();
    scrHeight = scrHeight - 280;

    $('#chartOrganization').css('height', scrHeight);
    $('#chartTree').css('height', scrHeight);

$(document).on('click', '#full_view', function () {
    $('#chartOrganization').removeClass('col-md-6');
    $('#chartOrganization').addClass('col-md-12');
    $('#chartTree').hide();

    $('#full_view').hide();
    $('#resize').show();

    screenHeight = $(window).height();
    screenHeight = screenHeight - 280;
    $('#chartDiv').css('height', screenHeight);

});

$(document).on('click', '#resize', function () {
    $('#chartOrganization').removeClass('col-md-12');
    $('#chartOrganization').addClass('col-md-6');
    $('#chartTree').show();

    $('#resize').hide();
    $('#full_view').show();
    $('#chartDiv').css('height', '100%');
});

// search filter
$('#search-text-field').on('change keyup',
    function() {
        if ($('#search-text-field').val() != "") {
            $('#reset-filters').removeClass('d-none');
            searchText();
        } else {
            $('#reset-filters').addClass('d-none');
            searchText();
        }
    });

        $('#reset-filters').click(function() {
            $('#filter-form')[0].reset();
            $('.filter-box .select-picker').selectpicker("refresh");
            $('#reset-filters').addClass('d-none');
            searchText();

        });

 function searchText(){
        let srchText = $('#search-text-field').val();

        $.ajax({
            url: "{{ route('designation.srchFilter') }}",
            type: "POST",
            data: {
                "_token": "{{ csrf_token() }}",
                "searchText": srchText
            },
            success: function(data) {
                if(data.status == 'success')
                {
                    $('#chartTree').html(data.html);
                    $('#chartOrganization').html(data.organizational);
                    $(function() { DragAndDrop.enable("#dragRoot"); });
                }
            }
        });
    }

// Collapse tree by node

$(document).on('click', '#dragRoot', function(e) {
              $('#node-ul-'+e.target.id).toggle();
});

// On drag reduce the opacity of the node and on drop restore the opacity

$(document).on('mouseup', 'body', function(e) {
    $('.node-cpe').addClass('unsetOpacity');
    setTimeout(() => {
        $('.node-cpe').removeClass('setOpacity');
    }, 100);
});

// initialize draggable
        var DragAndDrop = (function(DragAndDrop) {

            function shouldAcceptDrop(item) {
                $('.node-cpe').removeClass('unsetOpacity');
                item[0].classList.add('setOpacity');
                var $target = $(this).closest("li");
                var $item = item.closest("li");

                if ($.contains($item[0], $target[0])) {
                    // can't drop on one of your children!
                    return false;
                }

                return true;

            }

            function itemOver(event, ui) {}

            function itemOut(event, ui) {}

            function itemDropped(event, ui) {
                var parent_id  = event.target.id;

                if (parent_id == 'NewNode') {
                    var checkExits = setInterval(() => {
                        if ($('#node-ul-NewNode').length) {
                            $('#node-ul-NewNode').children('li').each(function() {
                                $newParent = $(this).val();
                            });

                            $.ajax({
                                url: "{{ route('designation.changeParent') }}",
                                type: "POST",
                                data: {
                                    "_token": "{{ csrf_token() }}",
                                    "newParent": $newParent,
                                },
                                success: function(data) {
                                    if(data.status == 'success')
                                    {
                                        $('#chartTree').html(data.html);
                                        $('#chartOrganization').html(data.organizational);
                                        $(function() { DragAndDrop.enable("#dragRoot"); });
                                    }
                                }
                            });

                            clearInterval(checkExits)
                        }
                    }, 500);

                }


                var $target = $(this).closest("li");
                var $item = ui.draggable.closest("li");

                var $srcUL = $item.parent("ul");
                var $dstUL = $target.children("ul").first();

                // destination may not have a UL yet
                if ($dstUL.length == 0) {
                    $dstUL = $("<ul id='node-ul-"+parent_id+"'></ul>");
                    $target.append($dstUL);
                }

                $item.slideUp(50, function() {

                    $dstUL.append($item);

                    if ($srcUL.children("li").length == 0) {
                        $srcUL.remove();
                    }

                    $item.slideDown(50, function() {
                        $item.css('display', '');
                    });

                });

                var checkElementExits = setInterval(() => {
                    var values = [];

                    if(parent_id == 'NewNode')
                    {
                        clearInterval(checkElementExits);
                    }

                    if ($('#node-ul-'+parent_id).length && parent_id != 'NewNode') {
                        $('#node-ul-'+parent_id).children('li').each(function() {
                            values.push($(this).val());
                        });

                    // save the values in designation table
                        $.ajax({
                            url: "{{ route('designation.changeParent') }}",
                            type: "POST",
                            data: {
                                "_token": "{{ csrf_token() }}",
                                "values": values,
                                "parent_id": parent_id
                            },
                            success: function(data) {
                                if(data.status == 'success')
                                {
                                    $('#chartTree').html(data.html);
                                    $('#chartOrganization').html(data.organizational);
                                    $(function() { DragAndDrop.enable("#dragRoot"); });
                                }
                            }
                        });

                        clearInterval(checkElementExits);
                    }
                }, 500);

            }

            DragAndDrop.enable = function(selector) {


                $(selector).find(".node-cpe").draggable({
                    helper: "clone"
                });

                $(selector).find(".node-cpe, .node-facility").droppable({
                    activeClass: "active",
                    hoverClass: "hover",
                    accept: shouldAcceptDrop,
                    over: itemOver,
                    out: itemOut,
                    drop: itemDropped,
                    greedy: true,
                    tolerance: "pointer"
                });

            };

            return DragAndDrop;

        })(DragAndDrop || {});

        if (editDepartment == 'none') {
            $('.node-cpe').css('cursor', 'default');
        }
        else{
            $(function() {
                DragAndDrop.enable("#dragRoot");
            });
        }


</script>
@endpush
