/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, <PERSON>
 * Copyright (c) 2013, salesforce.com
 */
.ql-container {
  box-sizing: border-box;
  font-family: Helvetica, Arial, sans-serif;
  font-size: 13px;
  height: 100%;
  margin: 0px;
  position: relative;
}
.ql-container.ql-disabled .ql-tooltip {
  visibility: hidden;
}
.ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {
  pointer-events: none;
}
.ql-clipboard {
  left: -100000px;
  height: 1px;
  overflow-y: hidden;
  position: absolute;
  top: 50%;
}
.ql-clipboard p {
  margin: 0;
  padding: 0;
}
.ql-editor {
  box-sizing: border-box;
  line-height: 1.42;
  height: 100%;
  outline: none;
  overflow-y: auto;
  padding: 12px 15px;
  -o-tab-size: 4;
     tab-size: 4;
  -moz-tab-size: 4;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.ql-editor > * {
  cursor: text;
}
.ql-editor p,
.ql-editor ol,
.ql-editor ul,
.ql-editor pre,
.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin: 0;
  padding: 0;
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol,
.ql-editor ul {
  padding-left: 1.5em;
}
.ql-editor ol > li,
.ql-editor ul > li {
  list-style-type: none;
}
.ql-editor ul > li::before {
  content: '\2022';
}
.ql-editor ul[data-checked=true],
.ql-editor ul[data-checked=false] {
  pointer-events: none;
}
.ql-editor ul[data-checked=true] > li *,
.ql-editor ul[data-checked=false] > li * {
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li::before,
.ql-editor ul[data-checked=false] > li::before {
  color: #777;
  cursor: pointer;
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li::before {
  content: '\2611';
}
.ql-editor ul[data-checked=false] > li::before {
  content: '\2610';
}
.ql-editor li::before {
  display: inline-block;
  white-space: nowrap;
  width: 1.2em;
}
.ql-editor li:not(.ql-direction-rtl)::before {
  margin-left: -1.5em;
  margin-right: 0.3em;
  text-align: right;
}
.ql-editor li.ql-direction-rtl::before {
  margin-left: 0.3em;
  margin-right: -1.5em;
}
.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
  padding-left: 1.5em;
}
.ql-editor ol li.ql-direction-rtl,
.ql-editor ul li.ql-direction-rtl {
  padding-right: 1.5em;
}
.ql-editor ol li {
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  counter-increment: list-0;
}
.ql-editor ol li:before {
  content: counter(list-0, decimal) '. ';
}
.ql-editor ol li.ql-indent-1 {
  counter-increment: list-1;
}
.ql-editor ol li.ql-indent-1:before {
  content: counter(list-1, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-1 {
  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-2 {
  counter-increment: list-2;
}
.ql-editor ol li.ql-indent-2:before {
  content: counter(list-2, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-2 {
  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-3 {
  counter-increment: list-3;
}
.ql-editor ol li.ql-indent-3:before {
  content: counter(list-3, decimal) '. ';
}
.ql-editor ol li.ql-indent-3 {
  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-4 {
  counter-increment: list-4;
}
.ql-editor ol li.ql-indent-4:before {
  content: counter(list-4, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-4 {
  counter-reset: list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-5 {
  counter-increment: list-5;
}
.ql-editor ol li.ql-indent-5:before {
  content: counter(list-5, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-5 {
  counter-reset: list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-6 {
  counter-increment: list-6;
}
.ql-editor ol li.ql-indent-6:before {
  content: counter(list-6, decimal) '. ';
}
.ql-editor ol li.ql-indent-6 {
  counter-reset: list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-7 {
  counter-increment: list-7;
}
.ql-editor ol li.ql-indent-7:before {
  content: counter(list-7, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-7 {
  counter-reset: list-8 list-9;
}
.ql-editor ol li.ql-indent-8 {
  counter-increment: list-8;
}
.ql-editor ol li.ql-indent-8:before {
  content: counter(list-8, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-8 {
  counter-reset: list-9;
}
.ql-editor ol li.ql-indent-9 {
  counter-increment: list-9;
}
.ql-editor ol li.ql-indent-9:before {
  content: counter(list-9, decimal) '. ';
}
.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 3em;
}
.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 4.5em;
}
.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 3em;
}
.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 4.5em;
}
.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 6em;
}
.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 7.5em;
}
.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 6em;
}
.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 7.5em;
}
.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 9em;
}
.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 10.5em;
}
.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 9em;
}
.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 10.5em;
}
.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 12em;
}
.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 13.5em;
}
.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 12em;
}
.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 13.5em;
}
.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 15em;
}
.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 16.5em;
}
.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 15em;
}
.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 16.5em;
}
.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 18em;
}
.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 19.5em;
}
.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 18em;
}
.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 19.5em;
}
.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 21em;
}
.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 22.5em;
}
.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 21em;
}
.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 22.5em;
}
.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 24em;
}
.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 25.5em;
}
.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 24em;
}
.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 25.5em;
}
.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 27em;
}
.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 28.5em;
}
.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 27em;
}
.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 28.5em;
}
.ql-editor .ql-video {
  display: block;
  max-width: 100%;
}
.ql-editor .ql-video.ql-align-center {
  margin: 0 auto;
}
.ql-editor .ql-video.ql-align-right {
  margin: 0 0 0 auto;
}
.ql-editor .ql-bg-black {
  background-color: #000;
}
.ql-editor .ql-bg-red {
  background-color: #e60000;
}
.ql-editor .ql-bg-orange {
  background-color: #f90;
}
.ql-editor .ql-bg-yellow {
  background-color: #ff0;
}
.ql-editor .ql-bg-green {
  background-color: #008a00;
}
.ql-editor .ql-bg-blue {
  background-color: #06c;
}
.ql-editor .ql-bg-purple {
  background-color: #93f;
}
.ql-editor .ql-color-white {
  color: #fff;
}
.ql-editor .ql-color-red {
  color: #e60000;
}
.ql-editor .ql-color-orange {
  color: #f90;
}
.ql-editor .ql-color-yellow {
  color: #ff0;
}
.ql-editor .ql-color-green {
  color: #008a00;
}
.ql-editor .ql-color-blue {
  color: #06c;
}
.ql-editor .ql-color-purple {
  color: #93f;
}
.ql-editor .ql-font-serif {
  font-family: Georgia, Times New Roman, serif;
}
.ql-editor .ql-font-monospace {
  font-family: Monaco, Courier New, monospace;
}
.ql-editor .ql-size-small {
  font-size: 0.75em;
}
.ql-editor .ql-size-large {
  font-size: 1.5em;
}
.ql-editor .ql-size-huge {
  font-size: 2.5em;
}
.ql-editor .ql-direction-rtl {
  direction: rtl;
  text-align: inherit;
}
.ql-editor .ql-align-center {
  text-align: center;
}
.ql-editor .ql-align-justify {
  text-align: justify;
}
.ql-editor .ql-align-right {
  text-align: right;
}
.ql-editor.ql-blank::before {
  color: rgba(0,0,0,0.6);
  content: attr(data-placeholder);
  font-style: italic;
  left: 15px;
  pointer-events: none;
  position: absolute;
  right: 15px;
}
.ql-snow.ql-toolbar:after,
.ql-snow .ql-toolbar:after {
  clear: both;
  content: '';
  display: table;
}
.ql-snow.ql-toolbar button,
.ql-snow .ql-toolbar button {
  background: none;
  border: none;
  cursor: pointer;
  display: inline-block;
  float: left;
  height: 24px;
  padding: 3px 5px;
  width: 28px;
}
.ql-snow.ql-toolbar button svg,
.ql-snow .ql-toolbar button svg {
  float: left;
  height: 100%;
}
.ql-snow.ql-toolbar button:active:hover,
.ql-snow .ql-toolbar button:active:hover {
  outline: none;
}
.ql-snow.ql-toolbar input.ql-image[type=file],
.ql-snow .ql-toolbar input.ql-image[type=file] {
  display: none;
}
.ql-snow.ql-toolbar button:hover,
.ql-snow .ql-toolbar button:hover,
.ql-snow.ql-toolbar button:focus,
.ql-snow .ql-toolbar button:focus,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  color: #06c;
}
.ql-snow.ql-toolbar button:hover .ql-fill,
.ql-snow .ql-toolbar button:hover .ql-fill,
.ql-snow.ql-toolbar button:focus .ql-fill,
.ql-snow .ql-toolbar button:focus .ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: #06c;
}
.ql-snow.ql-toolbar button:hover .ql-stroke,
.ql-snow .ql-toolbar button:hover .ql-stroke,
.ql-snow.ql-toolbar button:focus .ql-stroke,
.ql-snow .ql-toolbar button:focus .ql-stroke,
.ql-snow.ql-toolbar button.ql-active .ql-stroke,
.ql-snow .ql-toolbar button.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow.ql-toolbar button:hover .ql-stroke-miter,
.ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.ql-snow.ql-toolbar button:focus .ql-stroke-miter,
.ql-snow .ql-toolbar button:focus .ql-stroke-miter,
.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: #06c;
}
@media (pointer: coarse) {
  .ql-snow.ql-toolbar button:hover:not(.ql-active),
  .ql-snow .ql-toolbar button:hover:not(.ql-active) {
    color: #444;
  }
  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,
  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,
  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,
  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {
    fill: #444;
  }
  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,
  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,
  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,
  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {
    stroke: #444;
  }
}
.ql-snow {
  box-sizing: border-box;
}
.ql-snow * {
  box-sizing: border-box;
}
.ql-snow .ql-hidden {
  display: none;
}
.ql-snow .ql-out-bottom,
.ql-snow .ql-out-top {
  visibility: hidden;
}
.ql-snow .ql-tooltip {
  position: absolute;
  transform: translateY(10px);
}
.ql-snow .ql-tooltip a {
  cursor: pointer;
  text-decoration: none;
}
.ql-snow .ql-tooltip.ql-flip {
  transform: translateY(-10px);
}
.ql-snow .ql-formats {
  display: inline-block;
  vertical-align: middle;
}
.ql-snow .ql-formats:after {
  clear: both;
  content: '';
  display: table;
}
.ql-snow .ql-stroke {
  fill: none;
  stroke: #444;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}
.ql-snow .ql-stroke-miter {
  fill: none;
  stroke: #444;
  stroke-miterlimit: 10;
  stroke-width: 2;
}
.ql-snow .ql-fill,
.ql-snow .ql-stroke.ql-fill {
  fill: #444;
}
.ql-snow .ql-empty {
  fill: none;
}
.ql-snow .ql-even {
  fill-rule: evenodd;
}
.ql-snow .ql-thin,
.ql-snow .ql-stroke.ql-thin {
  stroke-width: 1;
}
.ql-snow .ql-transparent {
  opacity: 0.4;
}
.ql-snow .ql-direction svg:last-child {
  display: none;
}
.ql-snow .ql-direction.ql-active svg:last-child {
  display: inline;
}
.ql-snow .ql-direction.ql-active svg:first-child {
  display: none;
}
.ql-snow .ql-editor h1 {
  font-size: 2em;
}
.ql-snow .ql-editor h2 {
  font-size: 1.5em;
}
.ql-snow .ql-editor h3 {
  font-size: 1.17em;
}
.ql-snow .ql-editor h4 {
  font-size: 1em;
}
.ql-snow .ql-editor h5 {
  font-size: 0.83em;
}
.ql-snow .ql-editor h6 {
  font-size: 0.67em;
}
.ql-snow .ql-editor a {
  text-decoration: underline;
}
.ql-snow .ql-editor blockquote {
  border-left: 4px solid #ccc;
  margin-bottom: 5px;
  margin-top: 5px;
  padding-left: 16px;
}
.ql-snow .ql-editor code,
.ql-snow .ql-editor pre {
  background-color: #f0f0f0;
  border-radius: 3px;
}
.ql-snow .ql-editor pre {
  white-space: pre-wrap;
  margin-bottom: 5px;
  margin-top: 5px;
  padding: 5px 10px;
}
.ql-snow .ql-editor code {
  font-size: 85%;
  padding: 2px 4px;
}
.ql-snow .ql-editor pre.ql-syntax {
  background-color: #23241f;
  color: #f8f8f2;
  overflow: visible;
}
.ql-snow .ql-editor img {
  max-width: 100%;
}
.ql-snow .ql-picker {
  color: #444;
  display: inline-block;
  float: left;
  font-size: 14px;
  font-weight: 500;
  height: 24px;
  position: relative;
  vertical-align: middle;
}
.ql-snow .ql-picker-label {
  cursor: pointer;
  display: inline-block;
  height: 100%;
  padding-left: 8px;
  padding-right: 2px;
  position: relative;
  width: 100%;
}
.ql-snow .ql-picker-label::before {
  display: inline-block;
  line-height: 22px;
}
.ql-snow .ql-picker-options {
  background-color: #fff;
  display: none;
  min-width: 100%;
  padding: 4px 8px;
  position: absolute;
  white-space: nowrap;
}
.ql-snow .ql-picker-options .ql-picker-item {
  cursor: pointer;
  display: block;
  padding-bottom: 5px;
  padding-top: 5px;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: #ccc;
  z-index: 2;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {
  fill: #ccc;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
  stroke: #ccc;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  display: block;
  margin-top: -1px;
  top: 100%;
  z-index: 1;
}
.ql-snow .ql-color-picker,
.ql-snow .ql-icon-picker {
  width: 28px;
}
.ql-snow .ql-color-picker .ql-picker-label,
.ql-snow .ql-icon-picker .ql-picker-label {
  padding: 2px 4px;
}
.ql-snow .ql-color-picker .ql-picker-label svg,
.ql-snow .ql-icon-picker .ql-picker-label svg {
  right: 4px;
}
.ql-snow .ql-icon-picker .ql-picker-options {
  padding: 4px 0px;
}
.ql-snow .ql-icon-picker .ql-picker-item {
  height: 24px;
  width: 24px;
  padding: 2px 4px;
}
.ql-snow .ql-color-picker .ql-picker-options {
  padding: 3px 5px;
  width: 152px;
}
.ql-snow .ql-color-picker .ql-picker-item {
  border: 1px solid transparent;
  float: left;
  height: 16px;
  margin: 2px;
  padding: 0px;
  width: 16px;
}
.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  position: absolute;
  margin-top: -9px;
  right: 0;
  top: 50%;
  width: 18px;
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=''])::before {
  content: attr(data-label);
}
.ql-snow .ql-picker.ql-header {
  width: 98px;
}
.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: 'Normal';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: 'Heading 1';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: 'Heading 2';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: 'Heading 3';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: 'Heading 4';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: 'Heading 5';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: 'Heading 6';
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  font-size: 2em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  font-size: 1.5em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  font-size: 1.17em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  font-size: 1em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  font-size: 0.83em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  font-size: 0.67em;
}
.ql-snow .ql-picker.ql-font {
  width: 108px;
}
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: 'Sans Serif';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  content: 'Serif';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  content: 'Monospace';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  font-family: Georgia, Times New Roman, serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  font-family: Monaco, Courier New, monospace;
}
.ql-snow .ql-picker.ql-size {
  width: 98px;
}
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: 'Normal';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  content: 'Small';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  content: 'Large';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  content: 'Huge';
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  font-size: 10px;
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  font-size: 18px;
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  font-size: 32px;
}
.ql-snow .ql-color-picker.ql-background .ql-picker-item {
  background-color: #fff;
}
.ql-snow .ql-color-picker.ql-color .ql-picker-item {
  background-color: #000;
}
.ql-toolbar.ql-snow {
  border: 1px solid #ccc;
  box-sizing: border-box;
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  padding: 8px;
}
.ql-toolbar.ql-snow .ql-formats {
  margin-right: 15px;
}
.ql-toolbar.ql-snow .ql-picker-label {
  border: 1px solid transparent;
}
.ql-toolbar.ql-snow .ql-picker-options {
  border: 1px solid transparent;
  box-shadow: rgba(0,0,0,0.2) 0 2px 8px;
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: #ccc;
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  border-color: #ccc;
}
.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,
.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover {
  border-color: #000;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border-top: 0px;
}
.ql-snow .ql-tooltip {
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0px 0px 5px #ddd;
  color: #444;
  padding: 5px 12px;
  white-space: nowrap;
}
.ql-snow .ql-tooltip::before {
  content: "Visit URL:";
  line-height: 26px;
  margin-right: 8px;
}
.ql-snow .ql-tooltip input[type=text] {
  display: none;
  border: 1px solid #ccc;
  font-size: 13px;
  height: 26px;
  margin: 0px;
  padding: 3px 5px;
  width: 170px;
}
.ql-snow .ql-tooltip a.ql-preview {
  display: inline-block;
  max-width: 200px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
}
.ql-snow .ql-tooltip a.ql-action::after {
  border-right: 1px solid #ccc;
  content: 'Edit';
  margin-left: 16px;
  padding-right: 8px;
}
.ql-snow .ql-tooltip a.ql-remove::before {
  content: 'Remove';
  margin-left: 8px;
}
.ql-snow .ql-tooltip a {
  line-height: 26px;
}
.ql-snow .ql-tooltip.ql-editing a.ql-preview,
.ql-snow .ql-tooltip.ql-editing a.ql-remove {
  display: none;
}
.ql-snow .ql-tooltip.ql-editing input[type=text] {
  display: inline-block;
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: 'Save';
  padding-right: 0px;
}
.ql-snow .ql-tooltip[data-mode=link]::before {
  content: "Enter link:";
}
.ql-snow .ql-tooltip[data-mode=formula]::before {
  content: "Enter formula:";
}
.ql-snow .ql-tooltip[data-mode=video]::before {
  content: "Enter video:";
}
.ql-snow a {
  color: #06c;
}
.ql-container.ql-snow {
  border: 1px solid #ccc;
}

#quill-editor{position:relative}.mention{color:#0366d6}.completions{background:#fff;border-radius:2px;box-shadow:2px 2px 2px rgba(0,0,0,.25);list-style:none}.completions,.completions>li{margin:0;padding:0}.completions>li>button{background:none;border:none;box-sizing:border-box;display:block;height:2em;margin:0;padding:.25em .5em;text-align:left;width:100%}.completions>li>button:hover{background:#ddd}.completions>li>button:focus{background:#ddd;outline:none}.completions>li>button>.matched{color:#000;font-weight:700}.completions>li>button>*{vertical-align:middle}.emoji_completions{background:#fff;border:1px solid rgba(0,0,0,.15);border-radius:3px;box-shadow:0 5px 10px rgba(0,0,0,.12);list-style:none;margin:0;padding:6px}.emoji_completions li{display:inline-block;margin:2px 0;padding:0}.emoji_completions li:not(:last-of-type){margin-right:3px}.emoji_completions>li>button{background:#efefef;border:none;border-radius:3px;box-sizing:border-box;display:block;margin:0;padding:3px 2px 6px;text-align:left;width:100%}.emoji_completions>li>button:hover{background:#2d9ee0;color:#fff}.emoji_completions>li>button:focus{background:#2d9ee0;color:#fff;outline:none}.emoji_completions>li>button.emoji-active{background:red;background:#2d9ee0;color:#fff;outline:none}.emoji_completions>li>button>.matched{font-weight:700}.emoji_completions>li>button>*,.ico{vertical-align:middle}.ico{font-size:18px;line-height:0;margin-right:5px}#emoji-palette{border:1px solid rgba(0,0,0,.15);border-radius:3px;box-shadow:0 5px 10px rgba(0,0,0,.12);max-width:250px;position:absolute;z-index:999}.bem{cursor:pointer;display:inline-block;font-size:24px;margin:2px;text-align:center;width:34px}#tab-filters{margin:20px auto 0;width:210px}.emoji-tab{cursor:pointer;display:inline-table;height:100%;min-height:30px;text-align:center;width:30px}#tab-toolbar{background-color:#f7f7f7;border-bottom:1px solid rgba(0,0,0,.15);padding:4px 4px 0}#tab-toolbar ul{margin:0;padding:0}#tab-toolbar .active{border-bottom:3px solid #2ab27b}#tab-panel{background:#fff;display:flex;flex-wrap:wrap;justify-content:center;max-height:220px;overflow-y:scroll;padding:2px}#quill-editor x-contain,contain{background:#fb8;display:block}#quill-editor table{border-collapse:collapse;width:100%}#quill-editor table td{border:1px solid #000;height:25px;padding:5px}.ql-picker.ql-table .ql-picker-label:before,button.ql-table:after{content:"TABLE"}button.ql-contain:after{content:"WRAP"}button.ql-table[value=append-row]:after{content:"ROWS+"}button.ql-table[value=append-col]:after{content:"COLS+"}.ql-contain,.ql-table{margin-right:-15px;width:auto!important}#emoji-close-div{height:100%;left:0;position:fixed;top:0;width:100%}.textarea-emoji-control{height:25px;right:4px;top:10px;width:25px}#textarea-emoji{border:1px solid #66afe9;border:1px solid rgba(0,0,0,.15);border-radius:3px;box-shadow:0 5px 10px rgba(0,0,0,.12);max-width:250px;position:absolute;right:0;z-index:999}.ql-editor{padding-right:26px}.i-activity{background:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 40 40"><g fill="none" fill-rule="evenodd"><g fill="%236F6D70"><g transform="translate(7.500000, 7.500000)"><path stroke="%236F6D70" d="M18.02 1.36c5.92 3.02 8.28 10.26 5.26 16.18-2.12 4.17-6.35 6.57-10.73 6.57-1.83 0-3.7-.4-5.45-1.3-5.9-3-8.27-10.22-5.25-16.2C3.97 2.5 8.2.1 12.57.1c1.84 0 3.7.42 5.45 1.3zm4.7 11.44c.1-1.3-.06-2.6-.47-3.87-.13-.38-.27-.75-.43-1.1l-3.42-1.6-1.57-3.4c-.62-.3-1.27-.5-1.92-.68-.7-.18-1.5-.27-2.3-.27-.4 0-.8.02-1.2.06L8.9 4.74l-3.74.43c-.63.68-1.16 1.45-1.6 2.28-.42.84-.72 1.72-.9 2.63l1.84 3.3-.74 3.68c.3.56.66 1.08 1.1 1.58.76.94 1.7 1.7 2.8 2.32l3.7-.74 3.26 1.84c1.13-.23 2.23-.65 3.24-1.26.6-.35 1.2-.77 1.7-1.24l.44-3.74 2.78-2.55.05-.47z" stroke-linecap="round" stroke-linejoin="round"/><polygon points="10.6158689 8.50666885 8.42649168 12.8046921 11.836847 16.2129328 16.1342124 14.0235556 15.3793892 9.26144504"/></g></g></g></svg>')}.i-activity,.i-flags{content:"";height:25px;margin:auto;width:25px}.i-flags{background:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 40 40"><g fill="none" fill-rule="evenodd"><g fill="%236F6D70" fill-rule="nonzero"><g transform="translate(9.500000, 8.000000)"><path d="M.5 3.13V23.5c0 .83.68 1.5 1.5 1.5.84 0 1.5-.67 1.5-1.5V3.14c0-.83-.66-1.5-1.5-1.5-.82 0-1.5.67-1.5 1.5z"/><path d="M3.5 11.54c.7-.16 1.44-.22 2.25-.17 1.38.07 2.48.3 5.23 1.04l.55.2c3.02.8 4.77 1 5.96.67v-7.9c-1.7.33-3.8-.07-7.1-1-3.9-1.1-5.7-1.3-6.9-.5v7.7zm7.68-10.1c4.1 1.15 5.7 1.3 6.98.44 1-.66 2.33.05 2.33 1.25v11c0 .5-.3 1-.7 1.26-2.2 1.4-4.6 1.2-9.1 0l-.56-.16c-4.54-1.2-6.15-1.3-7.05-.2-.9 1.06-2.65.42-2.65-.98v-11c0-.4.2-.8.5-1.1C3.4-.24 5.75-.1 11.2 1.4z"/></g></g></g></svg>')}.i-food{background:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 40 40"><g fill="none" fill-rule="evenodd"><g fill="%236F6D70"><path fill-rule="nonzero" d="M9.57 28.2c0 .28.22.5.5.5h19.2c.27 0 .5-.22.5-.5v-4.4H9.57v4.4zm23.2-3.06v3.07c0 1.95-1.57 3.5-3.5 3.5h-19.2c-1.93 0-3.5-1.55-3.5-3.5V25c.46.15.96.24 1.47.24h23.78c.33 0 .64-.04.94-.1z"/><path fill-rule="nonzero" d="M6.57 18.2v-3.45c0-3.56 2.9-6.45 6.45-6.45h13.3c3.55 0 6.44 2.9 6.44 6.45v3.45H6.56zm3-1.83h3.6l.4.86c.23.5.73.83 1.3.83.56 0 1.06-.33 1.3-.83l.4-.86h13.2v-1.62c0-1.9-1.56-3.45-3.45-3.45h-13.3c-1.9 0-3.45 1.55-3.45 3.45v1.62z"/><path fill-rule="nonzero" d="M13.23 16.37l.4.86c.24.5.74.83 1.3.83.57 0 1.07-.33 1.3-.83l.4-.86H31.9c2.44 0 4.43 1.98 4.43 4.43 0 2.45-1.98 4.44-4.44 4.44H8.1c-2.44 0-4.43-2-4.43-4.44 0-2.45 1.98-4.43 4.44-4.43h5.14zm-5.12 3c-.8 0-1.42.64-1.42 1.43 0 .8.64 1.44 1.44 1.44h23.8c.8 0 1.43-.64 1.43-1.44 0-.8-.64-1.43-1.44-1.43H18.4c-.83 1.04-2.1 1.7-3.5 1.7-1.37 0-2.65-.66-3.47-1.7H8.1z"/><circle cx="14.6682646" cy="13.75" r="1"/><circle cx="24.6682646" cy="13.75" r="1"/><circle cx="19.6682646" cy="13.75" r="1"/></g></g></svg>')}.i-food,.i-nature{content:"";height:25px;margin:auto;width:25px}.i-nature{background:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 40 40"><g fill="none" fill-rule="evenodd"><g fill="%236F6D70" fill-rule="nonzero"><path d="M15.96 18.26L30.86 32c.5.46 1.3.43 1.77-.08.46-.5.43-1.3-.08-1.76l-14.9-13.74c-.5-.46-1.3-.43-1.76.08-.5.5-.5 1.3 0 1.76z"/><path d="M18.17 21.28c-.7-.06-1.3.45-1.35 1.14-.06.7.45 1.3 1.13 1.35l4.96.43c.9.07 1.5-.66 1.4-1.47l-1-5.6c-.1-.7-.74-1.14-1.42-1.02-.67.2-1.12.8-1 1.5l.7 4-3.32-.3z"/><path d="M28.48 28.95c-.38.17-1 .4-1.85.64-2.92.7-6 .9-8.95-.2-5.98-2.17-9.8-8.5-10.54-19.9l-.1-1.4 1.38-.2c14.45-2.08 23.4 7.4 21.33 19.85l-1.9-.3.63 1.43zM10.24 10.77C11.12 20.14 14.2 25 18.7 26.6c2.27.83 4.76.74 7.14.1.4-.12.76-.23 1.07-.35 1.2-9.6-5.4-16.57-16.6-15.58z"/></g></g></svg>')}.i-objects{background:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 40 40"><g fill="none" fill-rule="evenodd"><g fill="%236F6D70" fill-rule="nonzero"><path d="M11.04 16.7c0-4.85 4.02-8.76 8.96-8.76 4.94 0 8.96 3.9 8.96 8.76 0 2.54-1.12 4.9-3 6.54v1.87c0 1.28-1.02 2.27-2.26 2.27h-7.37c-1.23 0-2.25-1-2.25-2.22V23.3c-1.9-1.65-3.04-4-3.04-6.58zm11.9 5.82c0-.48.24-.93.63-1.22 1.5-1.08 2.4-2.77 2.4-4.6 0-3.17-2.67-5.76-5.97-5.76s-5.96 2.6-5.96 5.76c0 1.84.9 3.54 2.42 4.62.4.28.62.74.62 1.22v1.8h5.87V22.5z"/><path d="M21.76 28.78c-.22.05-.42.1-.62.13-.5.1-.9.2-1.1.2-.24 0-.62-.04-1.08-.12l-.74-.15-.08-.02v-2.93c0-.83-.68-1.5-1.5-1.5-.83 0-1.5.67-1.5 1.5v4.1c0 .68.44 1.27 1.1 1.45l.38.1.94.23c.3.1.6.15.87.2.62.1 1.16.17 1.6.17.47 0 1.03-.1 1.7-.2l.7-.17.95-.22c.18-.03.32-.1.4-.1.64-.2 1.08-.76 1.08-1.43v-4.1c0-.83-.67-1.5-1.5-1.5-.82 0-1.5.67-1.5 1.5v2.9c-.03 0-.07 0-.1.02z"/></g></g></svg>')}.i-objects,.i-people{content:"";height:25px;margin:auto;width:25px}.i-people{background:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 40 40"><g fill="none" fill-rule="evenodd"><g fill="%236F6D70"><path fill-rule="nonzero" d="M20 34c-7.73 0-14-6.27-14-14S12.27 6 20 6s14 6.27 14 14-6.27 14-14 14zm0-3c6.08 0 11-4.92 11-11S26.08 9 20 9 9 13.92 9 20s4.92 11 11 11z"/><circle cx="15.3474348" cy="16.7705459" r="2.34743481"/><circle cx="24.4703784" cy="16.7705459" r="2.34743481"/><path d="M20 27.9c2.7 0 4.88-2.18 4.88-4.88 0-2.7-9.76-2.7-9.76 0S17.3 27.9 20 27.9z"/></g></g></svg>')}.i-symbols{background:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 40 40"><g fill="none" fill-rule="evenodd"><g fill="%236F6D70" fill-rule="nonzero"><path d="M15.37 7.95c-4.48 0-8.06 3.9-8.06 8.64 0 3.5 2.2 6.9 5.8 10.3 1.2 1.1 2.5 2.2 3.9 3.1.84.6 1.5 1 1.98 1.3l.27.15.8.5 1.1-.6c.5-.27 1.18-.7 2-1.25 1.34-.9 2.66-1.9 3.9-3 3.57-3.28 5.75-6.8 5.75-10.6 0-4.74-3.6-8.65-8.1-8.65v3.3c2.6 0 4.76 2.4 4.76 5.35 0 2.65-1.72 5.43-4.7 8.13-1.1 1-2.27 1.9-3.5 2.7-.43.3-.83.54-1.17.74-.35-.2-.76-.5-1.2-.83-1.24-.87-2.4-1.83-3.54-2.87-2.95-2.76-4.7-5.5-4.7-7.9 0-2.98 2.2-5.35 4.78-5.35 1.3 0 2.5.6 3.4 1.6L20 14.3l1.25-1.43c.9-1.03 2.1-1.6 3.38-1.6v-3.3c-1.68 0-3.3.56-4.63 1.57-1.34-1-2.95-1.57-4.63-1.57z"/></g></g></svg>')}.i-symbols,.i-travel{content:"";height:25px;margin:auto;width:25px}.i-travel{background:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 40 40"><g fill="none" fill-rule="evenodd"><g fill="%236F6D70" fill-rule="nonzero"><path d="M25.46 11.2s-2.67 2.58-3.94 3.95l-10.6-2.13c-.12-.02-.25.04-.3.15l-.8 1.6c-.07.13 0 .3.12.37l7.75 3.88L13.4 24c-.5-.16-1.1-.33-1.66-.3-.3 0-.6.06-.85.25-.3.2-.4.5-.4.9s.1.74.3.98l3.2 3.23c.3.23.7.34 1 .34.4 0 .7-.13.9-.37.2-.23.24-.53.25-.84 0-.6-.15-1.2-.3-1.7l4.97-4.3 3.9 7.76c.06.13.23.2.36.12l1.6-.8c.13-.07.2-.2.17-.3l-2.12-10.6c1.4-1.28 3.95-3.95 3.96-3.96.86-.88 1.4-1.93 1.4-2.87 0-.5-.17-1-.5-1.33-.37-.36-.87-.5-1.38-.5-.95 0-2 .52-2.88 1.4zm2.87-4.4c1.28 0 2.54.44 3.5 1.4.93.93 1.38 2.2 1.38 3.47 0 1.8-.8 3.54-2.2 4.94-.4.5-1.7 1.8-2.8 2.9l1.8 9c.3 1.5-.4 2.9-1.7 3.6l-1.62.8c-1.62.8-3.6.1-4.36-1.4L20 27.1l-.7.6v.62c-.03.92-.28 1.8-.92 2.6-.8 1-1.98 1.5-3.22 1.5-1.03 0-2.12-.37-2.96-1.1l-.16-.14-3.22-3.22-.1-.12c-.75-.83-1.12-1.9-1.12-3 0-1.24.5-2.43 1.48-3.22.8-.6 1.68-.9 2.62-.9h.62l.6-.7-4.27-2.1c-1.65-.8-2.33-2.8-1.52-4.4l.8-1.64c.67-1.3 2.14-2.02 3.57-1.73l9 1.8 1.36-1.33 1.5-1.48c1.42-1.4 3.17-2.27 4.97-2.27z"/></g></g></svg>')}.button-emoji{margin-bottom:-5px}.ql-emojiblot{display:inline-block;vertical-align:text-top}.ap{background-image:url(1e7b63404cd2fb8e6525b2fd4ee4d286.png);background-repeat:no-repeat;background-size:820px;box-sizing:border-box;display:inline-flex;font-size:20px;height:20px;line-height:1;margin-top:-3px;overflow:hidden;text-indent:-999px;width:20px}.ap-copyright{background-position:0 0}.ap-registered{background-position:0 -20px}.ap-bangbang{background-position:0 -40px}.ap-interrobang{background-position:0 -60px}.ap-tm{background-position:0 -80px}.ap-information_source{background-position:0 -100px}.ap-left_right_arrow{background-position:0 -120px}.ap-arrow_up_down{background-position:0 -140px}.ap-arrow_upper_left{background-position:0 -160px}.ap-arrow_upper_right{background-position:0 -180px}.ap-arrow_lower_right{background-position:0 -200px}.ap-arrow_lower_left{background-position:0 -220px}.ap-leftwards_arrow_with_hook{background-position:0 -240px}.ap-arrow_right_hook{background-position:0 -260px}.ap-watch{background-position:0 -280px}.ap-hourglass{background-position:0 -300px}.ap-keyboard{background-position:0 -320px}.ap-fast_forward{background-position:0 -360px}.ap-rewind{background-position:0 -380px}.ap-arrow_double_up{background-position:0 -400px}.ap-arrow_double_down{background-position:0 -420px}.ap-black_right_pointing_double_triangle_with_vertical_bar{background-position:0 -440px}.ap-black_left_pointing_double_triangle_with_vertical_bar{background-position:0 -460px}.ap-black_right_pointing_triangle_with_double_vertical_bar{background-position:0 -480px}.ap-alarm_clock{background-position:0 -500px}.ap-stopwatch{background-position:0 -520px}.ap-timer_clock{background-position:0 -540px}.ap-hourglass_flowing_sand{background-position:0 -560px}.ap-double_vertical_bar{background-position:0 -580px}.ap-black_square_for_stop{background-position:0 -600px}.ap-black_circle_for_record{background-position:0 -620px}.ap-m{background-position:0 -640px}.ap-black_small_square{background-position:0 -660px}.ap-white_small_square{background-position:0 -680px}.ap-arrow_forward{background-position:0 -700px}.ap-arrow_backward{background-position:0 -720px}.ap-white_medium_square{background-position:0 -740px}.ap-black_medium_square{background-position:0 -760px}.ap-white_medium_small_square{background-position:0 -780px}.ap-black_medium_small_square{background-position:0 -800px}.ap-sunny{background-position:-20px 0}.ap-cloud{background-position:-20px -20px}.ap-umbrella{background-position:-20px -40px}.ap-snowman{background-position:-20px -60px}.ap-comet{background-position:-20px -80px}.ap-phone,.ap-telephone{background-position:-20px -100px}.ap-ballot_box_with_check{background-position:-20px -120px}.ap-umbrella_with_rain_drops{background-position:-20px -140px}.ap-coffee{background-position:-20px -160px}.ap-shamrock{background-position:-20px -180px}.ap-point_up{background-position:-20px -200px}.ap-skull_and_crossbones{background-position:-20px -320px}.ap-radioactive_sign{background-position:-20px -340px}.ap-biohazard_sign{background-position:-20px -360px}.ap-orthodox_cross{background-position:-20px -380px}.ap-star_and_crescent{background-position:-20px -400px}.ap-peace_symbol{background-position:-20px -420px}.ap-yin_yang{background-position:-20px -440px}.ap-wheel_of_dharma{background-position:-20px -460px}.ap-white_frowning_face{background-position:-20px -480px}.ap-relaxed{background-position:-20px -500px}.ap-aries{background-position:-20px -520px}.ap-taurus{background-position:-20px -540px}.ap-gemini{background-position:-20px -560px}.ap-cancer{background-position:-20px -580px}.ap-leo{background-position:-20px -600px}.ap-virgo{background-position:-20px -620px}.ap-libra{background-position:-20px -640px}.ap-scorpius{background-position:-20px -660px}.ap-sagittarius{background-position:-20px -680px}.ap-capricorn{background-position:-20px -700px}.ap-aquarius{background-position:-20px -720px}.ap-pisces{background-position:-20px -740px}.ap-spades{background-position:-20px -760px}.ap-clubs{background-position:-20px -780px}.ap-hearts{background-position:-20px -800px}.ap-diamonds{background-position:-40px 0}.ap-hotsprings{background-position:-40px -20px}.ap-recycle{background-position:-40px -40px}.ap-wheelchair{background-position:-40px -60px}.ap-hammer_and_pick{background-position:-40px -80px}.ap-anchor{background-position:-40px -100px}.ap-crossed_swords{background-position:-40px -120px}.ap-scales{background-position:-40px -140px}.ap-alembic{background-position:-40px -160px}.ap-gear{background-position:-40px -180px}.ap-atom_symbol{background-position:-40px -200px}.ap-fleur_de_lis{background-position:-40px -220px}.ap-warning{background-position:-40px -240px}.ap-zap{background-position:-40px -260px}.ap-white_circle{background-position:-40px -280px}.ap-black_circle{background-position:-40px -300px}.ap-coffin{background-position:-40px -320px}.ap-funeral_urn{background-position:-40px -340px}.ap-soccer{background-position:-40px -360px}.ap-baseball{background-position:-40px -380px}.ap-snowman_without_snow{background-position:-40px -400px}.ap-partly_sunny{background-position:-40px -420px}.ap-thunder_cloud_and_rain{background-position:-40px -440px}.ap-ophiuchus{background-position:-40px -460px}.ap-pick{background-position:-40px -480px}.ap-helmet_with_white_cross{background-position:-40px -500px}.ap-chains{background-position:-40px -520px}.ap-no_entry{background-position:-40px -540px}.ap-shinto_shrine{background-position:-40px -560px}.ap-church{background-position:-40px -580px}.ap-mountain{background-position:-40px -600px}.ap-beach_umbrella,.ap-umbrella_on_ground{background-position:-40px -620px}.ap-fountain{background-position:-40px -640px}.ap-golf{background-position:-40px -660px}.ap-ferry{background-position:-40px -680px}.ap-boat{background-position:-40px -700px}.ap-skier{background-position:-40px -720px}.ap-ice_skate{background-position:-40px -740px}.ap-person_with_ball{background-position:-40px -760px}.ap-tent{background-position:-60px -60px}.ap-fuelpump{background-position:-60px -80px}.ap-scissors{background-position:-60px -100px}.ap-white_check_mark{background-position:-60px -120px}.ap-airplane{background-position:-60px -140px}.ap-email{background-position:-60px -160px}.ap-fist{background-position:-60px -180px}.ap-hand{background-position:-60px -300px}.ap-v{background-position:-60px -420px}.ap-writing_hand{background-position:-60px -540px}.ap-pencil2{background-position:-60px -660px}.ap-black_nib{background-position:-60px -680px}.ap-heavy_check_mark{background-position:-60px -700px}.ap-heavy_multiplication_x{background-position:-60px -720px}.ap-latin_cross{background-position:-60px -740px}.ap-star_of_david{background-position:-60px -760px}.ap-sparkles{background-position:-60px -780px}.ap-eight_spoked_asterisk{background-position:-60px -800px}.ap-eight_pointed_black_star{background-position:-80px 0}.ap-snowflake{background-position:-80px -20px}.ap-sparkle{background-position:-80px -40px}.ap-x{background-position:-80px -60px}.ap-negative_squared_cross_mark{background-position:-80px -80px}.ap-question{background-position:-80px -100px}.ap-grey_question{background-position:-80px -120px}.ap-grey_exclamation{background-position:-80px -140px}.ap-exclamation{background-position:-80px -160px}.ap-heavy_heart_exclamation_mark_ornament{background-position:-80px -180px}.ap-heart{background-position:-80px -200px}.ap-heavy_plus_sign{background-position:-80px -220px}.ap-heavy_minus_sign{background-position:-80px -240px}.ap-heavy_division_sign{background-position:-80px -260px}.ap-arrow_right{background-position:-80px -280px}.ap-curly_loop{background-position:-80px -300px}.ap-loop{background-position:-80px -320px}.ap-arrow_heading_up{background-position:-80px -340px}.ap-arrow_heading_down{background-position:-80px -360px}.ap-arrow_left{background-position:-80px -380px}.ap-arrow_up{background-position:-80px -400px}.ap-arrow_down{background-position:-80px -420px}.ap-black_large_square{background-position:-80px -440px}.ap-white_large_square{background-position:-80px -460px}.ap-star{background-position:-80px -480px}.ap-o{background-position:-80px -500px}.ap-wavy_dash{background-position:-80px -520px}.ap-part_alternation_mark{background-position:-80px -540px}.ap-congratulations{background-position:-80px -560px}.ap-secret{background-position:-80px -580px}.ap-mahjong{background-position:-80px -600px}.ap-black_joker{background-position:-80px -620px}.ap-a{background-position:-80px -640px}.ap-b{background-position:-80px -660px}.ap-o2{background-position:-80px -680px}.ap-parking{background-position:-80px -700px}.ap-ab{background-position:-80px -720px}.ap-cl{background-position:-80px -740px}.ap-cool{background-position:-80px -760px}.ap-free{background-position:-80px -780px}.ap-id{background-position:-80px -800px}.ap-new{background-position:-100px 0}.ap-ng{background-position:-100px -20px}.ap-ok{background-position:-100px -40px}.ap-sos{background-position:-100px -60px}.ap-up{background-position:-100px -80px}.ap-vs{background-position:-100px -100px}.ap-koko{background-position:-100px -120px}.ap-sa{background-position:-100px -140px}.ap-u7121{background-position:-100px -160px}.ap-u6307{background-position:-100px -180px}.ap-u7981{background-position:-100px -200px}.ap-u7a7a{background-position:-100px -220px}.ap-u5408{background-position:-100px -240px}.ap-u6e80{background-position:-100px -260px}.ap-u6709{background-position:-100px -280px}.ap-u6708{background-position:-100px -300px}.ap-u7533{background-position:-100px -320px}.ap-u5272{background-position:-100px -340px}.ap-u55b6{background-position:-100px -360px}.ap-ideograph_advantage{background-position:-100px -380px}.ap-accept{background-position:-100px -400px}.ap-cyclone{background-position:-100px -420px}.ap-foggy{background-position:-100px -440px}.ap-closed_umbrella{background-position:-100px -460px}.ap-night_with_stars{background-position:-100px -480px}.ap-sunrise_over_mountains{background-position:-100px -500px}.ap-sunrise{background-position:-100px -520px}.ap-city_sunset{background-position:-100px -540px}.ap-city_sunrise{background-position:-100px -560px}.ap-rainbow{background-position:-100px -580px}.ap-bridge_at_night{background-position:-100px -600px}.ap-ocean{background-position:-100px -620px}.ap-volcano{background-position:-100px -640px}.ap-milky_way{background-position:-100px -660px}.ap-earth_africa{background-position:-100px -680px}.ap-earth_americas{background-position:-100px -700px}.ap-earth_asia{background-position:-100px -720px}.ap-globe_with_meridians{background-position:-100px -740px}.ap-new_moon{background-position:-100px -760px}.ap-waxing_crescent_moon{background-position:-100px -780px}.ap-first_quarter_moon{background-position:-100px -800px}.ap-moon{background-position:-120px 0}.ap-full_moon{background-position:-120px -20px}.ap-waning_gibbous_moon{background-position:-120px -40px}.ap-last_quarter_moon{background-position:-120px -60px}.ap-waning_crescent_moon{background-position:-120px -80px}.ap-crescent_moon{background-position:-120px -100px}.ap-new_moon_with_face{background-position:-120px -120px}.ap-first_quarter_moon_with_face{background-position:-120px -140px}.ap-last_quarter_moon_with_face{background-position:-120px -160px}.ap-full_moon_with_face{background-position:-120px -180px}.ap-sun_with_face{background-position:-120px -200px}.ap-star2{background-position:-120px -220px}.ap-stars{background-position:-120px -240px}.ap-thermometer{background-position:-120px -260px}.ap-mostly_sunny{background-position:-120px -280px}.ap-barely_sunny{background-position:-120px -300px}.ap-partly_sunny_rain{background-position:-120px -320px}.ap-rain_cloud{background-position:-120px -340px}.ap-snow_cloud{background-position:-120px -360px}.ap-lightning{background-position:-120px -380px}.ap-tornado{background-position:-120px -400px}.ap-fog{background-position:-120px -420px}.ap-wind_blowing_face{background-position:-120px -440px}.ap-hotdog{background-position:-120px -460px}.ap-taco{background-position:-120px -480px}.ap-burrito{background-position:-120px -500px}.ap-chestnut{background-position:-120px -520px}.ap-seedling{background-position:-120px -540px}.ap-evergreen_tree{background-position:-120px -560px}.ap-deciduous_tree{background-position:-120px -580px}.ap-palm_tree{background-position:-120px -600px}.ap-cactus{background-position:-120px -620px}.ap-hot_pepper{background-position:-120px -640px}.ap-tulip{background-position:-120px -660px}.ap-cherry_blossom{background-position:-120px -680px}.ap-rose{background-position:-120px -700px}.ap-hibiscus{background-position:-120px -720px}.ap-sunflower{background-position:-120px -740px}.ap-blossom{background-position:-120px -760px}.ap-corn{background-position:-120px -780px}.ap-ear_of_rice{background-position:-120px -800px}.ap-herb{background-position:-140px 0}.ap-four_leaf_clover{background-position:-140px -20px}.ap-maple_leaf{background-position:-140px -40px}.ap-fallen_leaf{background-position:-140px -60px}.ap-leaves{background-position:-140px -80px}.ap-mushroom{background-position:-140px -100px}.ap-tomato{background-position:-140px -120px}.ap-eggplant{background-position:-140px -140px}.ap-grapes{background-position:-140px -160px}.ap-melon{background-position:-140px -180px}.ap-watermelon{background-position:-140px -200px}.ap-tangerine{background-position:-140px -220px}.ap-lemon{background-position:-140px -240px}.ap-banana{background-position:-140px -260px}.ap-pineapple{background-position:-140px -280px}.ap-apple{background-position:-140px -300px}.ap-green_apple{background-position:-140px -320px}.ap-pear{background-position:-140px -340px}.ap-peach{background-position:-140px -360px}.ap-cherries{background-position:-140px -380px}.ap-strawberry{background-position:-140px -400px}.ap-hamburger{background-position:-140px -420px}.ap-pizza{background-position:-140px -440px}.ap-meat_on_bone{background-position:-140px -460px}.ap-poultry_leg{background-position:-140px -480px}.ap-rice_cracker{background-position:-140px -500px}.ap-rice_ball{background-position:-140px -520px}.ap-rice{background-position:-140px -540px}.ap-curry{background-position:-140px -560px}.ap-ramen{background-position:-140px -580px}.ap-spaghetti{background-position:-140px -600px}.ap-bread{background-position:-140px -620px}.ap-fries{background-position:-140px -640px}.ap-sweet_potato{background-position:-140px -660px}.ap-dango{background-position:-140px -680px}.ap-oden{background-position:-140px -700px}.ap-sushi{background-position:-140px -720px}.ap-fried_shrimp{background-position:-140px -740px}.ap-fish_cake{background-position:-140px -760px}.ap-icecream{background-position:-140px -780px}.ap-shaved_ice{background-position:-140px -800px}.ap-ice_cream{background-position:-160px 0}.ap-doughnut{background-position:-160px -20px}.ap-cookie{background-position:-160px -40px}.ap-chocolate_bar{background-position:-160px -60px}.ap-candy{background-position:-160px -80px}.ap-lollipop{background-position:-160px -100px}.ap-custard{background-position:-160px -120px}.ap-honey_pot{background-position:-160px -140px}.ap-cake{background-position:-160px -160px}.ap-bento{background-position:-160px -180px}.ap-stew{background-position:-160px -200px}.ap-egg{background-position:-160px -220px}.ap-fork_and_knife{background-position:-160px -240px}.ap-tea{background-position:-160px -260px}.ap-sake{background-position:-160px -280px}.ap-wine_glass{background-position:-160px -300px}.ap-cocktail{background-position:-160px -320px}.ap-tropical_drink{background-position:-160px -340px}.ap-beer{background-position:-160px -360px}.ap-beers{background-position:-160px -380px}.ap-baby_bottle{background-position:-160px -400px}.ap-knife_fork_plate{background-position:-160px -420px}.ap-champagne{background-position:-160px -440px}.ap-popcorn{background-position:-160px -460px}.ap-ribbon{background-position:-160px -480px}.ap-gift{background-position:-160px -500px}.ap-birthday{background-position:-160px -520px}.ap-jack_o_lantern{background-position:-160px -540px}.ap-christmas_tree{background-position:-160px -560px}.ap-santa{background-position:-160px -580px}.ap-fireworks{background-position:-160px -700px}.ap-sparkler{background-position:-160px -720px}.ap-balloon{background-position:-160px -740px}.ap-tada{background-position:-160px -760px}.ap-confetti_ball{background-position:-160px -780px}.ap-tanabata_tree{background-position:-160px -800px}.ap-crossed_flags{background-position:-180px 0}.ap-bamboo{background-position:-180px -20px}.ap-dolls{background-position:-180px -40px}.ap-flags{background-position:-180px -60px}.ap-wind_chime{background-position:-180px -80px}.ap-rice_scene{background-position:-180px -100px}.ap-school_satchel{background-position:-180px -120px}.ap-mortar_board{background-position:-180px -140px}.ap-medal{background-position:-180px -160px}.ap-reminder_ribbon{background-position:-180px -180px}.ap-studio_microphone{background-position:-180px -200px}.ap-level_slider{background-position:-180px -220px}.ap-control_knobs{background-position:-180px -240px}.ap-film_frames{background-position:-180px -260px}.ap-admission_tickets{background-position:-180px -280px}.ap-carousel_horse{background-position:-180px -300px}.ap-ferris_wheel{background-position:-180px -320px}.ap-roller_coaster{background-position:-180px -340px}.ap-fishing_pole_and_fish{background-position:-180px -360px}.ap-microphone{background-position:-180px -380px}.ap-movie_camera{background-position:-180px -400px}.ap-cinema{background-position:-180px -420px}.ap-headphones{background-position:-180px -440px}.ap-art{background-position:-180px -460px}.ap-tophat{background-position:-180px -480px}.ap-circus_tent{background-position:-180px -500px}.ap-ticket{background-position:-180px -520px}.ap-clapper{background-position:-180px -540px}.ap-performing_arts{background-position:-180px -560px}.ap-video_game{background-position:-180px -580px}.ap-dart{background-position:-180px -600px}.ap-slot_machine{background-position:-180px -620px}.ap-8ball{background-position:-180px -640px}.ap-game_die{background-position:-180px -660px}.ap-bowling{background-position:-180px -680px}.ap-flower_playing_cards{background-position:-180px -700px}.ap-musical_note{background-position:-180px -720px}.ap-notes{background-position:-180px -740px}.ap-saxophone{background-position:-180px -760px}.ap-guitar{background-position:-180px -780px}.ap-musical_keyboard{background-position:-180px -800px}.ap-trumpet{background-position:-200px 0}.ap-violin{background-position:-200px -20px}.ap-musical_score{background-position:-200px -40px}.ap-running_shirt_with_sash{background-position:-200px -60px}.ap-tennis{background-position:-200px -80px}.ap-ski{background-position:-200px -100px}.ap-basketball{background-position:-200px -120px}.ap-checkered_flag{background-position:-200px -140px}.ap-snowboarder{background-position:-200px -160px}.ap-runner{background-position:-200px -180px}.ap-surfer{background-position:-200px -300px}.ap-sports_medal{background-position:-200px -420px}.ap-trophy{background-position:-200px -440px}.ap-horse_racing{background-position:-200px -460px}.ap-football{background-position:-200px -480px}.ap-rugby_football{background-position:-200px -500px}.ap-swimmer{background-position:-200px -520px}.ap-weight_lifter{background-position:-200px -640px}.ap-golfer{background-position:-200px -760px}.ap-racing_motorcycle{background-position:-200px -780px}.ap-racing_car{background-position:-200px -800px}.ap-cricket_bat_and_ball{background-position:-220px 0}.ap-volleyball{background-position:-220px -20px}.ap-field_hockey_stick_and_ball{background-position:-220px -40px}.ap-ice_hockey_stick_and_puck{background-position:-220px -60px}.ap-table_tennis_paddle_and_ball{background-position:-220px -80px}.ap-snow_capped_mountain{background-position:-220px -100px}.ap-camping{background-position:-220px -120px}.ap-beach_with_umbrella{background-position:-220px -140px}.ap-building_construction{background-position:-220px -160px}.ap-house_buildings{background-position:-220px -180px}.ap-cityscape{background-position:-220px -200px}.ap-derelict_house_building{background-position:-220px -220px}.ap-classical_building{background-position:-220px -240px}.ap-desert{background-position:-220px -260px}.ap-desert_island{background-position:-220px -280px}.ap-national_park{background-position:-220px -300px}.ap-stadium{background-position:-220px -320px}.ap-house{background-position:-220px -340px}.ap-house_with_garden{background-position:-220px -360px}.ap-office{background-position:-220px -380px}.ap-post_office{background-position:-220px -400px}.ap-european_post_office{background-position:-220px -420px}.ap-hospital{background-position:-220px -440px}.ap-bank{background-position:-220px -460px}.ap-atm{background-position:-220px -480px}.ap-hotel{background-position:-220px -500px}.ap-love_hotel{background-position:-220px -520px}.ap-convenience_store{background-position:-220px -540px}.ap-school{background-position:-220px -560px}.ap-department_store{background-position:-220px -580px}.ap-factory{background-position:-220px -600px}.ap-izakaya_lantern{background-position:-220px -620px}.ap-japanese_castle{background-position:-220px -640px}.ap-european_castle{background-position:-220px -660px}.ap-waving_white_flag{background-position:-220px -680px}.ap-waving_black_flag{background-position:-220px -700px}.ap-rosette{background-position:-220px -720px}.ap-label{background-position:-220px -740px}.ap-badminton_racquet_and_shuttlecock{background-position:-220px -760px}.ap-bow_and_arrow{background-position:-220px -780px}.ap-amphora{background-position:-220px -800px}.ap-skin-tone-2{background-position:-240px 0}.ap-skin-tone-3{background-position:-240px -20px}.ap-skin-tone-4{background-position:-240px -40px}.ap-skin-tone-5{background-position:-240px -60px}.ap-skin-tone-6{background-position:-240px -80px}.ap-rat{background-position:-240px -100px}.ap-mouse2{background-position:-240px -120px}.ap-ox{background-position:-240px -140px}.ap-water_buffalo{background-position:-240px -160px}.ap-cow2{background-position:-240px -180px}.ap-tiger2{background-position:-240px -200px}.ap-leopard{background-position:-240px -220px}.ap-rabbit2{background-position:-240px -240px}.ap-cat2{background-position:-240px -260px}.ap-dragon{background-position:-240px -280px}.ap-crocodile{background-position:-240px -300px}.ap-whale2{background-position:-240px -320px}.ap-snail{background-position:-240px -340px}.ap-snake{background-position:-240px -360px}.ap-racehorse{background-position:-240px -380px}.ap-ram{background-position:-240px -400px}.ap-goat{background-position:-240px -420px}.ap-sheep{background-position:-240px -440px}.ap-monkey{background-position:-240px -460px}.ap-rooster{background-position:-240px -480px}.ap-chicken{background-position:-240px -500px}.ap-dog2{background-position:-240px -520px}.ap-pig2{background-position:-240px -540px}.ap-boar{background-position:-240px -560px}.ap-elephant{background-position:-240px -580px}.ap-octopus{background-position:-240px -600px}.ap-shell{background-position:-240px -620px}.ap-bug{background-position:-240px -640px}.ap-ant{background-position:-240px -660px}.ap-bee{background-position:-240px -680px}.ap-beetle{background-position:-240px -700px}.ap-fish{background-position:-240px -720px}.ap-tropical_fish{background-position:-240px -740px}.ap-blowfish{background-position:-240px -760px}.ap-turtle{background-position:-240px -780px}.ap-hatching_chick{background-position:-240px -800px}.ap-baby_chick{background-position:-260px 0}.ap-hatched_chick{background-position:-260px -20px}.ap-bird{background-position:-260px -40px}.ap-penguin{background-position:-260px -60px}.ap-koala{background-position:-260px -80px}.ap-poodle{background-position:-260px -100px}.ap-dromedary_camel{background-position:-260px -120px}.ap-camel{background-position:-260px -140px}.ap-dolphin{background-position:-260px -160px}.ap-mouse{background-position:-260px -180px}.ap-cow{background-position:-260px -200px}.ap-tiger{background-position:-260px -220px}.ap-rabbit{background-position:-260px -240px}.ap-cat{background-position:-260px -260px}.ap-dragon_face{background-position:-260px -280px}.ap-whale{background-position:-260px -300px}.ap-horse{background-position:-260px -320px}.ap-monkey_face{background-position:-260px -340px}.ap-dog{background-position:-260px -360px}.ap-pig{background-position:-260px -380px}.ap-frog{background-position:-260px -400px}.ap-hamster{background-position:-260px -420px}.ap-wolf{background-position:-260px -440px}.ap-bear{background-position:-260px -460px}.ap-panda_face{background-position:-260px -480px}.ap-pig_nose{background-position:-260px -500px}.ap-feet{background-position:-260px -520px}.ap-chipmunk{background-position:-260px -540px}.ap-eyes{background-position:-260px -560px}.ap-eye{background-position:-260px -580px}.ap-ear{background-position:-260px -600px}.ap-nose{background-position:-260px -720px}.ap-lips{background-position:-280px -20px}.ap-tongue{background-position:-280px -40px}.ap-point_up_2{background-position:-280px -60px}.ap-point_down{background-position:-280px -180px}.ap-point_left{background-position:-280px -300px}.ap-point_right{background-position:-280px -420px}.ap-facepunch{background-position:-280px -540px}.ap-wave{background-position:-280px -660px}.ap-ok_hand{background-position:-280px -780px}.ap-thumbsup{background-position:-300px -80px}.ap--1,.ap-thumbsdown{background-position:-300px -200px}.ap-clap{background-position:-300px -320px}.ap-open_hands{background-position:-300px -440px}.ap-crown{background-position:-300px -560px}.ap-womans_hat{background-position:-300px -580px}.ap-eyeglasses{background-position:-300px -600px}.ap-necktie{background-position:-300px -620px}.ap-shirt{background-position:-300px -640px}.ap-jeans{background-position:-300px -660px}.ap-dress{background-position:-300px -680px}.ap-kimono{background-position:-300px -700px}.ap-bikini{background-position:-300px -720px}.ap-womans_clothes{background-position:-300px -740px}.ap-purse{background-position:-300px -760px}.ap-handbag{background-position:-300px -780px}.ap-pouch{background-position:-300px -800px}.ap-mans_shoe{background-position:-320px 0}.ap-athletic_shoe{background-position:-320px -20px}.ap-high_heel{background-position:-320px -40px}.ap-sandal{background-position:-320px -60px}.ap-boot{background-position:-320px -80px}.ap-footprints{background-position:-320px -100px}.ap-bust_in_silhouette{background-position:-320px -120px}.ap-busts_in_silhouette{background-position:-320px -140px}.ap-boy{background-position:-320px -160px}.ap-girl{background-position:-320px -280px}.ap-man{background-position:-320px -400px}.ap-woman{background-position:-320px -520px}.ap-family{background-position:-320px -640px}.ap-couple{background-position:-320px -660px}.ap-two_men_holding_hands{background-position:-320px -680px}.ap-two_women_holding_hands{background-position:-320px -700px}.ap-cop{background-position:-320px -720px}.ap-dancers{background-position:-340px -20px}.ap-bride_with_veil{background-position:-340px -40px}.ap-person_with_blond_hair{background-position:-340px -160px}.ap-man_with_gua_pi_mao{background-position:-340px -280px}.ap-man_with_turban{background-position:-340px -400px}.ap-older_man{background-position:-340px -520px}.ap-older_woman{background-position:-340px -640px}.ap-baby{background-position:-340px -760px}.ap-construction_worker{background-position:-360px -60px}.ap-princess{background-position:-360px -180px}.ap-japanese_ogre{background-position:-360px -300px}.ap-japanese_goblin{background-position:-360px -320px}.ap-ghost{background-position:-360px -340px}.ap-angel{background-position:-360px -360px}.ap-alien{background-position:-360px -480px}.ap-space_invader{background-position:-360px -500px}.ap-imp{background-position:-360px -520px}.ap-skull{background-position:-360px -540px}.ap-information_desk_person{background-position:-360px -560px}.ap-guardsman{background-position:-360px -680px}.ap-dancer{background-position:-360px -800px}.ap-lipstick{background-position:-380px -100px}.ap-nail_care{background-position:-380px -120px}.ap-massage{background-position:-380px -240px}.ap-haircut{background-position:-380px -360px}.ap-barber{background-position:-380px -480px}.ap-syringe{background-position:-380px -500px}.ap-pill{background-position:-380px -520px}.ap-kiss{background-position:-380px -540px}.ap-love_letter{background-position:-380px -560px}.ap-ring{background-position:-380px -580px}.ap-gem{background-position:-380px -600px}.ap-couplekiss{background-position:-380px -620px}.ap-bouquet{background-position:-380px -640px}.ap-couple_with_heart{background-position:-380px -660px}.ap-wedding{background-position:-380px -680px}.ap-heartbeat{background-position:-380px -700px}.ap-broken_heart{background-position:-380px -720px}.ap-two_hearts{background-position:-380px -740px}.ap-sparkling_heart{background-position:-380px -760px}.ap-heartpulse{background-position:-380px -780px}.ap-cupid{background-position:-380px -800px}.ap-blue_heart{background-position:-400px 0}.ap-green_heart{background-position:-400px -20px}.ap-yellow_heart{background-position:-400px -40px}.ap-purple_heart{background-position:-400px -60px}.ap-gift_heart{background-position:-400px -80px}.ap-revolving_hearts{background-position:-400px -100px}.ap-heart_decoration{background-position:-400px -120px}.ap-diamond_shape_with_a_dot_inside{background-position:-400px -140px}.ap-bulb{background-position:-400px -160px}.ap-anger{background-position:-400px -180px}.ap-bomb{background-position:-400px -200px}.ap-zzz{background-position:-400px -220px}.ap-boom{background-position:-400px -240px}.ap-sweat_drops{background-position:-400px -260px}.ap-droplet{background-position:-400px -280px}.ap-dash{background-position:-400px -300px}.ap-hankey{background-position:-400px -320px}.ap-muscle{background-position:-400px -340px}.ap-dizzy{background-position:-400px -460px}.ap-speech_balloon{background-position:-400px -480px}.ap-thought_balloon{background-position:-400px -500px}.ap-white_flower{background-position:-400px -520px}.ap-100{background-position:-400px -540px}.ap-moneybag{background-position:-400px -560px}.ap-currency_exchange{background-position:-400px -580px}.ap-heavy_dollar_sign{background-position:-400px -600px}.ap-credit_card{background-position:-400px -620px}.ap-yen{background-position:-400px -640px}.ap-dollar{background-position:-400px -660px}.ap-euro{background-position:-400px -680px}.ap-pound{background-position:-400px -700px}.ap-money_with_wings{background-position:-400px -720px}.ap-chart{background-position:-400px -740px}.ap-seat{background-position:-400px -760px}.ap-computer{background-position:-400px -780px}.ap-briefcase{background-position:-400px -800px}.ap-minidisc{background-position:-420px 0}.ap-floppy_disk{background-position:-420px -20px}.ap-cd{background-position:-420px -40px}.ap-dvd{background-position:-420px -60px}.ap-file_folder{background-position:-420px -80px}.ap-open_file_folder{background-position:-420px -100px}.ap-page_with_curl{background-position:-420px -120px}.ap-page_facing_up{background-position:-420px -140px}.ap-date{background-position:-420px -160px}.ap-calendar{background-position:-420px -180px}.ap-card_index{background-position:-420px -200px}.ap-chart_with_upwards_trend{background-position:-420px -220px}.ap-chart_with_downwards_trend{background-position:-420px -240px}.ap-bar_chart{background-position:-420px -260px}.ap-clipboard{background-position:-420px -280px}.ap-pushpin{background-position:-420px -300px}.ap-round_pushpin{background-position:-420px -320px}.ap-paperclip{background-position:-420px -340px}.ap-straight_ruler{background-position:-420px -360px}.ap-triangular_ruler{background-position:-420px -380px}.ap-bookmark_tabs{background-position:-420px -400px}.ap-ledger{background-position:-420px -420px}.ap-notebook{background-position:-420px -440px}.ap-notebook_with_decorative_cover{background-position:-420px -460px}.ap-closed_book{background-position:-420px -480px}.ap-book{background-position:-420px -500px}.ap-green_book{background-position:-420px -520px}.ap-blue_book{background-position:-420px -540px}.ap-orange_book{background-position:-420px -560px}.ap-books{background-position:-420px -580px}.ap-name_badge{background-position:-420px -600px}.ap-scroll{background-position:-420px -620px}.ap-memo{background-position:-420px -640px}.ap-telephone_receiver{background-position:-420px -660px}.ap-pager{background-position:-420px -680px}.ap-fax{background-position:-420px -700px}.ap-satellite_antenna{background-position:-420px -720px}.ap-loudspeaker{background-position:-420px -740px}.ap-mega{background-position:-420px -760px}.ap-outbox_tray{background-position:-420px -780px}.ap-inbox_tray{background-position:-420px -800px}.ap-package{background-position:-440px 0}.ap-e-mail{background-position:-440px -20px}.ap-incoming_envelope{background-position:-440px -40px}.ap-envelope_with_arrow{background-position:-440px -60px}.ap-mailbox_closed{background-position:-440px -80px}.ap-mailbox{background-position:-440px -100px}.ap-mailbox_with_mail{background-position:-440px -120px}.ap-mailbox_with_no_mail{background-position:-440px -140px}.ap-postbox{background-position:-440px -160px}.ap-postal_horn{background-position:-440px -180px}.ap-newspaper{background-position:-440px -200px}.ap-iphone{background-position:-440px -220px}.ap-calling{background-position:-440px -240px}.ap-vibration_mode{background-position:-440px -260px}.ap-mobile_phone_off{background-position:-440px -280px}.ap-no_mobile_phones{background-position:-440px -300px}.ap-signal_strength{background-position:-440px -320px}.ap-camera{background-position:-440px -340px}.ap-camera_with_flash{background-position:-440px -360px}.ap-video_camera{background-position:-440px -380px}.ap-tv{background-position:-440px -400px}.ap-radio{background-position:-440px -420px}.ap-vhs{background-position:-440px -440px}.ap-film_projector{background-position:-440px -460px}.ap-prayer_beads{background-position:-440px -480px}.ap-twisted_rightwards_arrows{background-position:-440px -500px}.ap-repeat{background-position:-440px -520px}.ap-repeat_one{background-position:-440px -540px}.ap-arrows_clockwise{background-position:-440px -560px}.ap-arrows_counterclockwise{background-position:-440px -580px}.ap-low_brightness{background-position:-440px -600px}.ap-high_brightness{background-position:-440px -620px}.ap-mute{background-position:-440px -640px}.ap-speaker{background-position:-440px -660px}.ap-sound{background-position:-440px -680px}.ap-loud_sound{background-position:-440px -700px}.ap-battery{background-position:-440px -720px}.ap-electric_plug{background-position:-440px -740px}.ap-mag{background-position:-440px -760px}.ap-mag_right{background-position:-440px -780px}.ap-lock_with_ink_pen{background-position:-440px -800px}.ap-closed_lock_with_key{background-position:-460px 0}.ap-key{background-position:-460px -20px}.ap-lock{background-position:-460px -40px}.ap-unlock{background-position:-460px -60px}.ap-bell{background-position:-460px -80px}.ap-no_bell{background-position:-460px -100px}.ap-bookmark{background-position:-460px -120px}.ap-link{background-position:-460px -140px}.ap-radio_button{background-position:-460px -160px}.ap-back{background-position:-460px -180px}.ap-end{background-position:-460px -200px}.ap-on{background-position:-460px -220px}.ap-soon{background-position:-460px -240px}.ap-top{background-position:-460px -260px}.ap-underage{background-position:-460px -280px}.ap-keycap_ten{background-position:-460px -300px}.ap-capital_abcd{background-position:-460px -320px}.ap-abcd{background-position:-460px -340px}.ap-1234{background-position:-460px -360px}.ap-symbols{background-position:-460px -380px}.ap-abc{background-position:-460px -400px}.ap-fire{background-position:-460px -420px}.ap-flashlight{background-position:-460px -440px}.ap-wrench{background-position:-460px -460px}.ap-hammer{background-position:-460px -480px}.ap-nut_and_bolt{background-position:-460px -500px}.ap-hocho{background-position:-460px -520px}.ap-gun{background-position:-460px -540px}.ap-microscope{background-position:-460px -560px}.ap-telescope{background-position:-460px -580px}.ap-crystal_ball{background-position:-460px -600px}.ap-six_pointed_star{background-position:-460px -620px}.ap-beginner{background-position:-460px -640px}.ap-trident{background-position:-460px -660px}.ap-black_square_button{background-position:-460px -680px}.ap-white_square_button{background-position:-460px -700px}.ap-red_circle{background-position:-460px -720px}.ap-large_blue_circle{background-position:-460px -740px}.ap-large_orange_diamond{background-position:-460px -760px}.ap-large_blue_diamond{background-position:-460px -780px}.ap-small_orange_diamond{background-position:-460px -800px}.ap-small_blue_diamond{background-position:-480px 0}.ap-small_red_triangle{background-position:-480px -20px}.ap-small_red_triangle_down{background-position:-480px -40px}.ap-arrow_up_small{background-position:-480px -60px}.ap-arrow_down_small{background-position:-480px -80px}.ap-om_symbol{background-position:-480px -100px}.ap-dove_of_peace{background-position:-480px -120px}.ap-kaaba{background-position:-480px -140px}.ap-mosque{background-position:-480px -160px}.ap-synagogue{background-position:-480px -180px}.ap-menorah_with_nine_branches{background-position:-480px -200px}.ap-clock1{background-position:-480px -220px}.ap-clock2{background-position:-480px -240px}.ap-clock3{background-position:-480px -260px}.ap-clock4{background-position:-480px -280px}.ap-clock5{background-position:-480px -300px}.ap-clock6{background-position:-480px -320px}.ap-clock7{background-position:-480px -340px}.ap-clock8{background-position:-480px -360px}.ap-clock9{background-position:-480px -380px}.ap-clock10{background-position:-480px -400px}.ap-clock11{background-position:-480px -420px}.ap-clock12{background-position:-480px -440px}.ap-clock130{background-position:-480px -460px}.ap-clock230{background-position:-480px -480px}.ap-clock330{background-position:-480px -500px}.ap-clock430{background-position:-480px -520px}.ap-clock530{background-position:-480px -540px}.ap-clock630{background-position:-480px -560px}.ap-clock730{background-position:-480px -580px}.ap-clock830{background-position:-480px -600px}.ap-clock930{background-position:-480px -620px}.ap-clock1030{background-position:-480px -640px}.ap-clock1130{background-position:-480px -660px}.ap-clock1230{background-position:-480px -680px}.ap-candle{background-position:-480px -700px}.ap-mantelpiece_clock{background-position:-480px -720px}.ap-hole{background-position:-480px -740px}.ap-man_in_business_suit_levitating{background-position:-480px -760px}.ap-sleuth_or_spy{background-position:-480px -780px}.ap-dark_sunglasses{background-position:-500px -80px}.ap-spider{background-position:-500px -100px}.ap-spider_web{background-position:-500px -120px}.ap-joystick{background-position:-500px -140px}.ap-linked_paperclips{background-position:-500px -160px}.ap-lower_left_ballpoint_pen{background-position:-500px -180px}.ap-lower_left_fountain_pen{background-position:-500px -200px}.ap-lower_left_paintbrush{background-position:-500px -220px}.ap-lower_left_crayon{background-position:-500px -240px}.ap-raised_hand_with_fingers_splayed{background-position:-500px -260px}.ap-middle_finger{background-position:-500px -380px}.ap-spock-hand{background-position:-500px -500px}.ap-desktop_computer{background-position:-500px -620px}.ap-printer{background-position:-500px -640px}.ap-three_button_mouse{background-position:-500px -660px}.ap-trackball{background-position:-500px -680px}.ap-frame_with_picture{background-position:-500px -700px}.ap-card_index_dividers{background-position:-500px -720px}.ap-card_file_box{background-position:-500px -740px}.ap-file_cabinet{background-position:-500px -760px}.ap-wastebasket{background-position:-500px -780px}.ap-spiral_note_pad{background-position:-500px -800px}.ap-spiral_calendar_pad{background-position:-520px 0}.ap-compression{background-position:-520px -20px}.ap-old_key{background-position:-520px -40px}.ap-rolled_up_newspaper{background-position:-520px -60px}.ap-dagger_knife{background-position:-520px -80px}.ap-speaking_head_in_silhouette{background-position:-520px -100px}.ap-left_speech_bubble{background-position:-520px -120px}.ap-right_anger_bubble{background-position:-520px -140px}.ap-ballot_box_with_ballot{background-position:-520px -160px}.ap-world_map{background-position:-520px -180px}.ap-mount_fuji{background-position:-520px -200px}.ap-tokyo_tower{background-position:-520px -220px}.ap-statue_of_liberty{background-position:-520px -240px}.ap-japan{background-position:-520px -260px}.ap-moyai{background-position:-520px -280px}.ap-grinning{background-position:-520px -300px}.ap-grin{background-position:-520px -320px}.ap-joy{background-position:-520px -340px}.ap-smiley{background-position:-520px -360px}.ap-smile{background-position:-520px -380px}.ap-sweat_smile{background-position:-520px -400px}.ap-laughing{background-position:-520px -420px}.ap-innocent{background-position:-520px -440px}.ap-smiling_imp{background-position:-520px -460px}.ap-wink{background-position:-520px -480px}.ap-blush{background-position:-520px -500px}.ap-yum{background-position:-520px -520px}.ap-relieved{background-position:-520px -540px}.ap-heart_eyes{background-position:-520px -560px}.ap-sunglasses{background-position:-520px -580px}.ap-smirk{background-position:-520px -600px}.ap-neutral_face{background-position:-520px -620px}.ap-expressionless{background-position:-520px -640px}.ap-unamused{background-position:-520px -660px}.ap-sweat{background-position:-520px -680px}.ap-pensive{background-position:-520px -700px}.ap-confused{background-position:-520px -720px}.ap-confounded{background-position:-520px -740px}.ap-kissing{background-position:-520px -760px}.ap-kissing_heart{background-position:-520px -780px}.ap-kissing_smiling_eyes{background-position:-520px -800px}.ap-kissing_closed_eyes{background-position:-540px 0}.ap-stuck_out_tongue{background-position:-540px -20px}.ap-stuck_out_tongue_winking_eye{background-position:-540px -40px}.ap-stuck_out_tongue_closed_eyes{background-position:-540px -60px}.ap-disappointed{background-position:-540px -80px}.ap-worried{background-position:-540px -100px}.ap-angry{background-position:-540px -120px}.ap-rage{background-position:-540px -140px}.ap-cry{background-position:-540px -160px}.ap-persevere{background-position:-540px -180px}.ap-triumph{background-position:-540px -200px}.ap-disappointed_relieved{background-position:-540px -220px}.ap-frowning{background-position:-540px -240px}.ap-anguished{background-position:-540px -260px}.ap-fearful{background-position:-540px -280px}.ap-weary{background-position:-540px -300px}.ap-sleepy{background-position:-540px -320px}.ap-tired_face{background-position:-540px -340px}.ap-grimacing{background-position:-540px -360px}.ap-sob{background-position:-540px -380px}.ap-open_mouth{background-position:-540px -400px}.ap-hushed{background-position:-540px -420px}.ap-cold_sweat{background-position:-540px -440px}.ap-scream{background-position:-540px -460px}.ap-astonished{background-position:-540px -480px}.ap-flushed{background-position:-540px -500px}.ap-sleeping{background-position:-540px -520px}.ap-dizzy_face{background-position:-540px -540px}.ap-no_mouth{background-position:-540px -560px}.ap-mask{background-position:-540px -580px}.ap-smile_cat{background-position:-540px -600px}.ap-joy_cat{background-position:-540px -620px}.ap-smiley_cat{background-position:-540px -640px}.ap-heart_eyes_cat{background-position:-540px -660px}.ap-smirk_cat{background-position:-540px -680px}.ap-kissing_cat{background-position:-540px -700px}.ap-pouting_cat{background-position:-540px -720px}.ap-crying_cat_face{background-position:-540px -740px}.ap-scream_cat{background-position:-540px -760px}.ap-slightly_frowning_face{background-position:-540px -780px}.ap-slightly_smiling_face{background-position:-540px -800px}.ap-upside_down_face{background-position:-560px 0}.ap-face_with_rolling_eyes{background-position:-560px -20px}.ap-no_good{background-position:-560px -40px}.ap-ok_woman{background-position:-560px -160px}.ap-bow{background-position:-560px -280px}.ap-see_no_evil{background-position:-560px -400px}.ap-hear_no_evil{background-position:-560px -420px}.ap-speak_no_evil{background-position:-560px -440px}.ap-raising_hand{background-position:-560px -460px}.ap-raised_hands{background-position:-560px -580px}.ap-person_frowning{background-position:-560px -700px}.ap-person_with_pouting_face{background-position:-580px 0}.ap-pray{background-position:-580px -120px}.ap-rocket{background-position:-580px -240px}.ap-helicopter{background-position:-580px -260px}.ap-steam_locomotive{background-position:-580px -280px}.ap-railway_car{background-position:-580px -300px}.ap-bullettrain_side{background-position:-580px -320px}.ap-bullettrain_front{background-position:-580px -340px}.ap-train2{background-position:-580px -360px}.ap-metro{background-position:-580px -380px}.ap-light_rail{background-position:-580px -400px}.ap-station{background-position:-580px -420px}.ap-tram{background-position:-580px -440px}.ap-train{background-position:-580px -460px}.ap-bus{background-position:-580px -480px}.ap-oncoming_bus{background-position:-580px -500px}.ap-trolleybus{background-position:-580px -520px}.ap-busstop{background-position:-580px -540px}.ap-minibus{background-position:-580px -560px}.ap-ambulance{background-position:-580px -580px}.ap-fire_engine{background-position:-580px -600px}.ap-police_car{background-position:-580px -620px}.ap-oncoming_police_car{background-position:-580px -640px}.ap-taxi{background-position:-580px -660px}.ap-oncoming_taxi{background-position:-580px -680px}.ap-car{background-position:-580px -700px}.ap-oncoming_automobile{background-position:-580px -720px}.ap-blue_car{background-position:-580px -740px}.ap-truck{background-position:-580px -760px}.ap-articulated_lorry{background-position:-580px -780px}.ap-tractor{background-position:-580px -800px}.ap-monorail{background-position:-600px 0}.ap-mountain_railway{background-position:-600px -20px}.ap-suspension_railway{background-position:-600px -40px}.ap-mountain_cableway{background-position:-600px -60px}.ap-aerial_tramway{background-position:-600px -80px}.ap-ship{background-position:-600px -100px}.ap-rowboat{background-position:-600px -120px}.ap-speedboat{background-position:-600px -240px}.ap-traffic_light{background-position:-600px -260px}.ap-vertical_traffic_light{background-position:-600px -280px}.ap-construction{background-position:-600px -300px}.ap-rotating_light{background-position:-600px -320px}.ap-triangular_flag_on_post{background-position:-600px -340px}.ap-door{background-position:-600px -360px}.ap-no_entry_sign{background-position:-600px -380px}.ap-smoking{background-position:-600px -400px}.ap-no_smoking{background-position:-600px -420px}.ap-put_litter_in_its_place{background-position:-600px -440px}.ap-do_not_litter{background-position:-600px -460px}.ap-potable_water{background-position:-600px -480px}.ap-non-potable_water{background-position:-600px -500px}.ap-bike{background-position:-600px -520px}.ap-no_bicycles{background-position:-600px -540px}.ap-bicyclist{background-position:-600px -560px}.ap-mountain_bicyclist{background-position:-600px -680px}.ap-walking{background-position:-600px -800px}.ap-no_pedestrians{background-position:-620px -100px}.ap-children_crossing{background-position:-620px -120px}.ap-mens{background-position:-620px -140px}.ap-womens{background-position:-620px -160px}.ap-restroom{background-position:-620px -180px}.ap-baby_symbol{background-position:-620px -200px}.ap-toilet{background-position:-620px -220px}.ap-wc{background-position:-620px -240px}.ap-shower{background-position:-620px -260px}.ap-bath{background-position:-620px -280px}.ap-bathtub{background-position:-620px -400px}.ap-passport_control{background-position:-620px -420px}.ap-customs{background-position:-620px -440px}.ap-baggage_claim{background-position:-620px -460px}.ap-left_luggage{background-position:-620px -480px}.ap-couch_and_lamp{background-position:-620px -500px}.ap-sleeping_accommodation{background-position:-620px -520px}.ap-shopping_bags{background-position:-620px -540px}.ap-bellhop_bell{background-position:-620px -560px}.ap-bed{background-position:-620px -580px}.ap-place_of_worship{background-position:-620px -600px}.ap-hammer_and_wrench{background-position:-620px -620px}.ap-shield{background-position:-620px -640px}.ap-oil_drum{background-position:-620px -660px}.ap-motorway{background-position:-620px -680px}.ap-railway_track{background-position:-620px -700px}.ap-motor_boat{background-position:-620px -720px}.ap-small_airplane{background-position:-620px -740px}.ap-airplane_departure{background-position:-620px -760px}.ap-airplane_arriving{background-position:-620px -780px}.ap-satellite{background-position:-620px -800px}.ap-passenger_ship{background-position:-640px 0}.ap-zipper_mouth_face{background-position:-640px -20px}.ap-money_mouth_face{background-position:-640px -40px}.ap-face_with_thermometer{background-position:-640px -60px}.ap-nerd_face{background-position:-640px -80px}.ap-thinking_face{background-position:-640px -100px}.ap-face_with_head_bandage{background-position:-640px -120px}.ap-robot_face{background-position:-640px -140px}.ap-hugging_face{background-position:-640px -160px}.ap-the_horns{background-position:-640px -180px}.ap-crab{background-position:-640px -300px}.ap-lion_face{background-position:-640px -320px}.ap-scorpion{background-position:-640px -340px}.ap-turkey{background-position:-640px -360px}.ap-unicorn_face{background-position:-640px -380px}.ap-cheese_wedge{background-position:-640px -400px}.ap-hash{background-position:-640px -420px}.ap-keycap_star{background-position:-640px -440px}.ap-zero{background-position:-640px -460px}.ap-one{background-position:-640px -480px}.ap-two{background-position:-640px -500px}.ap-three{background-position:-640px -520px}.ap-four{background-position:-640px -540px}.ap-five{background-position:-640px -560px}.ap-six{background-position:-640px -580px}.ap-seven{background-position:-640px -600px}.ap-eight{background-position:-640px -620px}.ap-nine{background-position:-640px -640px}.ap-flag-ac{background-position:-640px -660px}.ap-flag-ad{background-position:-640px -680px}.ap-flag-ae{background-position:-640px -700px}.ap-flag-af{background-position:-640px -720px}.ap-flag-ag{background-position:-640px -740px}.ap-flag-ai{background-position:-640px -760px}.ap-flag-al{background-position:-640px -780px}.ap-flag-am{background-position:-640px -800px}.ap-flag-ao{background-position:-660px 0}.ap-flag-aq{background-position:-660px -20px}.ap-flag-ar{background-position:-660px -40px}.ap-flag-as{background-position:-660px -60px}.ap-flag-at{background-position:-660px -80px}.ap-flag-au{background-position:-660px -100px}.ap-flag-aw{background-position:-660px -120px}.ap-flag-ax{background-position:-660px -140px}.ap-flag-az{background-position:-660px -160px}.ap-flag-ba{background-position:-660px -180px}.ap-flag-bb{background-position:-660px -200px}.ap-flag-bd{background-position:-660px -220px}.ap-flag-be{background-position:-660px -240px}.ap-flag-bf{background-position:-660px -260px}.ap-flag-bg{background-position:-660px -280px}.ap-flag-bh{background-position:-660px -300px}.ap-flag-bi{background-position:-660px -320px}.ap-flag-bj{background-position:-660px -340px}.ap-flag-bl{background-position:-660px -360px}.ap-flag-bm{background-position:-660px -380px}.ap-flag-bn{background-position:-660px -400px}.ap-flag-bo{background-position:-660px -420px}.ap-flag-bq{background-position:-660px -440px}.ap-flag-br{background-position:-660px -460px}.ap-flag-bs{background-position:-660px -480px}.ap-flag-bt{background-position:-660px -500px}.ap-flag-bv{background-position:-660px -520px}.ap-flag-bw{background-position:-660px -540px}.ap-flag-by{background-position:-660px -560px}.ap-flag-bz{background-position:-660px -580px}.ap-flag-ca{background-position:-660px -600px}.ap-flag-cc{background-position:-660px -620px}.ap-flag-cd{background-position:-660px -640px}.ap-flag-cf{background-position:-660px -660px}.ap-flag-cg{background-position:-660px -680px}.ap-flag-ch{background-position:-660px -700px}.ap-flag-ci{background-position:-660px -720px}.ap-flag-ck{background-position:-660px -740px}.ap-flag-cl{background-position:-660px -760px}.ap-flag-cm{background-position:-660px -780px}.ap-flag-cn{background-position:-660px -800px}.ap-flag-co{background-position:-680px 0}.ap-flag-cp{background-position:-680px -20px}.ap-flag-cr{background-position:-680px -40px}.ap-flag-cu{background-position:-680px -60px}.ap-flag-cv{background-position:-680px -80px}.ap-flag-cw{background-position:-680px -100px}.ap-flag-cx{background-position:-680px -120px}.ap-flag-cy{background-position:-680px -140px}.ap-flag-cz{background-position:-680px -160px}.ap-flag-de{background-position:-680px -180px}.ap-flag-dg{background-position:-680px -200px}.ap-flag-dj{background-position:-680px -220px}.ap-flag-dk{background-position:-680px -240px}.ap-flag-dm{background-position:-680px -260px}.ap-flag-do{background-position:-680px -280px}.ap-flag-dz{background-position:-680px -300px}.ap-flag-ea{background-position:-680px -320px}.ap-flag-ec{background-position:-680px -340px}.ap-flag-ee{background-position:-680px -360px}.ap-flag-eg{background-position:-680px -380px}.ap-flag-eh{background-position:-680px -400px}.ap-flag-er{background-position:-680px -420px}.ap-flag-es{background-position:-680px -440px}.ap-flag-et{background-position:-680px -460px}.ap-flag-eu{background-position:-680px -480px}.ap-flag-fi{background-position:-680px -500px}.ap-flag-fj{background-position:-680px -520px}.ap-flag-fk{background-position:-680px -540px}.ap-flag-fm{background-position:-680px -560px}.ap-flag-fo{background-position:-680px -580px}.ap-flag-fr{background-position:-680px -600px}.ap-flag-ga{background-position:-680px -620px}.ap-flag-gb{background-position:-680px -640px}.ap-flag-gd{background-position:-680px -660px}.ap-flag-ge{background-position:-680px -680px}.ap-flag-gf{background-position:-680px -700px}.ap-flag-gg{background-position:-680px -720px}.ap-flag-gh{background-position:-680px -740px}.ap-flag-gi{background-position:-680px -760px}.ap-flag-gl{background-position:-680px -780px}.ap-flag-gm{background-position:-680px -800px}.ap-flag-gn{background-position:-700px 0}.ap-flag-gp{background-position:-700px -20px}.ap-flag-gq{background-position:-700px -40px}.ap-flag-gr{background-position:-700px -60px}.ap-flag-gs{background-position:-700px -80px}.ap-flag-gt{background-position:-700px -100px}.ap-flag-gu{background-position:-700px -120px}.ap-flag-gw{background-position:-700px -140px}.ap-flag-gy{background-position:-700px -160px}.ap-flag-hk{background-position:-700px -180px}.ap-flag-hm{background-position:-700px -200px}.ap-flag-hn{background-position:-700px -220px}.ap-flag-hr{background-position:-700px -240px}.ap-flag-ht{background-position:-700px -260px}.ap-flag-hu{background-position:-700px -280px}.ap-flag-ic{background-position:-700px -300px}.ap-flag-id{background-position:-700px -320px}.ap-flag-ie{background-position:-700px -340px}.ap-flag-il{background-position:-700px -360px}.ap-flag-im{background-position:-700px -380px}.ap-flag-in{background-position:-700px -400px}.ap-flag-io{background-position:-700px -420px}.ap-flag-iq{background-position:-700px -440px}.ap-flag-ir{background-position:-700px -460px}.ap-flag-is{background-position:-700px -480px}.ap-flag-it{background-position:-700px -500px}.ap-flag-je{background-position:-700px -520px}.ap-flag-jm{background-position:-700px -540px}.ap-flag-jo{background-position:-700px -560px}.ap-flag-jp{background-position:-700px -580px}.ap-flag-ke{background-position:-700px -600px}.ap-flag-kg{background-position:-700px -620px}.ap-flag-kh{background-position:-700px -640px}.ap-flag-ki{background-position:-700px -660px}.ap-flag-km{background-position:-700px -680px}.ap-flag-kn{background-position:-700px -700px}.ap-flag-kp{background-position:-700px -720px}.ap-flag-kr{background-position:-700px -740px}.ap-flag-kw{background-position:-700px -760px}.ap-flag-ky{background-position:-700px -780px}.ap-flag-kz{background-position:-700px -800px}.ap-flag-la{background-position:-720px 0}.ap-flag-lb{background-position:-720px -20px}.ap-flag-lc{background-position:-720px -40px}.ap-flag-li{background-position:-720px -60px}.ap-flag-lk{background-position:-720px -80px}.ap-flag-lr{background-position:-720px -100px}.ap-flag-ls{background-position:-720px -120px}.ap-flag-lt{background-position:-720px -140px}.ap-flag-lu{background-position:-720px -160px}.ap-flag-lv{background-position:-720px -180px}.ap-flag-ly{background-position:-720px -200px}.ap-flag-ma{background-position:-720px -220px}.ap-flag-mc{background-position:-720px -240px}.ap-flag-md{background-position:-720px -260px}.ap-flag-me{background-position:-720px -280px}.ap-flag-mf{background-position:-720px -300px}.ap-flag-mg{background-position:-720px -320px}.ap-flag-mh{background-position:-720px -340px}.ap-flag-mk{background-position:-720px -360px}.ap-flag-ml{background-position:-720px -380px}.ap-flag-mm{background-position:-720px -400px}.ap-flag-mn{background-position:-720px -420px}.ap-flag-mo{background-position:-720px -440px}.ap-flag-mp{background-position:-720px -460px}.ap-flag-mq{background-position:-720px -480px}.ap-flag-mr{background-position:-720px -500px}.ap-flag-ms{background-position:-720px -520px}.ap-flag-mt{background-position:-720px -540px}.ap-flag-mu{background-position:-720px -560px}.ap-flag-mv{background-position:-720px -580px}.ap-flag-mw{background-position:-720px -600px}.ap-flag-mx{background-position:-720px -620px}.ap-flag-my{background-position:-720px -640px}.ap-flag-mz{background-position:-720px -660px}.ap-flag-na{background-position:-720px -680px}.ap-flag-nc{background-position:-720px -700px}.ap-flag-ne{background-position:-720px -720px}.ap-flag-nf{background-position:-720px -740px}.ap-flag-ng{background-position:-720px -760px}.ap-flag-ni{background-position:-720px -780px}.ap-flag-nl{background-position:-720px -800px}.ap-flag-no{background-position:-740px 0}.ap-flag-np{background-position:-740px -20px}.ap-flag-nr{background-position:-740px -40px}.ap-flag-nu{background-position:-740px -60px}.ap-flag-nz{background-position:-740px -80px}.ap-flag-om{background-position:-740px -100px}.ap-flag-pa{background-position:-740px -120px}.ap-flag-pe{background-position:-740px -140px}.ap-flag-pf{background-position:-740px -160px}.ap-flag-pg{background-position:-740px -180px}.ap-flag-ph{background-position:-740px -200px}.ap-flag-pk{background-position:-740px -220px}.ap-flag-pl{background-position:-740px -240px}.ap-flag-pm{background-position:-740px -260px}.ap-flag-pn{background-position:-740px -280px}.ap-flag-pr{background-position:-740px -300px}.ap-flag-ps{background-position:-740px -320px}.ap-flag-pt{background-position:-740px -340px}.ap-flag-pw{background-position:-740px -360px}.ap-flag-py{background-position:-740px -380px}.ap-flag-qa{background-position:-740px -400px}.ap-flag-re{background-position:-740px -420px}.ap-flag-ro{background-position:-740px -440px}.ap-flag-rs{background-position:-740px -460px}.ap-flag-ru{background-position:-740px -480px}.ap-flag-rw{background-position:-740px -500px}.ap-flag-sa{background-position:-740px -520px}.ap-flag-sb{background-position:-740px -540px}.ap-flag-sc{background-position:-740px -560px}.ap-flag-sd{background-position:-740px -580px}.ap-flag-se{background-position:-740px -600px}.ap-flag-sg{background-position:-740px -620px}.ap-flag-sh{background-position:-740px -640px}.ap-flag-si{background-position:-740px -660px}.ap-flag-sj{background-position:-740px -680px}.ap-flag-sk{background-position:-740px -700px}.ap-flag-sl{background-position:-740px -720px}.ap-flag-sm{background-position:-740px -740px}.ap-flag-sn{background-position:-740px -760px}.ap-flag-so{background-position:-740px -780px}.ap-flag-sr{background-position:-740px -800px}.ap-flag-ss{background-position:-760px 0}.ap-flag-st{background-position:-760px -20px}.ap-flag-sv{background-position:-760px -40px}.ap-flag-sx{background-position:-760px -60px}.ap-flag-sy{background-position:-760px -80px}.ap-flag-sz{background-position:-760px -100px}.ap-flag-ta{background-position:-760px -120px}.ap-flag-tc{background-position:-760px -140px}.ap-flag-td{background-position:-760px -160px}.ap-flag-tf{background-position:-760px -180px}.ap-flag-tg{background-position:-760px -200px}.ap-flag-th{background-position:-760px -220px}.ap-flag-tj{background-position:-760px -240px}.ap-flag-tk{background-position:-760px -260px}.ap-flag-tl{background-position:-760px -280px}.ap-flag-tm{background-position:-760px -300px}.ap-flag-tn{background-position:-760px -320px}.ap-flag-to{background-position:-760px -340px}.ap-flag-tr{background-position:-760px -360px}.ap-flag-tt{background-position:-760px -380px}.ap-flag-tv{background-position:-760px -400px}.ap-flag-tw{background-position:-760px -420px}.ap-flag-tz{background-position:-760px -440px}.ap-flag-ua{background-position:-760px -460px}.ap-flag-ug{background-position:-760px -480px}.ap-flag-um{background-position:-760px -500px}.ap-flag-us{background-position:-760px -520px}.ap-flag-uy{background-position:-760px -540px}.ap-flag-uz{background-position:-760px -560px}.ap-flag-va{background-position:-760px -580px}.ap-flag-vc{background-position:-760px -600px}.ap-flag-ve{background-position:-760px -620px}.ap-flag-vg{background-position:-760px -640px}.ap-flag-vi{background-position:-760px -660px}.ap-flag-vn{background-position:-760px -680px}.ap-flag-vu{background-position:-760px -700px}.ap-flag-wf{background-position:-760px -720px}.ap-flag-ws{background-position:-760px -740px}.ap-flag-xk{background-position:-760px -760px}.ap-flag-ye{background-position:-760px -780px}.ap-flag-yt{background-position:-760px -800px}.ap-flag-za{background-position:-780px 0}.ap-flag-zm{background-position:-780px -20px}.ap-flag-zw{background-position:-780px -40px}.ap-man-man-boy{background-position:-780px -60px}.ap-man-man-boy-boy{background-position:-780px -80px}.ap-man-man-girl{background-position:-780px -100px}.ap-man-man-girl-boy{background-position:-780px -120px}.ap-man-man-girl-girl{background-position:-780px -140px}.ap-man-woman-boy-boy{background-position:-780px -160px}.ap-man-woman-girl{background-position:-780px -180px}.ap-man-woman-girl-boy{background-position:-780px -200px}.ap-man-woman-girl-girl{background-position:-780px -220px}.ap-man-heart-man{background-position:-780px -240px}.ap-man-kiss-man{background-position:-780px -260px}.ap-woman-woman-boy{background-position:-780px -280px}.ap-woman-woman-boy-boy{background-position:-780px -300px}.ap-woman-woman-girl{background-position:-780px -320px}.ap-woman-woman-girl-boy{background-position:-780px -340px}.ap-woman-woman-girl-girl{background-position:-780px -360px}.ap-woman-heart-woman{background-position:-780px -380px}.ap-woman-kiss-woman{background-position:-780px -400px}
.ql-mention-list-container {
  width: 270px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(30, 30, 30, 0.08);
  z-index: 9001;
  overflow: auto;
}

.ql-mention-loading {
  line-height: 44px;
  padding: 0 20px;
  vertical-align: middle;
  font-size: 16px;
}

.ql-mention-list {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.ql-mention-list-item {
  cursor: pointer;
  line-height: 44px;
  font-size: 16px;
  padding: 0 20px;
  vertical-align: middle;
}

.ql-mention-list-item.disabled {
  cursor: auto;
}

.ql-mention-list-item.selected {
  background-color: #d3e1eb;
  text-decoration: none;
}

.mention {
  height: 24px;
  width: 65px;
  border-radius: 6px;
  background-color: #d3e1eb;
  padding: 3px 0;
  margin-right: 2px;
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}

.mention > span {
  margin: 0 3px;
}

@charset "UTF-8";
/*!--

TEMPLATE NAME: Bootbox - Agency HTML Template
TEMPLATE URI: - https://bootbox.froid.works/src/index.html
DESCRIPTION: Bootbox Agency HTML Template is crafted carefully and with love which can bring attentions to your client to make things working good for your business.
VERSION: 1.0.1
AUTHOR: Ajay Kumar Choudhary
AUTHOR URL: https://themeforest.net/user/ajay138/

[TABLE OF CONTENTS]

1.0 Custom Variables
2.0 Variable Reset, Bootstrap mixins & Functions
3.0 Common CSS
    3.1 Nav
    3.1 Header
    3.1 Footer
4.0 Page CSS
    4.1 Home
    4.2 About
    4.3 Price
    4.4 Blog
    4.5 Contact
    4.6 Error
    4.7 Login
    4.8 FAQ
    4.9 Careers
5.0 Reset
6.0 Custom
7.0 Animations
8.0 Customizer Styles

--*/
/************************ DASHBOARD UI KIT ***********************/
/*!
 * Bootstrap v4.6.2 (https://getbootstrap.com/)
 * Copyright 2011-2022 The Bootstrap Authors
 * Copyright 2011-2022 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --blue: #1d82f5;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #ea4c89;
  --red: #D30000;
  --orange: #fd7e14;
  --yellow: #FCBD01;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #1d82f5;
  --secondary: #6c757d;
  --success: #28a745;
  --info: #17a2b8;
  --warning: #FCBD01;
  --danger: #D30000;
  --light: #f8f9fa;
  --dark: #343a40;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fff;
}

[tabindex="-1"]:focus:not(:focus-visible) {
  outline: 0 !important;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: #1d82f5;
  text-decoration: none;
  background-color: transparent;
}
a:hover {
  color: #085dbd;
  text-decoration: underline;
}

a:not([href]):not([class]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type=radio],
input[type=checkbox] {
  box-sizing: border-box;
  padding: 0;
}

textarea {
  overflow: auto;
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

progress {
  vertical-align: baseline;
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

summary {
  display: list-item;
  cursor: pointer;
}

template {
  display: none;
}

[hidden] {
  display: none !important;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

h1, .h1 {
  font-size: 2.5rem;
}

h2, .h2 {
  font-size: 2rem;
}

h3, .h3 {
  font-size: 1.75rem;
}

h4, .h4 {
  font-size: 1.5rem;
}

h5, .h5 {
  font-size: 1.25rem;
}

h6, .h6 {
  font-size: 1rem;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}

.display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2;
}

.display-3 {
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.2;
}

.display-4 {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.2;
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

small,
.small {
  font-size: 0.875em;
  font-weight: 400;
}

mark,
.mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 90%;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.blockquote-footer {
  display: block;
  font-size: 0.875em;
  color: #6c757d;
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 90%;
  color: #6c757d;
}

code {
  font-size: 87.5%;
  color: #ea4c89;
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem;
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}

pre {
  display: block;
  font-size: 87.5%;
  color: #212529;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

.container,
.container-fluid,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1140px;
  }
}
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.no-gutters > .col,
.no-gutters > [class*=col-] {
  padding-right: 0;
  padding-left: 0;
}

.col-xl,
.col-xl-auto, .col-xl-12, .col-xl-11, .col-xl-10, .col-xl-9, .col-xl-8, .col-xl-7, .col-xl-6, .col-xl-5, .col-xl-4, .col-xl-3, .col-xl-2, .col-xl-1, .col-lg,
.col-lg-auto, .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1, .col-md,
.col-md-auto, .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1, .col-sm,
.col-sm-auto, .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1, .col,
.col-auto, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2, .col-1 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.row-cols-1 > * {
  flex: 0 0 100%;
  max-width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 50%;
  max-width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 33.3333333333%;
  max-width: 33.3333333333%;
}

.row-cols-4 > * {
  flex: 0 0 25%;
  max-width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 20%;
  max-width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 16.6666666667%;
  max-width: 16.6666666667%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.col-1 {
  flex: 0 0 8.33333333%;
  max-width: 8.33333333%;
}

.col-2 {
  flex: 0 0 16.66666667%;
  max-width: 16.66666667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.33333333%;
  max-width: 33.33333333%;
}

.col-5 {
  flex: 0 0 41.66666667%;
  max-width: 41.66666667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.33333333%;
  max-width: 58.33333333%;
}

.col-8 {
  flex: 0 0 66.66666667%;
  max-width: 66.66666667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.33333333%;
  max-width: 83.33333333%;
}

.col-11 {
  flex: 0 0 91.66666667%;
  max-width: 91.66666667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  order: -1;
}

.order-last {
  order: 13;
}

.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

.order-10 {
  order: 10;
}

.order-11 {
  order: 11;
}

.order-12 {
  order: 12;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-sm-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-sm-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-sm-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-sm-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-sm-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-sm-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-sm-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-sm-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-sm-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-sm-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-sm-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-sm-first {
    order: -1;
  }
  .order-sm-last {
    order: 13;
  }
  .order-sm-0 {
    order: 0;
  }
  .order-sm-1 {
    order: 1;
  }
  .order-sm-2 {
    order: 2;
  }
  .order-sm-3 {
    order: 3;
  }
  .order-sm-4 {
    order: 4;
  }
  .order-sm-5 {
    order: 5;
  }
  .order-sm-6 {
    order: 6;
  }
  .order-sm-7 {
    order: 7;
  }
  .order-sm-8 {
    order: 8;
  }
  .order-sm-9 {
    order: 9;
  }
  .order-sm-10 {
    order: 10;
  }
  .order-sm-11 {
    order: 11;
  }
  .order-sm-12 {
    order: 12;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-md-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-md-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-md-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-md-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-md-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-md-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-md-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-md-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-md-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-md-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-md-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-md-first {
    order: -1;
  }
  .order-md-last {
    order: 13;
  }
  .order-md-0 {
    order: 0;
  }
  .order-md-1 {
    order: 1;
  }
  .order-md-2 {
    order: 2;
  }
  .order-md-3 {
    order: 3;
  }
  .order-md-4 {
    order: 4;
  }
  .order-md-5 {
    order: 5;
  }
  .order-md-6 {
    order: 6;
  }
  .order-md-7 {
    order: 7;
  }
  .order-md-8 {
    order: 8;
  }
  .order-md-9 {
    order: 9;
  }
  .order-md-10 {
    order: 10;
  }
  .order-md-11 {
    order: 11;
  }
  .order-md-12 {
    order: 12;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .offset-md-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-lg-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-lg-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-lg-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-lg-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-lg-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-lg-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-lg-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-lg-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-lg-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-lg-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-lg-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-lg-first {
    order: -1;
  }
  .order-lg-last {
    order: 13;
  }
  .order-lg-0 {
    order: 0;
  }
  .order-lg-1 {
    order: 1;
  }
  .order-lg-2 {
    order: 2;
  }
  .order-lg-3 {
    order: 3;
  }
  .order-lg-4 {
    order: 4;
  }
  .order-lg-5 {
    order: 5;
  }
  .order-lg-6 {
    order: 6;
  }
  .order-lg-7 {
    order: 7;
  }
  .order-lg-8 {
    order: 8;
  }
  .order-lg-9 {
    order: 9;
  }
  .order-lg-10 {
    order: 10;
  }
  .order-lg-11 {
    order: 11;
  }
  .order-lg-12 {
    order: 12;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-xl-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-xl-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-xl-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-xl-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-xl-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-xl-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-xl-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-xl-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-xl-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-xl-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xl-first {
    order: -1;
  }
  .order-xl-last {
    order: 13;
  }
  .order-xl-0 {
    order: 0;
  }
  .order-xl-1 {
    order: 1;
  }
  .order-xl-2 {
    order: 2;
  }
  .order-xl-3 {
    order: 3;
  }
  .order-xl-4 {
    order: 4;
  }
  .order-xl-5 {
    order: 5;
  }
  .order-xl-6 {
    order: 6;
  }
  .order-xl-7 {
    order: 7;
  }
  .order-xl-8 {
    order: 8;
  }
  .order-xl-9 {
    order: 9;
  }
  .order-xl-10 {
    order: 10;
  }
  .order-xl-11 {
    order: 11;
  }
  .order-xl-12 {
    order: 12;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
}
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
}
.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
}
.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
}
.table tbody + tbody {
  border-top: 2px solid #dee2e6;
}

.table-sm th,
.table-sm td {
  padding: 0.3rem;
}

.table-bordered {
  border: 1px solid #dee2e6;
}
.table-bordered th,
.table-bordered td {
  border: 1px solid #dee2e6;
}
.table-bordered thead th,
.table-bordered thead td {
  border-bottom-width: 2px;
}

.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
  border: 0;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
  color: #212529;
  background-color: rgba(0, 0, 0, 0.075);
}

.table-primary,
.table-primary > th,
.table-primary > td {
  background-color: #c0dcfc;
}
.table-primary th,
.table-primary td,
.table-primary thead th,
.table-primary tbody + tbody {
  border-color: #89befa;
}

.table-hover .table-primary:hover {
  background-color: #a8cefb;
}
.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
  background-color: #a8cefb;
}

.table-secondary,
.table-secondary > th,
.table-secondary > td {
  background-color: #d6d8db;
}
.table-secondary th,
.table-secondary td,
.table-secondary thead th,
.table-secondary tbody + tbody {
  border-color: #b3b7bb;
}

.table-hover .table-secondary:hover {
  background-color: #c8cbcf;
}
.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
  background-color: #c8cbcf;
}

.table-success,
.table-success > th,
.table-success > td {
  background-color: #c3e6cb;
}
.table-success th,
.table-success td,
.table-success thead th,
.table-success tbody + tbody {
  border-color: #8fd19e;
}

.table-hover .table-success:hover {
  background-color: #b1dfbb;
}
.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #b1dfbb;
}

.table-info,
.table-info > th,
.table-info > td {
  background-color: #bee5eb;
}
.table-info th,
.table-info td,
.table-info thead th,
.table-info tbody + tbody {
  border-color: #86cfda;
}

.table-hover .table-info:hover {
  background-color: #abdde5;
}
.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #abdde5;
}

.table-warning,
.table-warning > th,
.table-warning > td {
  background-color: #feedb8;
}
.table-warning th,
.table-warning td,
.table-warning thead th,
.table-warning tbody + tbody {
  border-color: #fddd7b;
}

.table-hover .table-warning:hover {
  background-color: #fee79f;
}
.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #fee79f;
}

.table-danger,
.table-danger > th,
.table-danger > td {
  background-color: #f3b8b8;
}
.table-danger th,
.table-danger td,
.table-danger thead th,
.table-danger tbody + tbody {
  border-color: #e87a7a;
}

.table-hover .table-danger:hover {
  background-color: #efa2a2;
}
.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #efa2a2;
}

.table-light,
.table-light > th,
.table-light > td {
  background-color: #fdfdfe;
}
.table-light th,
.table-light td,
.table-light thead th,
.table-light tbody + tbody {
  border-color: #fbfcfc;
}

.table-hover .table-light:hover {
  background-color: #ececf6;
}
.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
  background-color: #ececf6;
}

.table-dark,
.table-dark > th,
.table-dark > td {
  background-color: #c6c8ca;
}
.table-dark th,
.table-dark td,
.table-dark thead th,
.table-dark tbody + tbody {
  border-color: #95999c;
}

.table-hover .table-dark:hover {
  background-color: #b9bbbe;
}
.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
  background-color: #b9bbbe;
}

.table-active,
.table-active > th,
.table-active > td {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover {
  background-color: rgba(0, 0, 0, 0.075);
}
.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}

.table .thead-dark th {
  color: #fff;
  background-color: #343a40;
  border-color: #454d55;
}
.table .thead-light th {
  color: #495057;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.table-dark {
  color: #fff;
  background-color: #343a40;
}
.table-dark th,
.table-dark td,
.table-dark thead th {
  border-color: #454d55;
}
.table-dark.table-bordered {
  border: 0;
}
.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}
.table-dark.table-hover tbody tr:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.075);
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-sm > .table-bordered {
    border: 0;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-md > .table-bordered {
    border: 0;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-lg > .table-bordered {
    border: 0;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-xl > .table-bordered {
    border: 0;
  }
}
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.table-responsive > .table-bordered {
  border: 0;
}

.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}
.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #97c6fa;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}
.form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}
.form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}
.form-control:disabled, .form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}

input[type=date].form-control,
input[type=time].form-control,
input[type=datetime-local].form-control,
input[type=month].form-control {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

select.form-control:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #495057;
}
select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

.form-control-file,
.form-control-range {
  display: block;
  width: 100%;
}

.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
  line-height: 1.5;
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.375rem 0;
  margin-bottom: 0;
  font-size: 1rem;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.form-control-lg {
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

select.form-control[size], select.form-control[multiple] {
  height: auto;
}

textarea.form-control {
  height: auto;
}

.form-group {
  margin-bottom: 1rem;
}

.form-text {
  display: block;
  margin-top: 0.25rem;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}
.form-row > .col,
.form-row > [class*=col-] {
  padding-right: 5px;
  padding-left: 5px;
}

.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}

.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  color: #6c757d;
}

.form-check-label {
  margin-bottom: 0;
}

.form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}
.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #28a745;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(40, 167, 69, 0.9);
  border-radius: 0.25rem;
}
.form-row > .col > .valid-tooltip, .form-row > [class*=col-] > .valid-tooltip {
  left: 5px;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #28a745;
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.was-validated select.form-control:valid, select.form-control.is-valid {
  padding-right: 3rem !important;
  background-position: right 1.5rem center;
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.was-validated .custom-select:valid, .custom-select.is-valid {
  border-color: #28a745;
  padding-right: calc(0.75em + 2.3125rem) !important;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.75rem center/8px 10px no-repeat, #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) no-repeat;
}
.was-validated .custom-select:valid:focus, .custom-select.is-valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #28a745;
}
.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip, .form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .custom-control-input:valid ~ .custom-control-label, .custom-control-input.is-valid ~ .custom-control-label {
  color: #28a745;
}
.was-validated .custom-control-input:valid ~ .custom-control-label::before, .custom-control-input.is-valid ~ .custom-control-label::before {
  border-color: #28a745;
}
.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .custom-control-input.is-valid:checked ~ .custom-control-label::before {
  border-color: #34ce57;
  background-color: #34ce57;
}
.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .custom-control-input.is-valid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #28a745;
}

.was-validated .custom-file-input:valid ~ .custom-file-label, .custom-file-input.is-valid ~ .custom-file-label {
  border-color: #28a745;
}
.was-validated .custom-file-input:valid:focus ~ .custom-file-label, .custom-file-input.is-valid:focus ~ .custom-file-label {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #D30000;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(211, 0, 0, 0.9);
  border-radius: 0.25rem;
}
.form-row > .col > .invalid-tooltip, .form-row > [class*=col-] > .invalid-tooltip {
  left: 5px;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #D30000;
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23D30000' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23D30000' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #D30000;
  box-shadow: 0 0 0 0.2rem rgba(211, 0, 0, 0.25);
}

.was-validated select.form-control:invalid, select.form-control.is-invalid {
  padding-right: 3rem !important;
  background-position: right 1.5rem center;
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.was-validated .custom-select:invalid, .custom-select.is-invalid {
  border-color: #D30000;
  padding-right: calc(0.75em + 2.3125rem) !important;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.75rem center/8px 10px no-repeat, #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23D30000' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23D30000' stroke='none'/%3e%3c/svg%3e") center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) no-repeat;
}
.was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus {
  border-color: #D30000;
  box-shadow: 0 0 0 0.2rem rgba(211, 0, 0, 0.25);
}

.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #D30000;
}
.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip, .form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .custom-control-input:invalid ~ .custom-control-label, .custom-control-input.is-invalid ~ .custom-control-label {
  color: #D30000;
}
.was-validated .custom-control-input:invalid ~ .custom-control-label::before, .custom-control-input.is-invalid ~ .custom-control-label::before {
  border-color: #D30000;
}
.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .custom-control-input.is-invalid:checked ~ .custom-control-label::before {
  border-color: #ff0707;
  background-color: #ff0707;
}
.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .custom-control-input.is-invalid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(211, 0, 0, 0.25);
}
.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #D30000;
}

.was-validated .custom-file-input:invalid ~ .custom-file-label, .custom-file-input.is-invalid ~ .custom-file-label {
  border-color: #D30000;
}
.was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .custom-file-input.is-invalid:focus ~ .custom-file-label {
  border-color: #D30000;
  box-shadow: 0 0 0 0.2rem rgba(211, 0, 0, 0.25);
}

.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}
.form-inline .form-check {
  width: 100%;
}
@media (min-width: 576px) {
  .form-inline label {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
  }
  .form-inline .form-group {
    display: flex;
    flex: 0 0 auto;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 0;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-plaintext {
    display: inline-block;
  }
  .form-inline .input-group,
.form-inline .custom-select {
    width: auto;
  }
  .form-inline .form-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }
  .form-inline .form-check-input {
    position: relative;
    flex-shrink: 0;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }
  .form-inline .custom-control {
    align-items: center;
    justify-content: center;
  }
  .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}

.btn {
  display: inline-block;
  font-weight: 400;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  color: #212529;
  text-decoration: none;
}
.btn:focus, .btn.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}
.btn.disabled, .btn:disabled {
  opacity: 0.65;
}
.btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}
a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none;
}

.btn-primary {
  color: #fff;
  background-color: #1d82f5;
  border-color: #1d82f5;
}
.btn-primary:hover {
  color: #fff;
  background-color: #0a6fe2;
  border-color: #0969d6;
}
.btn-primary:focus, .btn-primary.focus {
  color: #fff;
  background-color: #0a6fe2;
  border-color: #0969d6;
  box-shadow: 0 0 0 0.2rem rgba(63, 149, 247, 0.5);
}
.btn-primary.disabled, .btn-primary:disabled {
  color: #fff;
  background-color: #1d82f5;
  border-color: #1d82f5;
}
.btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active, .show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #0969d6;
  border-color: #0963c9;
}
.btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(63, 149, 247, 0.5);
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}
.btn-secondary:focus, .btn-secondary.focus {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}
.btn-secondary.disabled, .btn-secondary:disabled {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #545b62;
  border-color: #4e555b;
}
.btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}

.btn-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.btn-success:hover {
  color: #fff;
  background-color: #218838;
  border-color: #1e7e34;
}
.btn-success:focus, .btn-success.focus {
  color: #fff;
  background-color: #218838;
  border-color: #1e7e34;
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}
.btn-success.disabled, .btn-success:disabled {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active, .show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #1e7e34;
  border-color: #1c7430;
}
.btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus, .show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}

.btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.btn-info:hover {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
}
.btn-info:focus, .btn-info.focus {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}
.btn-info.disabled, .btn-info:disabled {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active, .show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #117a8b;
  border-color: #10707f;
}
.btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus, .show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}

.btn-warning {
  color: #212529;
  background-color: #FCBD01;
  border-color: #FCBD01;
}
.btn-warning:hover {
  color: #212529;
  background-color: #d6a001;
  border-color: #c99701;
}
.btn-warning:focus, .btn-warning.focus {
  color: #212529;
  background-color: #d6a001;
  border-color: #c99701;
  box-shadow: 0 0 0 0.2rem rgba(219, 166, 7, 0.5);
}
.btn-warning.disabled, .btn-warning:disabled {
  color: #212529;
  background-color: #FCBD01;
  border-color: #FCBD01;
}
.btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active, .show > .btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #c99701;
  border-color: #bd8d01;
}
.btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(219, 166, 7, 0.5);
}

.btn-danger {
  color: #fff;
  background-color: #D30000;
  border-color: #D30000;
}
.btn-danger:hover {
  color: #fff;
  background-color: #ad0000;
  border-color: #a00000;
}
.btn-danger:focus, .btn-danger.focus {
  color: #fff;
  background-color: #ad0000;
  border-color: #a00000;
  box-shadow: 0 0 0 0.2rem rgba(218, 38, 38, 0.5);
}
.btn-danger.disabled, .btn-danger:disabled {
  color: #fff;
  background-color: #D30000;
  border-color: #D30000;
}
.btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active, .show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #a00000;
  border-color: #930000;
}
.btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(218, 38, 38, 0.5);
}

.btn-light {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-light:hover {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
}
.btn-light:focus, .btn-light.focus {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}
.btn-light.disabled, .btn-light:disabled {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active, .show > .btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e5;
  border-color: #d3d9df;
}
.btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus, .show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}

.btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.btn-dark:hover {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
}
.btn-dark:focus, .btn-dark.focus {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}
.btn-dark.disabled, .btn-dark:disabled {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active, .show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #1d2124;
  border-color: #171a1d;
}
.btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}

.btn-outline-primary {
  color: #1d82f5;
  border-color: #1d82f5;
}
.btn-outline-primary:hover {
  color: #fff;
  background-color: #1d82f5;
  border-color: #1d82f5;
}
.btn-outline-primary:focus, .btn-outline-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.5);
}
.btn-outline-primary.disabled, .btn-outline-primary:disabled {
  color: #1d82f5;
  background-color: transparent;
}
.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #1d82f5;
  border-color: #1d82f5;
}
.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.5);
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}
.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-outline-secondary:focus, .btn-outline-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}
.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
  color: #6c757d;
  background-color: transparent;
}
.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active, .show > .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-outline-success {
  color: #28a745;
  border-color: #28a745;
}
.btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.btn-outline-success:focus, .btn-outline-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}
.btn-outline-success.disabled, .btn-outline-success:disabled {
  color: #28a745;
  background-color: transparent;
}
.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active, .show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}
.btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.btn-outline-info:focus, .btn-outline-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}
.btn-outline-info.disabled, .btn-outline-info:disabled {
  color: #17a2b8;
  background-color: transparent;
}
.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active, .show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-warning {
  color: #FCBD01;
  border-color: #FCBD01;
}
.btn-outline-warning:hover {
  color: #212529;
  background-color: #FCBD01;
  border-color: #FCBD01;
}
.btn-outline-warning:focus, .btn-outline-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(252, 189, 1, 0.5);
}
.btn-outline-warning.disabled, .btn-outline-warning:disabled {
  color: #FCBD01;
  background-color: transparent;
}
.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active, .show > .btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #FCBD01;
  border-color: #FCBD01;
}
.btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(252, 189, 1, 0.5);
}

.btn-outline-danger {
  color: #D30000;
  border-color: #D30000;
}
.btn-outline-danger:hover {
  color: #fff;
  background-color: #D30000;
  border-color: #D30000;
}
.btn-outline-danger:focus, .btn-outline-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(211, 0, 0, 0.5);
}
.btn-outline-danger.disabled, .btn-outline-danger:disabled {
  color: #D30000;
  background-color: transparent;
}
.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active, .show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #D30000;
  border-color: #D30000;
}
.btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(211, 0, 0, 0.5);
}

.btn-outline-light {
  color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-outline-light:hover {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-outline-light:focus, .btn-outline-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}
.btn-outline-light.disabled, .btn-outline-light:disabled {
  color: #f8f9fa;
  background-color: transparent;
}
.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active, .show > .btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-dark {
  color: #343a40;
  border-color: #343a40;
}
.btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.btn-outline-dark:focus, .btn-outline-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}
.btn-outline-dark.disabled, .btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}
.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active, .show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-link {
  font-weight: 400;
  color: #1d82f5;
  text-decoration: none;
}
.btn-link:hover {
  color: #085dbd;
  text-decoration: underline;
}
.btn-link:focus, .btn-link.focus {
  text-decoration: underline;
}
.btn-link:disabled, .btn-link.disabled {
  color: #6c757d;
  pointer-events: none;
}

.btn-lg, .btn-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.btn-sm, .btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 0.5rem;
}

input[type=submit].btn-block,
input[type=reset].btn-block,
input[type=button].btn-block {
  width: 100%;
}

.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.width {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.width {
    transition: none;
  }
}

.dropup,
.dropright,
.dropdown,
.dropleft {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.dropdown-menu-left {
  right: auto;
  left: 0;
}

.dropdown-menu-right {
  right: 0;
  left: auto;
}

@media (min-width: 576px) {
  .dropdown-menu-sm-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-sm-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-md-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-lg-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xl-right {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}
.dropright .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropright .dropdown-toggle::after {
  vertical-align: 0;
}

.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}
.dropleft .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropleft .dropdown-toggle::after {
  display: none;
}
.dropleft .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropleft .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-menu[x-placement^=top], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left] {
  right: auto;
  bottom: auto;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #e9ecef;
}
.dropdown-item.active, .dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #1d82f5;
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: #adb5bd;
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
  color: #212529;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  flex: 1 1 auto;
}
.btn-group > .btn:hover,
.btn-group-vertical > .btn:hover {
  z-index: 1;
}
.btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropleft .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.btn-group-toggle > .btn,
.btn-group-toggle > .btn-group > .btn {
  margin-bottom: 0;
}
.btn-group-toggle > .btn input[type=radio],
.btn-group-toggle > .btn input[type=checkbox],
.btn-group-toggle > .btn-group > .btn input[type=radio],
.btn-group-toggle > .btn-group > .btn input[type=checkbox] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-control-plaintext,
.input-group > .custom-select,
.input-group > .custom-file {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
  margin-bottom: 0;
}
.input-group > .form-control + .form-control,
.input-group > .form-control + .custom-select,
.input-group > .form-control + .custom-file,
.input-group > .form-control-plaintext + .form-control,
.input-group > .form-control-plaintext + .custom-select,
.input-group > .form-control-plaintext + .custom-file,
.input-group > .custom-select + .form-control,
.input-group > .custom-select + .custom-select,
.input-group > .custom-select + .custom-file,
.input-group > .custom-file + .form-control,
.input-group > .custom-file + .custom-select,
.input-group > .custom-file + .custom-file {
  margin-left: -1px;
}
.input-group > .form-control:focus,
.input-group > .custom-select:focus,
.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {
  z-index: 3;
}
.input-group > .custom-file .custom-file-input:focus {
  z-index: 4;
}
.input-group > .form-control:not(:first-child),
.input-group > .custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .custom-file {
  display: flex;
  align-items: center;
}
.input-group > .custom-file:not(:last-child) .custom-file-label, .input-group > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group:not(.has-validation) > .form-control:not(:last-child),
.input-group:not(.has-validation) > .custom-select:not(:last-child),
.input-group:not(.has-validation) > .custom-file:not(:last-child) .custom-file-label,
.input-group:not(.has-validation) > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > .form-control:nth-last-child(n+3),
.input-group.has-validation > .custom-select:nth-last-child(n+3),
.input-group.has-validation > .custom-file:nth-last-child(n+3) .custom-file-label,
.input-group.has-validation > .custom-file:nth-last-child(n+3) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group-prepend,
.input-group-append {
  display: flex;
}
.input-group-prepend .btn,
.input-group-append .btn {
  position: relative;
  z-index: 2;
}
.input-group-prepend .btn:focus,
.input-group-append .btn:focus {
  z-index: 3;
}
.input-group-prepend .btn + .btn,
.input-group-prepend .btn + .input-group-text,
.input-group-prepend .input-group-text + .input-group-text,
.input-group-prepend .input-group-text + .btn,
.input-group-append .btn + .btn,
.input-group-append .btn + .input-group-text,
.input-group-append .input-group-text + .input-group-text,
.input-group-append .input-group-text + .btn {
  margin-left: -1px;
}

.input-group-prepend {
  margin-right: -1px;
}

.input-group-append {
  margin-left: -1px;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}
.input-group-text input[type=radio],
.input-group-text input[type=checkbox] {
  margin-top: 0;
}

.input-group-lg > .form-control:not(textarea),
.input-group-lg > .custom-select {
  height: calc(1.5em + 1rem + 2px);
}

.input-group-lg > .form-control,
.input-group-lg > .custom-select,
.input-group-lg > .input-group-prepend > .input-group-text,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-append > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.input-group-sm > .form-control:not(textarea),
.input-group-sm > .custom-select {
  height: calc(1.5em + 0.5rem + 2px);
}

.input-group-sm > .form-control,
.input-group-sm > .custom-select,
.input-group-sm > .input-group-prepend > .input-group-text,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-append > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.input-group-lg > .custom-select,
.input-group-sm > .custom-select {
  padding-right: 1.75rem;
}

.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group:not(.has-validation) > .input-group-append:not(:last-child) > .btn,
.input-group:not(.has-validation) > .input-group-append:not(:last-child) > .input-group-text,
.input-group.has-validation > .input-group-append:nth-last-child(n+3) > .btn,
.input-group.has-validation > .input-group-append:nth-last-child(n+3) > .input-group-text,
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.custom-control {
  position: relative;
  z-index: 1;
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
  -webkit-print-color-adjust: exact;
     color-adjust: exact;
          print-color-adjust: exact;
}

.custom-control-inline {
  display: inline-flex;
  margin-right: 1rem;
}

.custom-control-input {
  position: absolute;
  left: 0;
  z-index: -1;
  width: 1rem;
  height: 1.25rem;
  opacity: 0;
}
.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #1d82f5;
  background-color: #1d82f5;
}
.custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}
.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #97c6fa;
}
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #fff;
  background-color: #c8e1fd;
  border-color: #c8e1fd;
}
.custom-control-input[disabled] ~ .custom-control-label, .custom-control-input:disabled ~ .custom-control-label {
  color: #6c757d;
}
.custom-control-input[disabled] ~ .custom-control-label::before, .custom-control-input:disabled ~ .custom-control-label::before {
  background-color: #e9ecef;
}

.custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}
.custom-control-label::before {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  border: 1px solid #adb5bd;
}
.custom-control-label::after {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background: 50%/50% 50% no-repeat;
}

.custom-checkbox .custom-control-label::before {
  border-radius: 0.25rem;
}
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  border-color: #1d82f5;
  background-color: #1d82f5;
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}
.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(29, 130, 245, 0.5);
}
.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
  background-color: rgba(29, 130, 245, 0.5);
}

.custom-radio .custom-control-label::before {
  border-radius: 50%;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(29, 130, 245, 0.5);
}

.custom-switch {
  padding-left: 2.25rem;
}
.custom-switch .custom-control-label::before {
  left: -2.25rem;
  width: 1.75rem;
  pointer-events: all;
  border-radius: 0.5rem;
}
.custom-switch .custom-control-label::after {
  top: calc(0.25rem + 2px);
  left: calc(-2.25rem + 2px);
  width: calc(1rem - 4px);
  height: calc(1rem - 4px);
  background-color: #adb5bd;
  border-radius: 0.5rem;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .custom-switch .custom-control-label::after {
    transition: none;
  }
}
.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fff;
  transform: translateX(0.75rem);
}
.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(29, 130, 245, 0.5);
}

.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.75rem center/8px 10px no-repeat;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.custom-select:focus {
  border-color: #97c6fa;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}
.custom-select:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}
.custom-select[multiple], .custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: 0.75rem;
  background-image: none;
}
.custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef;
}
.custom-select::-ms-expand {
  display: none;
}
.custom-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #495057;
}

.custom-select-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
}

.custom-select-lg {
  height: calc(1.5em + 1rem + 2px);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
}

.custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  margin-bottom: 0;
}

.custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  margin: 0;
  overflow: hidden;
  opacity: 0;
}
.custom-file-input:focus ~ .custom-file-label {
  border-color: #97c6fa;
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}
.custom-file-input[disabled] ~ .custom-file-label, .custom-file-input:disabled ~ .custom-file-label {
  background-color: #e9ecef;
}
.custom-file-input:lang(en) ~ .custom-file-label::after {
  content: "Browse";
}
.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse);
}

.custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  overflow: hidden;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}
.custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(1.5em + 0.75rem);
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  content: "Browse";
  background-color: #e9ecef;
  border-left: inherit;
  border-radius: 0 0.25rem 0.25rem 0;
}

.custom-range {
  width: 100%;
  height: 1.4rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.custom-range:focus {
  outline: 0;
}
.custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}
.custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}
.custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}
.custom-range::-moz-focus-outer {
  border: 0;
}
.custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #1d82f5;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.custom-range::-webkit-slider-thumb:active {
  background-color: #c8e1fd;
}
.custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #1d82f5;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.custom-range::-moz-range-thumb:active {
  background-color: #c8e1fd;
}
.custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
  background-color: #1d82f5;
  border: 0;
  border-radius: 1rem;
  -ms-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-ms-thumb {
    -ms-transition: none;
    transition: none;
  }
}
.custom-range::-ms-thumb:active {
  background-color: #c8e1fd;
}
.custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: 0.5rem;
}
.custom-range::-ms-fill-lower {
  background-color: #dee2e6;
  border-radius: 1rem;
}
.custom-range::-ms-fill-upper {
  margin-right: 15px;
  background-color: #dee2e6;
  border-radius: 1rem;
}
.custom-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.custom-range:disabled::-webkit-slider-runnable-track {
  cursor: default;
}
.custom-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}
.custom-range:disabled::-moz-range-track {
  cursor: default;
}
.custom-range:disabled::-ms-thumb {
  background-color: #adb5bd;
}

.custom-control-label::before,
.custom-file-label,
.custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .custom-control-label::before,
.custom-file-label,
.custom-select {
    transition: none;
  }
}

.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}
.nav-link:hover, .nav-link:focus {
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -1px;
  background-color: transparent;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: #e9ecef #e9ecef #dee2e6;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #1d82f5;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}
.navbar .container,
.navbar .container-fluid,
.navbar .container-sm,
.navbar .container-md,
.navbar .container-lg,
.navbar .container-xl {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  display: inline-block;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  line-height: inherit;
  white-space: nowrap;
}
.navbar-brand:hover, .navbar-brand:focus {
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}
.navbar-nav .dropdown-menu {
  position: static;
  float: none;
}

.navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.navbar-toggler:hover, .navbar-toggler:focus {
  text-decoration: none;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: 50%/100% 100% no-repeat;
}

.navbar-nav-scroll {
  max-height: 75vh;
  overflow-y: auto;
}

@media (max-width: 575.98px) {
  .navbar-expand-sm > .container,
.navbar-expand-sm > .container-fluid,
.navbar-expand-sm > .container-sm,
.navbar-expand-sm > .container-md,
.navbar-expand-sm > .container-lg,
.navbar-expand-sm > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm > .container,
.navbar-expand-sm > .container-fluid,
.navbar-expand-sm > .container-sm,
.navbar-expand-sm > .container-md,
.navbar-expand-sm > .container-lg,
.navbar-expand-sm > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}
@media (max-width: 767.98px) {
  .navbar-expand-md > .container,
.navbar-expand-md > .container-fluid,
.navbar-expand-md > .container-sm,
.navbar-expand-md > .container-md,
.navbar-expand-md > .container-lg,
.navbar-expand-md > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md > .container,
.navbar-expand-md > .container-fluid,
.navbar-expand-md > .container-sm,
.navbar-expand-md > .container-md,
.navbar-expand-md > .container-lg,
.navbar-expand-md > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .navbar-expand-lg > .container,
.navbar-expand-lg > .container-fluid,
.navbar-expand-lg > .container-sm,
.navbar-expand-lg > .container-md,
.navbar-expand-lg > .container-lg,
.navbar-expand-lg > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg > .container,
.navbar-expand-lg > .container-fluid,
.navbar-expand-lg > .container-sm,
.navbar-expand-lg > .container-md,
.navbar-expand-lg > .container-lg,
.navbar-expand-lg > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .navbar-expand-xl > .container,
.navbar-expand-xl > .container-fluid,
.navbar-expand-xl > .container-sm,
.navbar-expand-xl > .container-md,
.navbar-expand-xl > .container-lg,
.navbar-expand-xl > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl > .container,
.navbar-expand-xl > .container-fluid,
.navbar-expand-xl > .container-sm,
.navbar-expand-xl > .container-md,
.navbar-expand-xl > .container-lg,
.navbar-expand-xl > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}
.navbar-expand {
  flex-flow: row nowrap;
  justify-content: flex-start;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-sm,
.navbar-expand > .container-md,
.navbar-expand > .container-lg,
.navbar-expand > .container-xl {
  padding-right: 0;
  padding-left: 0;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-sm,
.navbar-expand > .container-md,
.navbar-expand > .container-lg,
.navbar-expand > .container-xl {
  flex-wrap: nowrap;
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}

.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}
.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus {
  color: rgba(0, 0, 0, 0.7);
}
.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}
.navbar-light .navbar-nav .show > .nav-link,
.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}
.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.5);
}
.navbar-light .navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-text a:hover, .navbar-light .navbar-text a:focus {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-dark .navbar-brand {
  color: #fff;
}
.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus {
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}
.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {
  color: rgba(255, 255, 255, 0.75);
}
.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}
.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}
.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}
.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.5);
}
.navbar-dark .navbar-text a {
  color: #fff;
}
.navbar-dark .navbar-text a:hover, .navbar-dark .navbar-text a:focus {
  color: #fff;
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}

.card-title {
  margin-bottom: 0.75rem;
}

.card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}
.card-link + .card-link {
  margin-left: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}
.card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

.card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}

.card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
  border-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-top,
.card-img-bottom {
  flex-shrink: 0;
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

.card-deck .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .card-deck {
    display: flex;
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }
  .card-deck .card {
    flex: 1 0 0%;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}

.card-group > .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
.card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
.card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
.card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
.card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.card-columns .card {
  margin-bottom: 0.75rem;
}
@media (min-width: 576px) {
  .card-columns {
    -moz-column-count: 3;
         column-count: 3;
    -moz-column-gap: 1.25rem;
         column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }
  .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}

.accordion {
  overflow-anchor: none;
}
.accordion > .card {
  overflow: hidden;
}
.accordion > .card:not(:last-of-type) {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.accordion > .card:not(:first-of-type) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.accordion > .card > .card-header {
  border-radius: 0;
  margin-bottom: -1px;
}

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: 0.5rem;
  color: #6c757d;
  content: "/";
}
.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: underline;
}
.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: none;
}
.breadcrumb-item.active {
  color: #6c757d;
}

.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #1d82f5;
  background-color: #fff;
  border: 1px solid #dee2e6;
}
.page-link:hover {
  z-index: 2;
  color: #085dbd;
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6;
}
.page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.25);
}

.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #1d82f5;
  border-color: #1d82f5;
}
.page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #dee2e6;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  line-height: 1.5;
}
.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
}
.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}
.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}

.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .badge {
    transition: none;
  }
}
a.badge:hover, a.badge:focus {
  text-decoration: none;
}

.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}

.badge-primary {
  color: #fff;
  background-color: #1d82f5;
}
a.badge-primary:hover, a.badge-primary:focus {
  color: #fff;
  background-color: #0969d6;
}
a.badge-primary:focus, a.badge-primary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.5);
}

.badge-secondary {
  color: #fff;
  background-color: #6c757d;
}
a.badge-secondary:hover, a.badge-secondary:focus {
  color: #fff;
  background-color: #545b62;
}
a.badge-secondary:focus, a.badge-secondary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.badge-success {
  color: #fff;
  background-color: #28a745;
}
a.badge-success:hover, a.badge-success:focus {
  color: #fff;
  background-color: #1e7e34;
}
a.badge-success:focus, a.badge-success.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.badge-info {
  color: #fff;
  background-color: #17a2b8;
}
a.badge-info:hover, a.badge-info:focus {
  color: #fff;
  background-color: #117a8b;
}
a.badge-info:focus, a.badge-info.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.badge-warning {
  color: #212529;
  background-color: #FCBD01;
}
a.badge-warning:hover, a.badge-warning:focus {
  color: #212529;
  background-color: #c99701;
}
a.badge-warning:focus, a.badge-warning.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(252, 189, 1, 0.5);
}

.badge-danger {
  color: #fff;
  background-color: #D30000;
}
a.badge-danger:hover, a.badge-danger:focus {
  color: #fff;
  background-color: #a00000;
}
a.badge-danger:focus, a.badge-danger.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(211, 0, 0, 0.5);
}

.badge-light {
  color: #212529;
  background-color: #f8f9fa;
}
a.badge-light:hover, a.badge-light:focus {
  color: #212529;
  background-color: #dae0e5;
}
a.badge-light:focus, a.badge-light.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.badge-dark {
  color: #fff;
  background-color: #343a40;
}
a.badge-dark:hover, a.badge-dark:focus {
  color: #fff;
  background-color: #1d2124;
}
a.badge-dark:focus, a.badge-dark.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #e9ecef;
  border-radius: 0.3rem;
}
@media (min-width: 576px) {
  .jumbotron {
    padding: 4rem 2rem;
  }
}

.jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

.alert-dismissible {
  padding-right: 4rem;
}
.alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 0.75rem 1.25rem;
  color: inherit;
}

.alert-primary {
  color: #0f447f;
  background-color: #d2e6fd;
  border-color: #c0dcfc;
}
.alert-primary hr {
  border-top-color: #a8cefb;
}
.alert-primary .alert-link {
  color: #0a2c51;
}

.alert-secondary {
  color: #383d41;
  background-color: #e2e3e5;
  border-color: #d6d8db;
}
.alert-secondary hr {
  border-top-color: #c8cbcf;
}
.alert-secondary .alert-link {
  color: #202326;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}
.alert-success hr {
  border-top-color: #b1dfbb;
}
.alert-success .alert-link {
  color: #0b2e13;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}
.alert-info hr {
  border-top-color: #abdde5;
}
.alert-info .alert-link {
  color: #062c33;
}

.alert-warning {
  color: #836201;
  background-color: #fef2cc;
  border-color: #feedb8;
}
.alert-warning hr {
  border-top-color: #fee79f;
}
.alert-warning .alert-link {
  color: #503c01;
}

.alert-danger {
  color: #6e0000;
  background-color: #f6cccc;
  border-color: #f3b8b8;
}
.alert-danger hr {
  border-top-color: #efa2a2;
}
.alert-danger .alert-link {
  color: #3b0000;
}

.alert-light {
  color: #818182;
  background-color: #fefefe;
  border-color: #fdfdfe;
}
.alert-light hr {
  border-top-color: #ececf6;
}
.alert-light .alert-link {
  color: #686868;
}

.alert-dark {
  color: #1b1e21;
  background-color: #d6d8d9;
  border-color: #c6c8ca;
}
.alert-dark hr {
  border-top-color: #b9bbbe;
}
.alert-dark .alert-link {
  color: #040505;
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  line-height: 0;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #1d82f5;
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    animation: none;
  }
}

.media {
  display: flex;
  align-items: flex-start;
}

.media-body {
  flex: 1;
}

.list-group {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: 0.25rem;
}

.list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  color: #495057;
  text-decoration: none;
  background-color: #f8f9fa;
}
.list-group-item-action:active {
  color: #212529;
  background-color: #e9ecef;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
}
.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #1d82f5;
  border-color: #1d82f5;
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: -1px;
  border-top-width: 1px;
}

.list-group-horizontal {
  flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child {
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: 1px;
  border-left-width: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: -1px;
  border-left-width: 1px;
}

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 1px;
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-primary {
  color: #0f447f;
  background-color: #c0dcfc;
}
.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
  color: #0f447f;
  background-color: #a8cefb;
}
.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #0f447f;
  border-color: #0f447f;
}

.list-group-item-secondary {
  color: #383d41;
  background-color: #d6d8db;
}
.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {
  color: #383d41;
  background-color: #c8cbcf;
}
.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #383d41;
  border-color: #383d41;
}

.list-group-item-success {
  color: #155724;
  background-color: #c3e6cb;
}
.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {
  color: #155724;
  background-color: #b1dfbb;
}
.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #155724;
  border-color: #155724;
}

.list-group-item-info {
  color: #0c5460;
  background-color: #bee5eb;
}
.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {
  color: #0c5460;
  background-color: #abdde5;
}
.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #0c5460;
  border-color: #0c5460;
}

.list-group-item-warning {
  color: #836201;
  background-color: #feedb8;
}
.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {
  color: #836201;
  background-color: #fee79f;
}
.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #836201;
  border-color: #836201;
}

.list-group-item-danger {
  color: #6e0000;
  background-color: #f3b8b8;
}
.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {
  color: #6e0000;
  background-color: #efa2a2;
}
.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #6e0000;
  border-color: #6e0000;
}

.list-group-item-light {
  color: #818182;
  background-color: #fdfdfe;
}
.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {
  color: #818182;
  background-color: #ececf6;
}
.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #818182;
  border-color: #818182;
}

.list-group-item-dark {
  color: #1b1e21;
  background-color: #c6c8ca;
}
.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {
  color: #1b1e21;
  background-color: #b9bbbe;
}
.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #1b1e21;
  border-color: #1b1e21;
}

.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}
.close:hover {
  color: #000;
  text-decoration: none;
}
.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {
  opacity: 0.75;
}

button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
}

a.close.disabled {
  pointer-events: none;
}

.toast {
  flex-basis: 350px;
  max-width: 350px;
  font-size: 0.875rem;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  opacity: 0;
  border-radius: 0.25rem;
}
.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}
.toast.showing {
  opacity: 1;
}
.toast.show {
  display: block;
  opacity: 1;
}
.toast.hide {
  display: none;
}

.toast-header {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.toast-body {
  padding: 0.75rem;
}

.modal-open {
  overflow: hidden;
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  display: flex;
  max-height: calc(100% - 1rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}
.modal-dialog-scrollable .modal-header,
.modal-dialog-scrollable .modal-footer {
  flex-shrink: 0;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
}
.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  height: -moz-min-content;
  height: min-content;
  content: "";
}
.modal-dialog-centered.modal-dialog-scrollable {
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}
.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.5;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}
.modal-footer > * {
  margin: 0.25rem;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 3.5rem);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
    height: -moz-min-content;
    height: min-content;
  }
  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
.modal-xl {
    max-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: 0.9;
}
.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top, .bs-tooltip-auto[x-placement^=top] {
  padding: 0.4rem 0;
}
.bs-tooltip-top .arrow, .bs-tooltip-auto[x-placement^=top] .arrow {
  bottom: 0;
}
.bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^=top] .arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}

.bs-tooltip-right, .bs-tooltip-auto[x-placement^=right] {
  padding: 0 0.4rem;
}
.bs-tooltip-right .arrow, .bs-tooltip-auto[x-placement^=right] .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^=right] .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}

.bs-tooltip-bottom, .bs-tooltip-auto[x-placement^=bottom] {
  padding: 0.4rem 0;
}
.bs-tooltip-bottom .arrow, .bs-tooltip-auto[x-placement^=bottom] .arrow {
  top: 0;
}
.bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^=bottom] .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}

.bs-tooltip-left, .bs-tooltip-auto[x-placement^=left] {
  padding: 0 0.4rem;
}
.bs-tooltip-left .arrow, .bs-tooltip-auto[x-placement^=left] .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^=left] .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}
.popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}
.popover .arrow::before, .popover .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-popover-top, .bs-popover-auto[x-placement^=top] {
  margin-bottom: 0.5rem;
}
.bs-popover-top > .arrow, .bs-popover-auto[x-placement^=top] > .arrow {
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-top > .arrow::before, .bs-popover-auto[x-placement^=top] > .arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-top > .arrow::after, .bs-popover-auto[x-placement^=top] > .arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}

.bs-popover-right, .bs-popover-auto[x-placement^=right] {
  margin-left: 0.5rem;
}
.bs-popover-right > .arrow, .bs-popover-auto[x-placement^=right] > .arrow {
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}
.bs-popover-right > .arrow::before, .bs-popover-auto[x-placement^=right] > .arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-right > .arrow::after, .bs-popover-auto[x-placement^=right] > .arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}

.bs-popover-bottom, .bs-popover-auto[x-placement^=bottom] {
  margin-top: 0.5rem;
}
.bs-popover-bottom > .arrow, .bs-popover-auto[x-placement^=bottom] > .arrow {
  top: calc(-0.5rem - 1px);
}
.bs-popover-bottom > .arrow::before, .bs-popover-auto[x-placement^=bottom] > .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-bottom > .arrow::after, .bs-popover-auto[x-placement^=bottom] > .arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[x-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f7f7f7;
}

.bs-popover-left, .bs-popover-auto[x-placement^=left] {
  margin-right: 0.5rem;
}
.bs-popover-left > .arrow, .bs-popover-auto[x-placement^=left] > .arrow {
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}
.bs-popover-left > .arrow::before, .bs-popover-auto[x-placement^=left] > .arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-left > .arrow::after, .bs-popover-auto[x-placement^=left] > .arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}

.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 0.5rem 0.75rem;
  color: #212529;
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-left),
.active.carousel-item-right {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-right),
.active.carousel-item-left {
  transform: translateX(-100%);
}

.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-left,
.carousel-fade .carousel-item-prev.carousel-item-right {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #fff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
.carousel-control-next {
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: 50%/100% 100% no-repeat;
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  display: flex;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators li {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators li {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  border: 0.25em solid currentcolor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: 0.75s linear infinite spinner-border;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  background-color: currentcolor;
  border-radius: 50%;
  opacity: 0;
  animation: 0.75s linear infinite spinner-grow;
}

.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
.spinner-grow {
    animation-duration: 1.5s;
  }
}
.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.bg-primary {
  background-color: #1d82f5 !important;
}

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #0969d6 !important;
}

.bg-secondary {
  background-color: #6c757d !important;
}

a.bg-secondary:hover, a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #545b62 !important;
}

.bg-success {
  background-color: #28a745 !important;
}

a.bg-success:hover, a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #1e7e34 !important;
}

.bg-info {
  background-color: #17a2b8 !important;
}

a.bg-info:hover, a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #117a8b !important;
}

.bg-warning {
  background-color: #FCBD01 !important;
}

a.bg-warning:hover, a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #c99701 !important;
}

.bg-danger {
  background-color: #D30000 !important;
}

a.bg-danger:hover, a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #a00000 !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

a.bg-light:hover, a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #dae0e5 !important;
}

.bg-dark {
  background-color: #343a40 !important;
}

a.bg-dark:hover, a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #1d2124 !important;
}

.bg-white {
  background-color: #fff !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.border {
  border: 1px solid #dee2e6 !important;
}

.border-top {
  border-top: 1px solid #dee2e6 !important;
}

.border-right {
  border-right: 1px solid #dee2e6 !important;
}

.border-bottom {
  border-bottom: 1px solid #dee2e6 !important;
}

.border-left {
  border-left: 1px solid #dee2e6 !important;
}

.border-0 {
  border: 0 !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-right-0 {
  border-right: 0 !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-left-0 {
  border-left: 0 !important;
}

.border-primary {
  border-color: #1d82f5 !important;
}

.border-secondary {
  border-color: #6c757d !important;
}

.border-success {
  border-color: #28a745 !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

.border-warning {
  border-color: #FCBD01 !important;
}

.border-danger {
  border-color: #D30000 !important;
}

.border-light {
  border-color: #f8f9fa !important;
}

.border-dark {
  border-color: #343a40 !important;
}

.border-white {
  border-color: #fff !important;
}

.rounded-sm {
  border-radius: 0.2rem !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.rounded-right {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-left {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-lg {
  border-radius: 0.3rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
}
@media print {
  .d-print-none {
    display: none !important;
  }
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
}
.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}
.embed-responsive::before {
  display: block;
  content: "";
}
.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.embed-responsive-21by9::before {
  padding-top: 42.85714286%;
}

.embed-responsive-16by9::before {
  padding-top: 56.25%;
}

.embed-responsive-4by3::before {
  padding-top: 75%;
}

.embed-responsive-1by1::before {
  padding-top: 100%;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}
.float-left {
  float: left !important;
}

.float-right {
  float: right !important;
}

.float-none {
  float: none !important;
}

@media (min-width: 576px) {
  .float-sm-left {
    float: left !important;
  }
  .float-sm-right {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
}
@media (min-width: 768px) {
  .float-md-left {
    float: left !important;
  }
  .float-md-right {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
}
@media (min-width: 992px) {
  .float-lg-left {
    float: left !important;
  }
  .float-lg-right {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important;
  }
  .float-xl-right {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
}
.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

@supports (position: sticky) {
  .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.mh-100 {
  max-height: 100% !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.vw-100 {
  width: 100vw !important;
}

.vh-100 {
  height: 100vh !important;
}

.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.mt-3,
.my-3 {
  margin-top: 1rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.mt-5,
.my-5 {
  margin-top: 3rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pt-3,
.py-3 {
  padding-top: 1rem !important;
}

.pr-3,
.px-3 {
  padding-right: 1rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}

.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.pt-5,
.py-5 {
  padding-top: 3rem !important;
}

.pr-5,
.px-5 {
  padding-right: 3rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}

.pl-5,
.px-5 {
  padding-left: 3rem !important;
}

.m-n1 {
  margin: -0.25rem !important;
}

.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important;
}

.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important;
}

.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important;
}

.ml-n1,
.mx-n1 {
  margin-left: -0.25rem !important;
}

.m-n2 {
  margin: -0.5rem !important;
}

.mt-n2,
.my-n2 {
  margin-top: -0.5rem !important;
}

.mr-n2,
.mx-n2 {
  margin-right: -0.5rem !important;
}

.mb-n2,
.my-n2 {
  margin-bottom: -0.5rem !important;
}

.ml-n2,
.mx-n2 {
  margin-left: -0.5rem !important;
}

.m-n3 {
  margin: -1rem !important;
}

.mt-n3,
.my-n3 {
  margin-top: -1rem !important;
}

.mr-n3,
.mx-n3 {
  margin-right: -1rem !important;
}

.mb-n3,
.my-n3 {
  margin-bottom: -1rem !important;
}

.ml-n3,
.mx-n3 {
  margin-left: -1rem !important;
}

.m-n4 {
  margin: -1.5rem !important;
}

.mt-n4,
.my-n4 {
  margin-top: -1.5rem !important;
}

.mr-n4,
.mx-n4 {
  margin-right: -1.5rem !important;
}

.mb-n4,
.my-n4 {
  margin-bottom: -1.5rem !important;
}

.ml-n4,
.mx-n4 {
  margin-left: -1.5rem !important;
}

.m-n5 {
  margin: -3rem !important;
}

.mt-n5,
.my-n5 {
  margin-top: -3rem !important;
}

.mr-n5,
.mx-n5 {
  margin-right: -3rem !important;
}

.mb-n5,
.my-n5 {
  margin-bottom: -3rem !important;
}

.ml-n5,
.mx-n5 {
  margin-left: -3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important;
  }
  .mt-sm-0,
.my-sm-0 {
    margin-top: 0 !important;
  }
  .mr-sm-0,
.mx-sm-0 {
    margin-right: 0 !important;
  }
  .mb-sm-0,
.my-sm-0 {
    margin-bottom: 0 !important;
  }
  .ml-sm-0,
.mx-sm-0 {
    margin-left: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .mt-sm-1,
.my-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mr-sm-1,
.mx-sm-1 {
    margin-right: 0.25rem !important;
  }
  .mb-sm-1,
.my-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-sm-1,
.mx-sm-1 {
    margin-left: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .mt-sm-2,
.my-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mr-sm-2,
.mx-sm-2 {
    margin-right: 0.5rem !important;
  }
  .mb-sm-2,
.my-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-sm-2,
.mx-sm-2 {
    margin-left: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .mt-sm-3,
.my-sm-3 {
    margin-top: 1rem !important;
  }
  .mr-sm-3,
.mx-sm-3 {
    margin-right: 1rem !important;
  }
  .mb-sm-3,
.my-sm-3 {
    margin-bottom: 1rem !important;
  }
  .ml-sm-3,
.mx-sm-3 {
    margin-left: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .mt-sm-4,
.my-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mr-sm-4,
.mx-sm-4 {
    margin-right: 1.5rem !important;
  }
  .mb-sm-4,
.my-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-sm-4,
.mx-sm-4 {
    margin-left: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .mt-sm-5,
.my-sm-5 {
    margin-top: 3rem !important;
  }
  .mr-sm-5,
.mx-sm-5 {
    margin-right: 3rem !important;
  }
  .mb-sm-5,
.my-sm-5 {
    margin-bottom: 3rem !important;
  }
  .ml-sm-5,
.mx-sm-5 {
    margin-left: 3rem !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .pt-sm-0,
.py-sm-0 {
    padding-top: 0 !important;
  }
  .pr-sm-0,
.px-sm-0 {
    padding-right: 0 !important;
  }
  .pb-sm-0,
.py-sm-0 {
    padding-bottom: 0 !important;
  }
  .pl-sm-0,
.px-sm-0 {
    padding-left: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .pt-sm-1,
.py-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pr-sm-1,
.px-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pb-sm-1,
.py-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-sm-1,
.px-sm-1 {
    padding-left: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .pt-sm-2,
.py-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pr-sm-2,
.px-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pb-sm-2,
.py-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-sm-2,
.px-sm-2 {
    padding-left: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .pt-sm-3,
.py-sm-3 {
    padding-top: 1rem !important;
  }
  .pr-sm-3,
.px-sm-3 {
    padding-right: 1rem !important;
  }
  .pb-sm-3,
.py-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pl-sm-3,
.px-sm-3 {
    padding-left: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .pt-sm-4,
.py-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pr-sm-4,
.px-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pb-sm-4,
.py-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-sm-4,
.px-sm-4 {
    padding-left: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .pt-sm-5,
.py-sm-5 {
    padding-top: 3rem !important;
  }
  .pr-sm-5,
.px-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-5,
.py-sm-5 {
    padding-bottom: 3rem !important;
  }
  .pl-sm-5,
.px-sm-5 {
    padding-left: 3rem !important;
  }
  .m-sm-n1 {
    margin: -0.25rem !important;
  }
  .mt-sm-n1,
.my-sm-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-sm-n1,
.mx-sm-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-sm-n1,
.my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-sm-n1,
.mx-sm-n1 {
    margin-left: -0.25rem !important;
  }
  .m-sm-n2 {
    margin: -0.5rem !important;
  }
  .mt-sm-n2,
.my-sm-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-sm-n2,
.mx-sm-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-sm-n2,
.my-sm-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-sm-n2,
.mx-sm-n2 {
    margin-left: -0.5rem !important;
  }
  .m-sm-n3 {
    margin: -1rem !important;
  }
  .mt-sm-n3,
.my-sm-n3 {
    margin-top: -1rem !important;
  }
  .mr-sm-n3,
.mx-sm-n3 {
    margin-right: -1rem !important;
  }
  .mb-sm-n3,
.my-sm-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-sm-n3,
.mx-sm-n3 {
    margin-left: -1rem !important;
  }
  .m-sm-n4 {
    margin: -1.5rem !important;
  }
  .mt-sm-n4,
.my-sm-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-sm-n4,
.mx-sm-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-sm-n4,
.my-sm-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-sm-n4,
.mx-sm-n4 {
    margin-left: -1.5rem !important;
  }
  .m-sm-n5 {
    margin: -3rem !important;
  }
  .mt-sm-n5,
.my-sm-n5 {
    margin-top: -3rem !important;
  }
  .mr-sm-n5,
.mx-sm-n5 {
    margin-right: -3rem !important;
  }
  .mb-sm-n5,
.my-sm-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-sm-n5,
.mx-sm-n5 {
    margin-left: -3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mt-sm-auto,
.my-sm-auto {
    margin-top: auto !important;
  }
  .mr-sm-auto,
.mx-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-auto,
.my-sm-auto {
    margin-bottom: auto !important;
  }
  .ml-sm-auto,
.mx-sm-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important;
  }
  .mt-md-0,
.my-md-0 {
    margin-top: 0 !important;
  }
  .mr-md-0,
.mx-md-0 {
    margin-right: 0 !important;
  }
  .mb-md-0,
.my-md-0 {
    margin-bottom: 0 !important;
  }
  .ml-md-0,
.mx-md-0 {
    margin-left: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .mt-md-1,
.my-md-1 {
    margin-top: 0.25rem !important;
  }
  .mr-md-1,
.mx-md-1 {
    margin-right: 0.25rem !important;
  }
  .mb-md-1,
.my-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-md-1,
.mx-md-1 {
    margin-left: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .mt-md-2,
.my-md-2 {
    margin-top: 0.5rem !important;
  }
  .mr-md-2,
.mx-md-2 {
    margin-right: 0.5rem !important;
  }
  .mb-md-2,
.my-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-md-2,
.mx-md-2 {
    margin-left: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .mt-md-3,
.my-md-3 {
    margin-top: 1rem !important;
  }
  .mr-md-3,
.mx-md-3 {
    margin-right: 1rem !important;
  }
  .mb-md-3,
.my-md-3 {
    margin-bottom: 1rem !important;
  }
  .ml-md-3,
.mx-md-3 {
    margin-left: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .mt-md-4,
.my-md-4 {
    margin-top: 1.5rem !important;
  }
  .mr-md-4,
.mx-md-4 {
    margin-right: 1.5rem !important;
  }
  .mb-md-4,
.my-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-md-4,
.mx-md-4 {
    margin-left: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .mt-md-5,
.my-md-5 {
    margin-top: 3rem !important;
  }
  .mr-md-5,
.mx-md-5 {
    margin-right: 3rem !important;
  }
  .mb-md-5,
.my-md-5 {
    margin-bottom: 3rem !important;
  }
  .ml-md-5,
.mx-md-5 {
    margin-left: 3rem !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .pt-md-0,
.py-md-0 {
    padding-top: 0 !important;
  }
  .pr-md-0,
.px-md-0 {
    padding-right: 0 !important;
  }
  .pb-md-0,
.py-md-0 {
    padding-bottom: 0 !important;
  }
  .pl-md-0,
.px-md-0 {
    padding-left: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .pt-md-1,
.py-md-1 {
    padding-top: 0.25rem !important;
  }
  .pr-md-1,
.px-md-1 {
    padding-right: 0.25rem !important;
  }
  .pb-md-1,
.py-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-md-1,
.px-md-1 {
    padding-left: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .pt-md-2,
.py-md-2 {
    padding-top: 0.5rem !important;
  }
  .pr-md-2,
.px-md-2 {
    padding-right: 0.5rem !important;
  }
  .pb-md-2,
.py-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-md-2,
.px-md-2 {
    padding-left: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .pt-md-3,
.py-md-3 {
    padding-top: 1rem !important;
  }
  .pr-md-3,
.px-md-3 {
    padding-right: 1rem !important;
  }
  .pb-md-3,
.py-md-3 {
    padding-bottom: 1rem !important;
  }
  .pl-md-3,
.px-md-3 {
    padding-left: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .pt-md-4,
.py-md-4 {
    padding-top: 1.5rem !important;
  }
  .pr-md-4,
.px-md-4 {
    padding-right: 1.5rem !important;
  }
  .pb-md-4,
.py-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-md-4,
.px-md-4 {
    padding-left: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .pt-md-5,
.py-md-5 {
    padding-top: 3rem !important;
  }
  .pr-md-5,
.px-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-5,
.py-md-5 {
    padding-bottom: 3rem !important;
  }
  .pl-md-5,
.px-md-5 {
    padding-left: 3rem !important;
  }
  .m-md-n1 {
    margin: -0.25rem !important;
  }
  .mt-md-n1,
.my-md-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-md-n1,
.mx-md-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-md-n1,
.my-md-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-md-n1,
.mx-md-n1 {
    margin-left: -0.25rem !important;
  }
  .m-md-n2 {
    margin: -0.5rem !important;
  }
  .mt-md-n2,
.my-md-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-md-n2,
.mx-md-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-md-n2,
.my-md-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-md-n2,
.mx-md-n2 {
    margin-left: -0.5rem !important;
  }
  .m-md-n3 {
    margin: -1rem !important;
  }
  .mt-md-n3,
.my-md-n3 {
    margin-top: -1rem !important;
  }
  .mr-md-n3,
.mx-md-n3 {
    margin-right: -1rem !important;
  }
  .mb-md-n3,
.my-md-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-md-n3,
.mx-md-n3 {
    margin-left: -1rem !important;
  }
  .m-md-n4 {
    margin: -1.5rem !important;
  }
  .mt-md-n4,
.my-md-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-md-n4,
.mx-md-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-md-n4,
.my-md-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-md-n4,
.mx-md-n4 {
    margin-left: -1.5rem !important;
  }
  .m-md-n5 {
    margin: -3rem !important;
  }
  .mt-md-n5,
.my-md-n5 {
    margin-top: -3rem !important;
  }
  .mr-md-n5,
.mx-md-n5 {
    margin-right: -3rem !important;
  }
  .mb-md-n5,
.my-md-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-md-n5,
.mx-md-n5 {
    margin-left: -3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mt-md-auto,
.my-md-auto {
    margin-top: auto !important;
  }
  .mr-md-auto,
.mx-md-auto {
    margin-right: auto !important;
  }
  .mb-md-auto,
.my-md-auto {
    margin-bottom: auto !important;
  }
  .ml-md-auto,
.mx-md-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important;
  }
  .mt-lg-0,
.my-lg-0 {
    margin-top: 0 !important;
  }
  .mr-lg-0,
.mx-lg-0 {
    margin-right: 0 !important;
  }
  .mb-lg-0,
.my-lg-0 {
    margin-bottom: 0 !important;
  }
  .ml-lg-0,
.mx-lg-0 {
    margin-left: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .mt-lg-1,
.my-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mr-lg-1,
.mx-lg-1 {
    margin-right: 0.25rem !important;
  }
  .mb-lg-1,
.my-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-lg-1,
.mx-lg-1 {
    margin-left: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .mt-lg-2,
.my-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mr-lg-2,
.mx-lg-2 {
    margin-right: 0.5rem !important;
  }
  .mb-lg-2,
.my-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-lg-2,
.mx-lg-2 {
    margin-left: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .mt-lg-3,
.my-lg-3 {
    margin-top: 1rem !important;
  }
  .mr-lg-3,
.mx-lg-3 {
    margin-right: 1rem !important;
  }
  .mb-lg-3,
.my-lg-3 {
    margin-bottom: 1rem !important;
  }
  .ml-lg-3,
.mx-lg-3 {
    margin-left: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .mt-lg-4,
.my-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mr-lg-4,
.mx-lg-4 {
    margin-right: 1.5rem !important;
  }
  .mb-lg-4,
.my-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-lg-4,
.mx-lg-4 {
    margin-left: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .mt-lg-5,
.my-lg-5 {
    margin-top: 3rem !important;
  }
  .mr-lg-5,
.mx-lg-5 {
    margin-right: 3rem !important;
  }
  .mb-lg-5,
.my-lg-5 {
    margin-bottom: 3rem !important;
  }
  .ml-lg-5,
.mx-lg-5 {
    margin-left: 3rem !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .pt-lg-0,
.py-lg-0 {
    padding-top: 0 !important;
  }
  .pr-lg-0,
.px-lg-0 {
    padding-right: 0 !important;
  }
  .pb-lg-0,
.py-lg-0 {
    padding-bottom: 0 !important;
  }
  .pl-lg-0,
.px-lg-0 {
    padding-left: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .pt-lg-1,
.py-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pr-lg-1,
.px-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pb-lg-1,
.py-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-lg-1,
.px-lg-1 {
    padding-left: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .pt-lg-2,
.py-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pr-lg-2,
.px-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pb-lg-2,
.py-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-lg-2,
.px-lg-2 {
    padding-left: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .pt-lg-3,
.py-lg-3 {
    padding-top: 1rem !important;
  }
  .pr-lg-3,
.px-lg-3 {
    padding-right: 1rem !important;
  }
  .pb-lg-3,
.py-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pl-lg-3,
.px-lg-3 {
    padding-left: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .pt-lg-4,
.py-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pr-lg-4,
.px-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pb-lg-4,
.py-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-lg-4,
.px-lg-4 {
    padding-left: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .pt-lg-5,
.py-lg-5 {
    padding-top: 3rem !important;
  }
  .pr-lg-5,
.px-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-5,
.py-lg-5 {
    padding-bottom: 3rem !important;
  }
  .pl-lg-5,
.px-lg-5 {
    padding-left: 3rem !important;
  }
  .m-lg-n1 {
    margin: -0.25rem !important;
  }
  .mt-lg-n1,
.my-lg-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-lg-n1,
.mx-lg-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-lg-n1,
.my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-lg-n1,
.mx-lg-n1 {
    margin-left: -0.25rem !important;
  }
  .m-lg-n2 {
    margin: -0.5rem !important;
  }
  .mt-lg-n2,
.my-lg-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-lg-n2,
.mx-lg-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-lg-n2,
.my-lg-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-lg-n2,
.mx-lg-n2 {
    margin-left: -0.5rem !important;
  }
  .m-lg-n3 {
    margin: -1rem !important;
  }
  .mt-lg-n3,
.my-lg-n3 {
    margin-top: -1rem !important;
  }
  .mr-lg-n3,
.mx-lg-n3 {
    margin-right: -1rem !important;
  }
  .mb-lg-n3,
.my-lg-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-lg-n3,
.mx-lg-n3 {
    margin-left: -1rem !important;
  }
  .m-lg-n4 {
    margin: -1.5rem !important;
  }
  .mt-lg-n4,
.my-lg-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-lg-n4,
.mx-lg-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-lg-n4,
.my-lg-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-lg-n4,
.mx-lg-n4 {
    margin-left: -1.5rem !important;
  }
  .m-lg-n5 {
    margin: -3rem !important;
  }
  .mt-lg-n5,
.my-lg-n5 {
    margin-top: -3rem !important;
  }
  .mr-lg-n5,
.mx-lg-n5 {
    margin-right: -3rem !important;
  }
  .mb-lg-n5,
.my-lg-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-lg-n5,
.mx-lg-n5 {
    margin-left: -3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mt-lg-auto,
.my-lg-auto {
    margin-top: auto !important;
  }
  .mr-lg-auto,
.mx-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-auto,
.my-lg-auto {
    margin-bottom: auto !important;
  }
  .ml-lg-auto,
.mx-lg-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important;
  }
  .mt-xl-0,
.my-xl-0 {
    margin-top: 0 !important;
  }
  .mr-xl-0,
.mx-xl-0 {
    margin-right: 0 !important;
  }
  .mb-xl-0,
.my-xl-0 {
    margin-bottom: 0 !important;
  }
  .ml-xl-0,
.mx-xl-0 {
    margin-left: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .mt-xl-1,
.my-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mr-xl-1,
.mx-xl-1 {
    margin-right: 0.25rem !important;
  }
  .mb-xl-1,
.my-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-xl-1,
.mx-xl-1 {
    margin-left: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .mt-xl-2,
.my-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mr-xl-2,
.mx-xl-2 {
    margin-right: 0.5rem !important;
  }
  .mb-xl-2,
.my-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-xl-2,
.mx-xl-2 {
    margin-left: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .mt-xl-3,
.my-xl-3 {
    margin-top: 1rem !important;
  }
  .mr-xl-3,
.mx-xl-3 {
    margin-right: 1rem !important;
  }
  .mb-xl-3,
.my-xl-3 {
    margin-bottom: 1rem !important;
  }
  .ml-xl-3,
.mx-xl-3 {
    margin-left: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .mt-xl-4,
.my-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mr-xl-4,
.mx-xl-4 {
    margin-right: 1.5rem !important;
  }
  .mb-xl-4,
.my-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-xl-4,
.mx-xl-4 {
    margin-left: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .mt-xl-5,
.my-xl-5 {
    margin-top: 3rem !important;
  }
  .mr-xl-5,
.mx-xl-5 {
    margin-right: 3rem !important;
  }
  .mb-xl-5,
.my-xl-5 {
    margin-bottom: 3rem !important;
  }
  .ml-xl-5,
.mx-xl-5 {
    margin-left: 3rem !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .pt-xl-0,
.py-xl-0 {
    padding-top: 0 !important;
  }
  .pr-xl-0,
.px-xl-0 {
    padding-right: 0 !important;
  }
  .pb-xl-0,
.py-xl-0 {
    padding-bottom: 0 !important;
  }
  .pl-xl-0,
.px-xl-0 {
    padding-left: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .pt-xl-1,
.py-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pr-xl-1,
.px-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pb-xl-1,
.py-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-xl-1,
.px-xl-1 {
    padding-left: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .pt-xl-2,
.py-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pr-xl-2,
.px-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pb-xl-2,
.py-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-xl-2,
.px-xl-2 {
    padding-left: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .pt-xl-3,
.py-xl-3 {
    padding-top: 1rem !important;
  }
  .pr-xl-3,
.px-xl-3 {
    padding-right: 1rem !important;
  }
  .pb-xl-3,
.py-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pl-xl-3,
.px-xl-3 {
    padding-left: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .pt-xl-4,
.py-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pr-xl-4,
.px-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pb-xl-4,
.py-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-xl-4,
.px-xl-4 {
    padding-left: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .pt-xl-5,
.py-xl-5 {
    padding-top: 3rem !important;
  }
  .pr-xl-5,
.px-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-5,
.py-xl-5 {
    padding-bottom: 3rem !important;
  }
  .pl-xl-5,
.px-xl-5 {
    padding-left: 3rem !important;
  }
  .m-xl-n1 {
    margin: -0.25rem !important;
  }
  .mt-xl-n1,
.my-xl-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-xl-n1,
.mx-xl-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-xl-n1,
.my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-xl-n1,
.mx-xl-n1 {
    margin-left: -0.25rem !important;
  }
  .m-xl-n2 {
    margin: -0.5rem !important;
  }
  .mt-xl-n2,
.my-xl-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-xl-n2,
.mx-xl-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-xl-n2,
.my-xl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-xl-n2,
.mx-xl-n2 {
    margin-left: -0.5rem !important;
  }
  .m-xl-n3 {
    margin: -1rem !important;
  }
  .mt-xl-n3,
.my-xl-n3 {
    margin-top: -1rem !important;
  }
  .mr-xl-n3,
.mx-xl-n3 {
    margin-right: -1rem !important;
  }
  .mb-xl-n3,
.my-xl-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-xl-n3,
.mx-xl-n3 {
    margin-left: -1rem !important;
  }
  .m-xl-n4 {
    margin: -1.5rem !important;
  }
  .mt-xl-n4,
.my-xl-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-xl-n4,
.mx-xl-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-xl-n4,
.my-xl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-xl-n4,
.mx-xl-n4 {
    margin-left: -1.5rem !important;
  }
  .m-xl-n5 {
    margin: -3rem !important;
  }
  .mt-xl-n5,
.my-xl-n5 {
    margin-top: -3rem !important;
  }
  .mr-xl-n5,
.mx-xl-n5 {
    margin-right: -3rem !important;
  }
  .mb-xl-n5,
.my-xl-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-xl-n5,
.mx-xl-n5 {
    margin-left: -3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mt-xl-auto,
.my-xl-auto {
    margin-top: auto !important;
  }
  .mr-xl-auto,
.mx-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-auto,
.my-xl-auto {
    margin-bottom: auto !important;
  }
  .ml-xl-auto,
.mx-xl-auto {
    margin-left: auto !important;
  }
}
.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: rgba(0, 0, 0, 0);
}

.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}

.text-justify {
  text-align: justify !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important;
  }
  .text-sm-right {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important;
  }
  .text-md-right {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important;
  }
  .text-lg-right {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important;
  }
  .text-xl-right {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}
.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-lighter {
  font-weight: lighter !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.font-weight-bolder {
  font-weight: bolder !important;
}

.font-italic {
  font-style: italic !important;
}

.text-white {
  color: #fff !important;
}

.text-primary {
  color: #1d82f5 !important;
}

a.text-primary:hover, a.text-primary:focus {
  color: #085dbd !important;
}

.text-secondary {
  color: #6c757d !important;
}

a.text-secondary:hover, a.text-secondary:focus {
  color: #494f54 !important;
}

.text-success {
  color: #28a745 !important;
}

a.text-success:hover, a.text-success:focus {
  color: #19692c !important;
}

.text-info {
  color: #17a2b8 !important;
}

a.text-info:hover, a.text-info:focus {
  color: #0f6674 !important;
}

.text-warning {
  color: #FCBD01 !important;
}

a.text-warning:hover, a.text-warning:focus {
  color: #b08401 !important;
}

.text-danger {
  color: #D30000 !important;
}

a.text-danger:hover, a.text-danger:focus {
  color: #870000 !important;
}

.text-light {
  color: #f8f9fa !important;
}

a.text-light:hover, a.text-light:focus {
  color: #cbd3da !important;
}

.text-dark {
  color: #343a40 !important;
}

a.text-dark:hover, a.text-dark:focus {
  color: #121416 !important;
}

.text-body {
  color: #212529 !important;
}

.text-muted {
  color: #6c757d !important;
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-break {
  word-break: break-word !important;
  word-wrap: break-word !important;
}

.text-reset {
  color: inherit !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

@media print {
  *,
*::before,
*::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  a:not(.btn) {
    text-decoration: underline;
  }
  abbr[title]::after {
    content: " (" attr(title) ")";
  }
  pre {
    white-space: pre-wrap !important;
  }
  pre,
blockquote {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }
  tr,
img {
    page-break-inside: avoid;
  }
  p,
h2,
h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
h3 {
    page-break-after: avoid;
  }
  @page {
    size: a3;
  }
  body {
    min-width: 992px !important;
  }
  .container {
    min-width: 992px !important;
  }
  .navbar {
    display: none;
  }
  .badge {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
.table th {
    background-color: #fff !important;
  }
  .table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6 !important;
  }
  .table-dark {
    color: inherit;
  }
  .table-dark th,
.table-dark td,
.table-dark thead th,
.table-dark tbody + tbody {
    border-color: #dee2e6;
  }
  .table .thead-dark th {
    color: inherit;
    border-color: #dee2e6;
  }
}
/*!
 * Cropper.js v1.5.12
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2021-06-12T08:00:11.623Z
 */
.cropper-container {
  direction: ltr;
  font-size: 0;
  line-height: 0;
  position: relative;
  touch-action: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.cropper-container img {
  display: block;
  height: 100%;
  image-orientation: 0deg;
  max-height: none !important;
  max-width: none !important;
  min-height: 0 !important;
  min-width: 0 !important;
  width: 100%;
}

.cropper-wrap-box,
.cropper-canvas,
.cropper-drag-box,
.cropper-crop-box,
.cropper-modal {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.cropper-wrap-box,
.cropper-canvas {
  overflow: hidden;
}

.cropper-drag-box {
  background-color: #fff;
  opacity: 0;
}

.cropper-modal {
  background-color: #000;
  opacity: 0.5;
}

.cropper-view-box {
  display: block;
  height: 100%;
  outline: 1px solid #39f;
  outline-color: rgba(51, 153, 255, 0.75);
  overflow: hidden;
  width: 100%;
}

.cropper-dashed {
  border: 0 dashed #eee;
  display: block;
  opacity: 0.5;
  position: absolute;
}

.cropper-dashed.dashed-h {
  border-bottom-width: 1px;
  border-top-width: 1px;
  height: 33.3333333333%;
  left: 0;
  top: 33.3333333333%;
  width: 100%;
}

.cropper-dashed.dashed-v {
  border-left-width: 1px;
  border-right-width: 1px;
  height: 100%;
  left: 33.3333333333%;
  top: 0;
  width: 33.3333333333%;
}

.cropper-center {
  display: block;
  height: 0;
  left: 50%;
  opacity: 0.75;
  position: absolute;
  top: 50%;
  width: 0;
}

.cropper-center::before,
.cropper-center::after {
  background-color: #eee;
  content: " ";
  display: block;
  position: absolute;
}

.cropper-center::before {
  height: 1px;
  left: -3px;
  top: 0;
  width: 7px;
}

.cropper-center::after {
  height: 7px;
  left: 0;
  top: -3px;
  width: 1px;
}

.cropper-face,
.cropper-line,
.cropper-point {
  display: block;
  height: 100%;
  opacity: 0.1;
  position: absolute;
  width: 100%;
}

.cropper-face {
  background-color: #fff;
  left: 0;
  top: 0;
}

.cropper-line {
  background-color: #39f;
}

.cropper-line.line-e {
  cursor: ew-resize;
  right: -3px;
  top: 0;
  width: 5px;
}

.cropper-line.line-n {
  cursor: ns-resize;
  height: 5px;
  left: 0;
  top: -3px;
}

.cropper-line.line-w {
  cursor: ew-resize;
  left: -3px;
  top: 0;
  width: 5px;
}

.cropper-line.line-s {
  bottom: -3px;
  cursor: ns-resize;
  height: 5px;
  left: 0;
}

.cropper-point {
  background-color: #39f;
  height: 5px;
  opacity: 0.75;
  width: 5px;
}

.cropper-point.point-e {
  cursor: ew-resize;
  margin-top: -3px;
  right: -3px;
  top: 50%;
}

.cropper-point.point-n {
  cursor: ns-resize;
  left: 50%;
  margin-left: -3px;
  top: -3px;
}

.cropper-point.point-w {
  cursor: ew-resize;
  left: -3px;
  margin-top: -3px;
  top: 50%;
}

.cropper-point.point-s {
  bottom: -3px;
  cursor: s-resize;
  left: 50%;
  margin-left: -3px;
}

.cropper-point.point-ne {
  cursor: nesw-resize;
  right: -3px;
  top: -3px;
}

.cropper-point.point-nw {
  cursor: nwse-resize;
  left: -3px;
  top: -3px;
}

.cropper-point.point-sw {
  bottom: -3px;
  cursor: nesw-resize;
  left: -3px;
}

.cropper-point.point-se {
  bottom: -3px;
  cursor: nwse-resize;
  height: 20px;
  opacity: 1;
  right: -3px;
  width: 20px;
}

@media (min-width: 768px) {
  .cropper-point.point-se {
    height: 15px;
    width: 15px;
  }
}
@media (min-width: 992px) {
  .cropper-point.point-se {
    height: 10px;
    width: 10px;
  }
}
@media (min-width: 1200px) {
  .cropper-point.point-se {
    height: 5px;
    opacity: 0.75;
    width: 5px;
  }
}
.cropper-point.point-se::before {
  background-color: #39f;
  bottom: -50%;
  content: " ";
  display: block;
  height: 200%;
  opacity: 0;
  position: absolute;
  right: -50%;
  width: 200%;
}

.cropper-invisible {
  opacity: 0;
}

.cropper-bg {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");
}

.cropper-hide {
  display: block;
  height: 0;
  position: absolute;
  width: 0;
}

.cropper-hidden {
  display: none !important;
}

.cropper-move {
  cursor: move;
}

.cropper-crop {
  cursor: crosshair;
}

.cropper-disabled .cropper-drag-box,
.cropper-disabled .cropper-face,
.cropper-disabled .cropper-line,
.cropper-disabled .cropper-point {
  cursor: not-allowed;
}

/**************** TABS *************************/
.tabs .nav .nav-item {
  padding: 14px 26px;
  line-height: 1.79;
  color: #28313c !important;
  margin-bottom: 0px;
  border: 0px;
}
.tabs .nav .nav-item:hover {
  border-bottom: 3px solid rgba(29, 130, 245, 0.5215686275);
}
@media (max-width: 767.98px) {
  .tabs .nav .nav-item {
    padding: 10px 15px;
  }
}
.tabs .nav .nav-link.active, .tabs .nav .nav-item.show .nav-link {
  border-bottom: 3px solid #1D82F5;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
}

.tab {
  overflow: hidden;
}

.tabcontent {
  display: none;
}

.page-heading h2 {
  line-height: 1.17;
}

.page-header-right {
  height: 63px;
  position: relative;
  padding: 0rem 1rem;
}
@media (min-width: 992px) {
  .page-header-right {
    width: 50% !important;
  }
}
.page-header-right ul {
  padding-left: 0;
  list-style: none;
}
.page-header-right ul li {
  float: left;
  position: relative;
}

.header-icon-box {
  display: flex;
  align-items: center;
  margin-right: 22px;
  justify-content: center;
}

.logout_box .header-icon-box {
  margin-right: 6px;
}

.main-header {
  height: 63px;
  position: sticky;
  top: 0px;
  z-index: 2;
  margin-left: 240px;
  transition: width 0.3s ease-in-out, margin-left 0.3s ease-in-out;
  border-bottom: 1px solid #e8eef3;
}
@media (max-width: 991.98px) {
  .main-header {
    width: 100%;
    margin-left: 0px;
  }
}
.main-header .navbar-left {
  height: 63px;
}
@media (min-width: 992px) {
  .main-header .navbar-left {
    width: 50% !important;
  }
}

.menu-collapse {
  display: block;
  margin-left: 28px;
}
.menu-collapse:before {
  content: "";
  position: absolute;
  top: -5px;
  left: -6px;
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.12);
  z-index: 0;
}

.mc-wrap .mcw-line {
  width: 18px;
  height: 2px;
  background-color: #616e80;
}

.mc-wrap .mcw-line.center {
  margin: 3px 0;
}

.notification-dropdown {
  width: 393px !important;
  min-height: 160px;
  max-height: 405px;
  right: 19px;
  top: 26px;
  overflow: auto;
}
.notification-dropdown .dropdown-item:hover {
  background-color: #fff;
}
@media (max-width: 767.98px) {
  .notification-dropdown {
    width: 300px !important;
  }
}

.invite-member span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ===== RESET CSS ====== */
body {
  font-size: 14px;
  font-family: "Helvetica Neue";
  background-color: #fff;
  font-stretch: normal;
  font-style: normal;
  font-weight: normal;
  line-height: 20.5px;
  color: #28313c;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

::-moz-selection {
  background: #222;
  color: #fff;
}

::selection {
  background: #222;
  color: #fff;
}

/*===== custom scrollbar ====== */
::-webkit-scrollbar {
  width: 10px;
  background: #e3e8ec;
}

::-webkit-scrollbar-thumb {
  border-radius: 7px;
  background-color: #aaa8a8;
  border: 3px solid #e3e8ec;
}

a {
  cursor: pointer;
}
a:hover {
  color: black !important;
  outline: 0 none;
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

ul, li {
  text-decoration: none;
  margin: 0;
  padding: 0;
  list-style: none;
}

p {
  line-height: 24px;
  margin-top: 0;
}

span {
  color: inherit;
}

i {
  line-height: 1;
  font-style: normal;
}

img {
  border-style: none;
  height: auto;
  max-width: 100%;
  vertical-align: middle;
}

iframe {
  border: none;
}

:active,
:focus {
  outline: none !important;
}

.btn.focus,
.btn:focus {
  outline: 0;
  box-shadow: none;
}

/* ===== Headings ====== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Helvetica Neue";
  margin-top: 0;
}

h1 {
  font-size: 40px;
}

h2 {
  font-size: 32px;
}

h3 {
  font-size: 25px;
}

h4 {
  font-size: 20px;
}

h5 {
  font-size: 18px;
}
h5 a {
  transition: all 0.3s ease;
}

h6 {
  font-size: 16px;
}
h6 a {
  transition: all 0.3s ease;
}

@font-face {
  font-family: "dropify";
  src: url("../fonts/dropify.eot");
  src: url("../fonts/dropify.eot#iefix") format("embedded-opentype"), url("../fonts/dropify.woff") format("woff"), url("../fonts/dropify.ttf") format("truetype"), url("../fonts/dropify.svg#dropify") format("svg");
  font-weight: normal;
  font-style: normal;
}
[class^=dropify-font-]:before, [class*=" dropify-font-"]:before, .dropify-font:before, .dropify-wrapper .dropify-message span.file-icon:before {
  font-family: "dropify";
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-left: 0.2em;
  margin-right: 0.2em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
}

.dropify-font-upload:before, .dropify-wrapper .dropify-message span.file-icon:before {
  content: "\e800";
}

.dropify-font-file:before {
  content: "\e801";
}

.dropify-wrapper {
  display: block;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  height: 200px;
  padding: 5px 10px;
  font-family: "Roboto", "Helvetica Neue", "Helvetica", "Arial";
  font-size: 14px;
  line-height: 22px;
  color: #777;
  background-color: #FFF;
  background-image: none;
  text-align: center;
  border: 2px solid #E5E5E5;
  transition: border-color 0.15s linear;
}
.dropify-wrapper:hover {
  background-size: 30px 30px;
  background-image: linear-gradient(-45deg, #F6F6F6 25%, transparent 25%, transparent 50%, #F6F6F6 50%, #F6F6F6 75%, transparent 75%, transparent);
  animation: stripes 2s linear infinite;
}
.dropify-wrapper.has-preview .dropify-clear {
  display: block;
}
.dropify-wrapper.has-error {
  border-color: #F34141;
}
.dropify-wrapper.has-error .dropify-message .dropify-error {
  display: block;
}
.dropify-wrapper.has-error:hover .dropify-errors-container  {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
}
.dropify-wrapper.disabled input {
  cursor: not-allowed;
}
.dropify-wrapper.disabled:hover {
  background-image: none;
  animation: none;
}
.dropify-wrapper.disabled .dropify-message {
  opacity: 0.5;
  text-decoration: line-through;
}
.dropify-wrapper.disabled .dropify-infos-message {
  display: none;
}
.dropify-wrapper input {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 5;
}
.dropify-wrapper .dropify-message {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.dropify-wrapper .dropify-message span.file-icon {
  font-size: 50px;
  color: #CCC;
}
.dropify-wrapper .dropify-message p {
  margin: 5px 0 0 0;
}
.dropify-wrapper .dropify-message p.dropify-error {
  color: #F34141;
  font-weight: bold;
  display: none;
}
.dropify-wrapper .dropify-clear {
  display: none;
  position: absolute;
  opacity: 0;
  z-index: 7;
  top: 10px;
  right: 10px;
  background: none;
  border: 2px solid #FFF;
  text-transform: uppercase;
  font-family: "Roboto", "Helvetica Neue", "Helvetica", "Arial";
  font-size: 11px;
  padding: 4px 8px;
  font-weight: bold;
  color: #FFF;
  transition: all 0.15s linear;
}
.dropify-wrapper .dropify-clear:hover {
  background: rgba(255, 255, 255, 0.2);
}
.dropify-wrapper .dropify-preview {
  display: none;
  position: absolute;
  z-index: 1;
  background-color: #FFF;
  padding: 5px;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  text-align: center;
}
.dropify-wrapper .dropify-preview .dropify-render img {
  top: 50%;
  transform: translate(0, -50%);
  position: relative;
  max-width: 100%;
  max-height: 100%;
  background-color: #FFF;
  transition: border-color 0.15s linear;
}
.dropify-wrapper .dropify-preview .dropify-render i {
  font-size: 70px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
  color: #777;
}
.dropify-wrapper .dropify-preview .dropify-render .dropify-extension {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin-top: 10px;
  text-transform: uppercase;
  font-weight: 900;
  letter-spacing: -0.03em;
  font-size: 13px;
  width: 42px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dropify-wrapper .dropify-preview .dropify-infos {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  background: rgba(0, 0, 0, 0.7);
  opacity: 0;
  transition: opacity 0.15s linear;
}
.dropify-wrapper .dropify-preview .dropify-infos .dropify-infos-inner {
  position: absolute;
  top: 50%;
  transform: translate(0, -40%);
  backface-visibility: hidden;
  width: 100%;
  padding: 0 20px;
  transition: all 0.2s ease;
}
.dropify-wrapper .dropify-preview .dropify-infos .dropify-infos-inner p {
  padding: 0;
  margin: 0;
  position: relative;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #FFF;
  text-align: center;
  line-height: 25px;
  font-weight: bold;
}
.dropify-wrapper .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-infos-message {
  margin-top: 15px;
  padding-top: 15px;
  font-size: 12px;
  position: relative;
  opacity: 0.5;
}
.dropify-wrapper .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-infos-message::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, 0);
  background: #FFF;
  width: 30px;
  height: 2px;
}
.dropify-wrapper:hover .dropify-clear {
  opacity: 1;
}
.dropify-wrapper:hover .dropify-preview .dropify-infos {
  opacity: 1;
}
.dropify-wrapper:hover .dropify-preview .dropify-infos .dropify-infos-inner {
  margin-top: -5px;
}
.dropify-wrapper.touch-fallback {
  height: auto !important;
}
.dropify-wrapper.touch-fallback:hover {
  background-image: none;
  animation: none;
}
.dropify-wrapper.touch-fallback .dropify-preview {
  position: relative;
  padding: 0;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-render {
  display: block;
  position: relative;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-render .dropify-font-file {
  position: relative;
  transform: translate(0, 0);
  top: 0;
  left: 0;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-render .dropify-font-file::before {
  margin-top: 30px;
  margin-bottom: 30px;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-render img {
  position: relative;
  transform: translate(0, 0);
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-infos {
  position: relative;
  opacity: 1;
  background: transparent;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-infos .dropify-infos-inner {
  position: relative;
  top: 0;
  transform: translate(0, 0);
  padding: 5px 90px 5px 0;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-infos .dropify-infos-inner p {
  padding: 0;
  margin: 0;
  position: relative;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #777;
  text-align: left;
  line-height: 25px;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-filename {
  font-weight: bold;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-infos-message {
  margin-top: 0;
  padding-top: 0;
  font-size: 11px;
  position: relative;
  opacity: 1;
}
.dropify-wrapper.touch-fallback .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-infos-message::before {
  display: none;
}
.dropify-wrapper.touch-fallback .dropify-message {
  transform: translate(0, 0);
  padding: 40px 0;
}
.dropify-wrapper.touch-fallback .dropify-clear {
  top: auto;
  bottom: 23px;
  opacity: 1;
  border-color: rgba(119, 119, 119, 0.7);
  color: #777;
}
.dropify-wrapper.touch-fallback.has-preview .dropify-message {
  display: none;
}
.dropify-wrapper.touch-fallback:hover .dropify-preview .dropify-infos .dropify-infos-inner {
  margin-top: 0;
}
.dropify-wrapper .dropify-loader {
  position: absolute;
  top: 15px;
  right: 15px;
  display: none;
  z-index: 9;
}
.dropify-wrapper .dropify-loader::after {
  display: block;
  position: relative;
  width: 20px;
  height: 20px;
  animation: rotate 0.6s linear infinite;
  border-radius: 100%;
  border-top: 1px solid #CCC;
  border-bottom: 1px solid #777;
  border-left: 1px solid #CCC;
  border-right: 1px solid #777;
  content: "";
}
.dropify-wrapper .dropify-errors-container {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  background: rgba(243, 65, 65, 0.8);
  text-align: left;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s linear 0.15s, opacity 0.15s linear;
}
.dropify-wrapper .dropify-errors-container ul {
  padding: 10px 20px;
  margin: 0;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.dropify-wrapper .dropify-errors-container ul li {
  margin-left: 20px;
  color: #FFF;
  font-weight: bold;
}
.dropify-wrapper .dropify-errors-container.visible {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
}
.dropify-wrapper ~ .dropify-errors-container ul {
  padding: 0;
  margin: 15px 0;
}
.dropify-wrapper ~ .dropify-errors-container ul li {
  margin-left: 20px;
  color: #F34141;
  font-weight: bold;
}

@keyframes stripes {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 60px 30px;
  }
}
@keyframes rotate {
  0% {
    transform: rotateZ(-360deg);
  }
  100% {
    transform: rotateZ(0deg);
  }
}
.swal2-popup.swal2-toast {
  flex-direction: column;
  align-items: stretch;
  width: auto;
  padding: 1.25em;
  overflow-y: hidden;
  background: #fff;
  box-shadow: 0 0 0.625em #d9d9d9;
}
.swal2-popup.swal2-toast .swal2-header {
  flex-direction: row;
  padding: 0;
}
.swal2-popup.swal2-toast .swal2-title {
  flex-grow: 1;
  justify-content: flex-start;
  margin: 0 0.625em;
  font-size: 1em;
}
.swal2-popup.swal2-toast .swal2-loading {
  justify-content: center;
}
.swal2-popup.swal2-toast .swal2-input {
  height: 2em;
  margin: 0.3125em auto;
  font-size: 1em;
}
.swal2-popup.swal2-toast .swal2-validation-message {
  font-size: 1em;
}
.swal2-popup.swal2-toast .swal2-footer {
  margin: 0.5em 0 0;
  padding: 0.5em 0 0;
  font-size: 0.8em;
}
.swal2-popup.swal2-toast .swal2-close {
  position: static;
  width: 0.8em;
  height: 0.8em;
  line-height: 0.8;
}
.swal2-popup.swal2-toast .swal2-content {
  justify-content: flex-start;
  margin: 0 0.625em;
  padding: 0;
  font-size: 1em;
  text-align: initial;
}
.swal2-popup.swal2-toast .swal2-html-container {
  padding: 0.625em 0 0;
}
.swal2-popup.swal2-toast .swal2-html-container:empty {
  padding: 0;
}
.swal2-popup.swal2-toast .swal2-icon {
  width: 2em;
  min-width: 2em;
  height: 2em;
  margin: 0 0.5em 0 0;
}
.swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {
  display: flex;
  align-items: center;
  font-size: 1.8em;
  font-weight: bold;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {
    font-size: 0.25em;
  }
}
.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring {
  width: 2em;
  height: 2em;
}
.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line] {
  top: 0.875em;
  width: 1.375em;
}
.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
  left: 0.3125em;
}
.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
  right: 0.3125em;
}
.swal2-popup.swal2-toast .swal2-actions {
  flex: 1;
  flex-basis: auto !important;
  align-self: stretch;
  width: auto;
  height: 2.2em;
  height: auto;
  margin: 0 0.3125em;
  margin-top: 0.3125em;
  padding: 0;
}
.swal2-popup.swal2-toast .swal2-styled {
  margin: 0.125em 0.3125em;
  padding: 0.3125em 0.625em;
  font-size: 1em;
}
.swal2-popup.swal2-toast .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(100, 150, 200, 0.5);
}
.swal2-popup.swal2-toast .swal2-success {
  border-color: #a5dc86;
}
.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line] {
  position: absolute;
  width: 1.6em;
  height: 3em;
  transform: rotate(45deg);
  border-radius: 50%;
}
.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left] {
  top: -0.8em;
  left: -0.5em;
  transform: rotate(-45deg);
  transform-origin: 2em 2em;
  border-radius: 4em 0 0 4em;
}
.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right] {
  top: -0.25em;
  left: 0.9375em;
  transform-origin: 0 1.5em;
  border-radius: 0 4em 4em 0;
}
.swal2-popup.swal2-toast .swal2-success .swal2-success-ring {
  width: 2em;
  height: 2em;
}
.swal2-popup.swal2-toast .swal2-success .swal2-success-fix {
  top: 0;
  left: 0.4375em;
  width: 0.4375em;
  height: 2.6875em;
}
.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line] {
  height: 0.3125em;
}
.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip] {
  top: 1.125em;
  left: 0.1875em;
  width: 0.75em;
}
.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long] {
  top: 0.9375em;
  right: 0.1875em;
  width: 1.375em;
}
.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip {
  animation: swal2-toast-animate-success-line-tip 0.75s;
}
.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long {
  animation: swal2-toast-animate-success-line-long 0.75s;
}
.swal2-popup.swal2-toast.swal2-show {
  animation: swal2-toast-show 0.5s;
}
.swal2-popup.swal2-toast.swal2-hide {
  animation: swal2-toast-hide 0.1s forwards;
}

.swal2-container {
  display: flex;
  position: fixed;
  z-index: 1060;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0.625em;
  overflow-x: hidden;
  transition: background-color 0.1s;
  -webkit-overflow-scrolling: touch;
}
.swal2-container.swal2-backdrop-show, .swal2-container.swal2-noanimation {
  background: rgba(0, 0, 0, 0.4);
}
.swal2-container.swal2-backdrop-hide {
  background: transparent !important;
}
.swal2-container.swal2-top {
  align-items: flex-start;
}
.swal2-container.swal2-top-start, .swal2-container.swal2-top-left {
  align-items: flex-start;
  justify-content: flex-start;
}
.swal2-container.swal2-top-end, .swal2-container.swal2-top-right {
  align-items: flex-start;
  justify-content: flex-end;
}
.swal2-container.swal2-center {
  align-items: center;
}
.swal2-container.swal2-center-start, .swal2-container.swal2-center-left {
  align-items: center;
  justify-content: flex-start;
}
.swal2-container.swal2-center-end, .swal2-container.swal2-center-right {
  align-items: center;
  justify-content: flex-end;
}
.swal2-container.swal2-bottom {
  align-items: flex-end;
}
.swal2-container.swal2-bottom-start, .swal2-container.swal2-bottom-left {
  align-items: flex-end;
  justify-content: flex-start;
}
.swal2-container.swal2-bottom-end, .swal2-container.swal2-bottom-right {
  align-items: flex-end;
  justify-content: flex-end;
}
.swal2-container.swal2-bottom > :first-child, .swal2-container.swal2-bottom-start > :first-child, .swal2-container.swal2-bottom-left > :first-child, .swal2-container.swal2-bottom-end > :first-child, .swal2-container.swal2-bottom-right > :first-child {
  margin-top: auto;
}
.swal2-container.swal2-grow-fullscreen > .swal2-modal {
  display: flex !important;
  flex: 1;
  align-self: stretch;
  justify-content: center;
}
.swal2-container.swal2-grow-row > .swal2-modal {
  display: flex !important;
  flex: 1;
  align-content: center;
  justify-content: center;
}
.swal2-container.swal2-grow-column {
  flex: 1;
  flex-direction: column;
}
.swal2-container.swal2-grow-column.swal2-top, .swal2-container.swal2-grow-column.swal2-center, .swal2-container.swal2-grow-column.swal2-bottom {
  align-items: center;
}
.swal2-container.swal2-grow-column.swal2-top-start, .swal2-container.swal2-grow-column.swal2-center-start, .swal2-container.swal2-grow-column.swal2-bottom-start, .swal2-container.swal2-grow-column.swal2-top-left, .swal2-container.swal2-grow-column.swal2-center-left, .swal2-container.swal2-grow-column.swal2-bottom-left {
  align-items: flex-start;
}
.swal2-container.swal2-grow-column.swal2-top-end, .swal2-container.swal2-grow-column.swal2-center-end, .swal2-container.swal2-grow-column.swal2-bottom-end, .swal2-container.swal2-grow-column.swal2-top-right, .swal2-container.swal2-grow-column.swal2-center-right, .swal2-container.swal2-grow-column.swal2-bottom-right {
  align-items: flex-end;
}
.swal2-container.swal2-grow-column > .swal2-modal {
  display: flex !important;
  flex: 1;
  align-content: center;
  justify-content: center;
}
.swal2-container.swal2-no-transition {
  transition: none !important;
}
.swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen) > .swal2-modal {
  margin: auto;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .swal2-container .swal2-modal {
    margin: 0 !important;
  }
}

.swal2-popup {
  display: none;
  position: relative;
  box-sizing: border-box;
  flex-direction: column;
  justify-content: center;
  width: 32em;
  max-width: 100%;
  padding: 1.25em;
  border: none;
  border-radius: 5px;
  background: #fff;
  font-family: inherit;
  font-size: 1rem;
}
.swal2-popup:focus {
  outline: none;
}
.swal2-popup.swal2-loading {
  overflow-y: hidden;
}

.swal2-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 1.8em;
}

.swal2-title {
  position: relative;
  max-width: 100%;
  margin: 0 0 0.4em;
  padding: 0;
  color: #595959;
  font-size: 1.875em;
  font-weight: 600;
  text-align: center;
  text-transform: none;
  word-wrap: break-word;
}

.swal2-actions {
  display: flex;
  z-index: 1;
  box-sizing: border-box;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 1.25em auto 0;
  padding: 0;
}
.swal2-actions:not(.swal2-loading) .swal2-styled[disabled] {
  opacity: 0.4;
}
.swal2-actions:not(.swal2-loading) .swal2-styled:hover {
  background-image: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
}
.swal2-actions:not(.swal2-loading) .swal2-styled:active {
  background-image: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2));
}

.swal2-loader {
  display: none;
  align-items: center;
  justify-content: center;
  width: 2.2em;
  height: 2.2em;
  margin: 0 1.875em;
  animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
  border-width: 0.25em;
  border-style: solid;
  border-radius: 100%;
  border-color: #2778c4 transparent #2778c4 transparent;
}

.swal2-styled {
  margin: 0.3125em;
  padding: 0.625em 1.1em;
  box-shadow: none;
  font-weight: 500;
}
.swal2-styled:not([disabled]) {
  cursor: pointer;
}
.swal2-styled.swal2-confirm {
  border: 0;
  border-radius: 0.25em;
  background: initial;
  background-color: #2778c4;
  color: #fff;
  font-size: 1em;
}
.swal2-styled.swal2-deny {
  border: 0;
  border-radius: 0.25em;
  background: initial;
  background-color: #d14529;
  color: #fff;
  font-size: 1em;
}
.swal2-styled.swal2-cancel {
  border: 0;
  border-radius: 0.25em;
  background: initial;
  background-color: #757575;
  color: #fff;
  font-size: 1em;
}
.swal2-styled:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(100, 150, 200, 0.5);
}
.swal2-styled::-moz-focus-inner {
  border: 0;
}

.swal2-footer {
  justify-content: center;
  margin: 1.25em 0 0;
  padding: 1em 0 0;
  border-top: 1px solid #eee;
  color: #545454;
  font-size: 1em;
}

.swal2-timer-progress-bar-container {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 0.25em;
  overflow: hidden;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
}

.swal2-timer-progress-bar {
  width: 100%;
  height: 0.25em;
  background: rgba(0, 0, 0, 0.2);
}

.swal2-image {
  max-width: 100%;
  margin: 1.25em auto;
}

.swal2-close {
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  align-items: center;
  justify-content: center;
  width: 1.2em;
  height: 1.2em;
  padding: 0;
  overflow: hidden;
  transition: color 0.1s ease-out;
  border: none;
  border-radius: 5px;
  background: transparent;
  color: #cccccc;
  font-family: serif;
  font-size: 2.5em;
  line-height: 1.2;
  cursor: pointer;
}
.swal2-close:hover {
  transform: none;
  background: transparent;
  color: #f27474;
}
.swal2-close:focus {
  outline: none;
  box-shadow: inset 0 0 0 3px rgba(100, 150, 200, 0.5);
}
.swal2-close::-moz-focus-inner {
  border: 0;
}

.swal2-content {
  z-index: 1;
  justify-content: center;
  margin: 0;
  padding: 0 1.6em;
  color: #545454;
  font-size: 1.125em;
  font-weight: normal;
  line-height: normal;
  text-align: center;
  word-wrap: break-word;
}

.swal2-input,
.swal2-file,
.swal2-textarea,
.swal2-select,
.swal2-radio,
.swal2-checkbox {
  margin: 1em auto;
}

.swal2-input,
.swal2-file,
.swal2-textarea {
  box-sizing: border-box;
  width: 100%;
  transition: border-color 0.3s, box-shadow 0.3s;
  border: 1px solid #d9d9d9;
  border-radius: 0.1875em;
  background: inherit;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06);
  color: inherit;
  font-size: 1.125em;
}
.swal2-input.swal2-inputerror,
.swal2-file.swal2-inputerror,
.swal2-textarea.swal2-inputerror {
  border-color: #f27474 !important;
  box-shadow: 0 0 2px #f27474 !important;
}
.swal2-input:focus,
.swal2-file:focus,
.swal2-textarea:focus {
  border: 1px solid #b4dbed;
  outline: none;
  box-shadow: 0 0 0 3px rgba(100, 150, 200, 0.5);
}
.swal2-input::-moz-placeholder, .swal2-file::-moz-placeholder, .swal2-textarea::-moz-placeholder {
  color: #cccccc;
}
.swal2-input::placeholder,
.swal2-file::placeholder,
.swal2-textarea::placeholder {
  color: #cccccc;
}

.swal2-range {
  margin: 1em auto;
  background: #fff;
}
.swal2-range input {
  width: 80%;
}
.swal2-range output {
  width: 20%;
  color: inherit;
  font-weight: 600;
  text-align: center;
}
.swal2-range input,
.swal2-range output {
  height: 2.625em;
  padding: 0;
  font-size: 1.125em;
  line-height: 2.625em;
}

.swal2-input {
  height: 2.625em;
  padding: 0 0.75em;
}
.swal2-input[type=number] {
  max-width: 10em;
}

.swal2-file {
  background: inherit;
  font-size: 1.125em;
}

.swal2-textarea {
  height: 6.75em;
  padding: 0.75em;
}

.swal2-select {
  min-width: 50%;
  max-width: 100%;
  padding: 0.375em 0.625em;
  background: inherit;
  color: inherit;
  font-size: 1.125em;
}

.swal2-radio,
.swal2-checkbox {
  align-items: center;
  justify-content: center;
  background: #fff;
  color: inherit;
}
.swal2-radio label,
.swal2-checkbox label {
  margin: 0 0.6em;
  font-size: 1.125em;
}
.swal2-radio input,
.swal2-checkbox input {
  flex-shrink: 0;
  margin: 0 0.4em;
}

.swal2-input-label {
  display: flex;
  justify-content: center;
  margin: 1em auto;
}

.swal2-validation-message {
  align-items: center;
  justify-content: center;
  margin: 0 -2.7em;
  padding: 0.625em;
  overflow: hidden;
  background: #f0f0f0;
  color: #666666;
  font-size: 1em;
  font-weight: 300;
}
.swal2-validation-message::before {
  content: "!";
  display: inline-block;
  width: 1.5em;
  min-width: 1.5em;
  height: 1.5em;
  margin: 0 0.625em;
  border-radius: 50%;
  background-color: #f27474;
  color: #fff;
  font-weight: 600;
  line-height: 1.5em;
  text-align: center;
}

.swal2-icon {
  position: relative;
  box-sizing: content-box;
  justify-content: center;
  width: 5em;
  height: 5em;
  margin: 1.25em auto 1.875em;
  border: 0.25em solid transparent;
  border-radius: 50%;
  border-color: #000;
  font-family: inherit;
  line-height: 5em;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.swal2-icon .swal2-icon-content {
  display: flex;
  align-items: center;
  font-size: 3.75em;
}
.swal2-icon.swal2-error {
  border-color: #f27474;
  color: #f27474;
}
.swal2-icon.swal2-error .swal2-x-mark {
  position: relative;
  flex-grow: 1;
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line] {
  display: block;
  position: absolute;
  top: 2.3125em;
  width: 2.9375em;
  height: 0.3125em;
  border-radius: 0.125em;
  background-color: #f27474;
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
  left: 1.0625em;
  transform: rotate(45deg);
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
  right: 1em;
  transform: rotate(-45deg);
}
.swal2-icon.swal2-error.swal2-icon-show {
  animation: swal2-animate-error-icon 0.5s;
}
.swal2-icon.swal2-error.swal2-icon-show .swal2-x-mark {
  animation: swal2-animate-error-x-mark 0.5s;
}
.swal2-icon.swal2-warning {
  border-color: #facea8;
  color: #f8bb86;
}
.swal2-icon.swal2-info {
  border-color: #9de0f6;
  color: #3fc3ee;
}
.swal2-icon.swal2-question {
  border-color: #c9dae1;
  color: #87adbd;
}
.swal2-icon.swal2-success {
  border-color: #a5dc86;
  color: #a5dc86;
}
.swal2-icon.swal2-success [class^=swal2-success-circular-line] {
  position: absolute;
  width: 3.75em;
  height: 7.5em;
  transform: rotate(45deg);
  border-radius: 50%;
}
.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left] {
  top: -0.4375em;
  left: -2.0635em;
  transform: rotate(-45deg);
  transform-origin: 3.75em 3.75em;
  border-radius: 7.5em 0 0 7.5em;
}
.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right] {
  top: -0.6875em;
  left: 1.875em;
  transform: rotate(-45deg);
  transform-origin: 0 3.75em;
  border-radius: 0 7.5em 7.5em 0;
}
.swal2-icon.swal2-success .swal2-success-ring {
  position: absolute;
  z-index: 2;
  top: -0.25em;
  left: -0.25em;
  box-sizing: content-box;
  width: 100%;
  height: 100%;
  border: 0.25em solid rgba(165, 220, 134, 0.3);
  border-radius: 50%;
}
.swal2-icon.swal2-success .swal2-success-fix {
  position: absolute;
  z-index: 1;
  top: 0.5em;
  left: 1.625em;
  width: 0.4375em;
  height: 5.625em;
  transform: rotate(-45deg);
}
.swal2-icon.swal2-success [class^=swal2-success-line] {
  display: block;
  position: absolute;
  z-index: 2;
  height: 0.3125em;
  border-radius: 0.125em;
  background-color: #a5dc86;
}
.swal2-icon.swal2-success [class^=swal2-success-line][class$=tip] {
  top: 2.875em;
  left: 0.8125em;
  width: 1.5625em;
  transform: rotate(45deg);
}
.swal2-icon.swal2-success [class^=swal2-success-line][class$=long] {
  top: 2.375em;
  right: 0.5em;
  width: 2.9375em;
  transform: rotate(-45deg);
}
.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-tip {
  animation: swal2-animate-success-line-tip 0.75s;
}
.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-long {
  animation: swal2-animate-success-line-long 0.75s;
}
.swal2-icon.swal2-success.swal2-icon-show .swal2-success-circular-line-right {
  animation: swal2-rotate-success-circular-line 4.25s ease-in;
}

.swal2-progress-steps {
  flex-wrap: wrap;
  align-items: center;
  max-width: 100%;
  margin: 0 0 1.25em;
  padding: 0;
  background: inherit;
  font-weight: 600;
}
.swal2-progress-steps li {
  display: inline-block;
  position: relative;
}
.swal2-progress-steps .swal2-progress-step {
  z-index: 20;
  flex-shrink: 0;
  width: 2em;
  height: 2em;
  border-radius: 2em;
  background: #2778c4;
  color: #fff;
  line-height: 2em;
  text-align: center;
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
  background: #2778c4;
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step {
  background: #add8e6;
  color: #fff;
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {
  background: #add8e6;
}
.swal2-progress-steps .swal2-progress-step-line {
  z-index: 10;
  flex-shrink: 0;
  width: 2.5em;
  height: 0.4em;
  margin: 0 -1px;
  background: #2778c4;
}

[class^=swal2] {
  -webkit-tap-highlight-color: transparent;
}

.swal2-show {
  animation: swal2-show 0.3s;
}

.swal2-hide {
  animation: swal2-hide 0.15s forwards;
}

.swal2-noanimation {
  transition: none;
}

.swal2-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

.swal2-rtl .swal2-close {
  right: auto;
  left: 0;
}
.swal2-rtl .swal2-timer-progress-bar {
  right: 0;
  left: auto;
}

@supports (-ms-accelerator: true) {
  .swal2-range input {
    width: 100% !important;
  }
  .swal2-range output {
    display: none;
  }
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .swal2-range input {
    width: 100% !important;
  }
  .swal2-range output {
    display: none;
  }
}
@keyframes swal2-toast-show {
  0% {
    transform: translateY(-0.625em) rotateZ(2deg);
  }
  33% {
    transform: translateY(0) rotateZ(-2deg);
  }
  66% {
    transform: translateY(0.3125em) rotateZ(2deg);
  }
  100% {
    transform: translateY(0) rotateZ(0deg);
  }
}
@keyframes swal2-toast-hide {
  100% {
    transform: rotateZ(1deg);
    opacity: 0;
  }
}
@keyframes swal2-toast-animate-success-line-tip {
  0% {
    top: 0.5625em;
    left: 0.0625em;
    width: 0;
  }
  54% {
    top: 0.125em;
    left: 0.125em;
    width: 0;
  }
  70% {
    top: 0.625em;
    left: -0.25em;
    width: 1.625em;
  }
  84% {
    top: 1.0625em;
    left: 0.75em;
    width: 0.5em;
  }
  100% {
    top: 1.125em;
    left: 0.1875em;
    width: 0.75em;
  }
}
@keyframes swal2-toast-animate-success-line-long {
  0% {
    top: 1.625em;
    right: 1.375em;
    width: 0;
  }
  65% {
    top: 1.25em;
    right: 0.9375em;
    width: 0;
  }
  84% {
    top: 0.9375em;
    right: 0;
    width: 1.125em;
  }
  100% {
    top: 0.9375em;
    right: 0.1875em;
    width: 1.375em;
  }
}
@keyframes swal2-show {
  0% {
    transform: scale(0.7);
  }
  45% {
    transform: scale(1.05);
  }
  80% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes swal2-hide {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.5);
    opacity: 0;
  }
}
@keyframes swal2-animate-success-line-tip {
  0% {
    top: 1.1875em;
    left: 0.0625em;
    width: 0;
  }
  54% {
    top: 1.0625em;
    left: 0.125em;
    width: 0;
  }
  70% {
    top: 2.1875em;
    left: -0.375em;
    width: 3.125em;
  }
  84% {
    top: 3em;
    left: 1.3125em;
    width: 1.0625em;
  }
  100% {
    top: 2.8125em;
    left: 0.8125em;
    width: 1.5625em;
  }
}
@keyframes swal2-animate-success-line-long {
  0% {
    top: 3.375em;
    right: 2.875em;
    width: 0;
  }
  65% {
    top: 3.375em;
    right: 2.875em;
    width: 0;
  }
  84% {
    top: 2.1875em;
    right: 0;
    width: 3.4375em;
  }
  100% {
    top: 2.375em;
    right: 0.5em;
    width: 2.9375em;
  }
}
@keyframes swal2-rotate-success-circular-line {
  0% {
    transform: rotate(-45deg);
  }
  5% {
    transform: rotate(-45deg);
  }
  12% {
    transform: rotate(-405deg);
  }
  100% {
    transform: rotate(-405deg);
  }
}
@keyframes swal2-animate-error-x-mark {
  0% {
    margin-top: 1.625em;
    transform: scale(0.4);
    opacity: 0;
  }
  50% {
    margin-top: 1.625em;
    transform: scale(0.4);
    opacity: 0;
  }
  80% {
    margin-top: -0.375em;
    transform: scale(1.15);
  }
  100% {
    margin-top: 0;
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes swal2-animate-error-icon {
  0% {
    transform: rotateX(100deg);
    opacity: 0;
  }
  100% {
    transform: rotateX(0deg);
    opacity: 1;
  }
}
@keyframes swal2-rotate-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
  overflow: hidden;
}
body.swal2-height-auto {
  height: auto !important;
}
body.swal2-no-backdrop .swal2-container {
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  max-width: calc(100% - 0.625em * 2);
  background-color: transparent !important;
}
body.swal2-no-backdrop .swal2-container > .swal2-modal {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
}
body.swal2-no-backdrop .swal2-container.swal2-top {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
body.swal2-no-backdrop .swal2-container.swal2-top-start, body.swal2-no-backdrop .swal2-container.swal2-top-left {
  top: 0;
  left: 0;
}
body.swal2-no-backdrop .swal2-container.swal2-top-end, body.swal2-no-backdrop .swal2-container.swal2-top-right {
  top: 0;
  right: 0;
}
body.swal2-no-backdrop .swal2-container.swal2-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
body.swal2-no-backdrop .swal2-container.swal2-center-start, body.swal2-no-backdrop .swal2-container.swal2-center-left {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
body.swal2-no-backdrop .swal2-container.swal2-center-end, body.swal2-no-backdrop .swal2-container.swal2-center-right {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
body.swal2-no-backdrop .swal2-container.swal2-bottom {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
body.swal2-no-backdrop .swal2-container.swal2-bottom-start, body.swal2-no-backdrop .swal2-container.swal2-bottom-left {
  bottom: 0;
  left: 0;
}
body.swal2-no-backdrop .swal2-container.swal2-bottom-end, body.swal2-no-backdrop .swal2-container.swal2-bottom-right {
  right: 0;
  bottom: 0;
}
@media print {
  body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
    overflow-y: scroll !important;
  }
  body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) > [aria-hidden=true] {
    display: none;
  }
  body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container {
    position: static !important;
  }
}
body.swal2-toast-shown .swal2-container {
  background-color: transparent;
}
body.swal2-toast-shown .swal2-container.swal2-top {
  top: 0;
  right: auto;
  bottom: auto;
  left: 50%;
  transform: translateX(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-top-end, body.swal2-toast-shown .swal2-container.swal2-top-right {
  top: 0;
  right: 0;
  bottom: auto;
  left: auto;
}
body.swal2-toast-shown .swal2-container.swal2-top-start, body.swal2-toast-shown .swal2-container.swal2-top-left {
  top: 0;
  right: auto;
  bottom: auto;
  left: 0;
}
body.swal2-toast-shown .swal2-container.swal2-center-start, body.swal2-toast-shown .swal2-container.swal2-center-left {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 0;
  transform: translateY(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-center {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 50%;
  transform: translate(-50%, -50%);
}
body.swal2-toast-shown .swal2-container.swal2-center-end, body.swal2-toast-shown .swal2-container.swal2-center-right {
  top: 50%;
  right: 0;
  bottom: auto;
  left: auto;
  transform: translateY(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-bottom-start, body.swal2-toast-shown .swal2-container.swal2-bottom-left {
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
}
body.swal2-toast-shown .swal2-container.swal2-bottom {
  top: auto;
  right: auto;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-bottom-end, body.swal2-toast-shown .swal2-container.swal2-bottom-right {
  top: auto;
  right: 0;
  bottom: 0;
  left: auto;
}

@keyframes bs-notify-fadeOut {
  0% {
    opacity: 0.9;
  }
  100% {
    opacity: 0;
  }
}
select.bs-select-hidden,
.bootstrap-select > select.bs-select-hidden,
select.selectpicker {
  display: none !important;
}

.bootstrap-select {
  width: 220px \0 ; /*IE9 and below*/
  vertical-align: middle;
}
.bootstrap-select > .dropdown-toggle {
  position: relative;
  width: 100%;
  text-align: right;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
}
.bootstrap-select > .dropdown-toggle:after {
  margin-top: -1px;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder, .bootstrap-select > .dropdown-toggle.bs-placeholder:hover, .bootstrap-select > .dropdown-toggle.bs-placeholder:focus, .bootstrap-select > .dropdown-toggle.bs-placeholder:active {
  color: #999;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder.btn-primary, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-primary:hover, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-primary:focus, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-primary:active, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-secondary, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-secondary:hover, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-secondary:focus, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-secondary:active, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-success, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-success:hover, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-success:focus, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-success:active, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-danger, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-danger:hover, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-danger:focus, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-danger:active, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-info, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-info:hover, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-info:focus, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-info:active, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-dark, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-dark:hover, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-dark:focus, .bootstrap-select > .dropdown-toggle.bs-placeholder.btn-dark:active {
  color: rgba(255, 255, 255, 0.5);
}
.bootstrap-select > select {
  position: absolute !important;
  bottom: 0;
  left: 50%;
  display: block !important;
  width: 0.5px !important;
  height: 100% !important;
  padding: 0 !important;
  opacity: 0 !important;
  border: none;
  z-index: 0 !important;
}
.bootstrap-select > select.mobile-device {
  top: 0;
  left: 0;
  display: block !important;
  width: 100% !important;
  z-index: 2 !important;
}
.has-error .bootstrap-select .dropdown-toggle, .error .bootstrap-select .dropdown-toggle, .bootstrap-select.is-invalid .dropdown-toggle, .was-validated .bootstrap-select select:invalid + .dropdown-toggle {
  border-color: rgb(185, 74, 72);
}
.bootstrap-select.is-valid .dropdown-toggle, .was-validated .bootstrap-select select:valid + .dropdown-toggle {
  border-color: #28a745;
}
.bootstrap-select.fit-width {
  width: auto !important;
}
.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 220px;
}
.bootstrap-select > select.mobile-device:focus + .dropdown-toggle,
.bootstrap-select .dropdown-toggle:focus {
  outline: thin dotted #333333 !important;
  outline: 5px auto -webkit-focus-ring-color !important;
  outline-offset: -2px;
}

.bootstrap-select.form-control {
  margin-bottom: 0;
  padding: 0;
  border: none;
  height: auto;
}
:not(.input-group) > .bootstrap-select.form-control:not([class*=col-]) {
  width: 100%;
}
.bootstrap-select.form-control.input-group-btn {
  float: none;
  z-index: auto;
}
.form-inline .bootstrap-select, .form-inline .bootstrap-select.form-control:not([class*=col-]) {
  width: auto;
}
.bootstrap-select:not(.input-group-btn), .bootstrap-select[class*=col-] {
  float: none;
  display: inline-block;
  margin-left: 0;
}
.bootstrap-select.dropdown-menu-right, .bootstrap-select[class*=col-].dropdown-menu-right, .row .bootstrap-select[class*=col-].dropdown-menu-right {
  float: right;
}
.form-inline .bootstrap-select, .form-horizontal .bootstrap-select, .form-group .bootstrap-select {
  margin-bottom: 0;
}
.form-group-lg .bootstrap-select.form-control, .form-group-sm .bootstrap-select.form-control {
  padding: 0;
}
.form-group-lg .bootstrap-select.form-control .dropdown-toggle, .form-group-sm .bootstrap-select.form-control .dropdown-toggle {
  height: 100%;
  font-size: inherit;
  line-height: inherit;
  border-radius: inherit;
}
.bootstrap-select.form-control-sm .dropdown-toggle, .bootstrap-select.form-control-lg .dropdown-toggle {
  font-size: inherit;
  line-height: inherit;
  border-radius: inherit;
}
.bootstrap-select.form-control-sm .dropdown-toggle {
  padding: 0.25rem 0.5rem;
}
.bootstrap-select.form-control-lg .dropdown-toggle {
  padding: 0.5rem 1rem;
}
.form-inline .bootstrap-select .form-control {
  width: 100%;
}
.bootstrap-select.disabled,
.bootstrap-select > .disabled {
  cursor: not-allowed;
}
.bootstrap-select.disabled:focus,
.bootstrap-select > .disabled:focus {
  outline: none !important;
}
.bootstrap-select.bs-container {
  position: absolute;
  top: 0;
  left: 0;
  height: 0 !important;
  padding: 0 !important;
}
.bootstrap-select.bs-container .dropdown-menu {
  z-index: 1060;
}
.bootstrap-select .dropdown-toggle .filter-option {
  position: static;
  top: 0;
  left: 0;
  float: left;
  height: 100%;
  width: 100%;
  text-align: left;
  overflow: hidden;
  flex: 0 1 auto;
}
.bs3.bootstrap-select .dropdown-toggle .filter-option {
  padding-right: inherit;
}

.input-group .bs3-has-addon.bootstrap-select .dropdown-toggle .filter-option {
  position: absolute;
  padding-top: inherit;
  padding-bottom: inherit;
  padding-left: inherit;
  float: none;
}
.input-group .bs3-has-addon.bootstrap-select .dropdown-toggle .filter-option .filter-option-inner {
  padding-right: inherit;
}

.bootstrap-select .dropdown-toggle .filter-option-inner-inner {
  overflow: hidden;
}
.bootstrap-select .dropdown-toggle .filter-expand {
  width: 0 !important;
  float: left;
  opacity: 0 !important;
  overflow: hidden;
}
.bootstrap-select .dropdown-toggle .caret {
  position: absolute;
  top: 50%;
  right: 12px;
  margin-top: -2px;
  vertical-align: middle;
}
.input-group .bootstrap-select.form-control .dropdown-toggle {
  border-radius: inherit;
}
.bootstrap-select[class*=col-] .dropdown-toggle {
  width: 100%;
}
.bootstrap-select .dropdown-menu {
  min-width: 100%;
  box-sizing: border-box;
}
.bootstrap-select .dropdown-menu > .inner:focus {
  outline: none !important;
}
.bootstrap-select .dropdown-menu.inner {
  position: static;
  float: none;
  border: 0;
  padding: 0;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}
.bootstrap-select .dropdown-menu li {
  position: relative;
}
.bootstrap-select .dropdown-menu li.active small {
  color: rgba(255, 255, 255, 0.5) !important;
}
.bootstrap-select .dropdown-menu li.disabled a {
  cursor: not-allowed;
}
.bootstrap-select .dropdown-menu li a {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.bootstrap-select .dropdown-menu li a.opt {
  position: relative;
  padding-left: 2.25em;
}
.bootstrap-select .dropdown-menu li a span.check-mark {
  display: none;
}
.bootstrap-select .dropdown-menu li a span.text {
  display: inline-block;
}
.bootstrap-select .dropdown-menu li small {
  padding-left: 0.5em;
}
.bootstrap-select .dropdown-menu .notify {
  position: absolute;
  bottom: 5px;
  width: 96%;
  margin: 0 2%;
  min-height: 26px;
  padding: 3px 5px;
  background: rgb(245, 245, 245);
  border: 1px solid rgb(227, 227, 227);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  pointer-events: none;
  opacity: 0.9;
  box-sizing: border-box;
}
.bootstrap-select .dropdown-menu .notify.fadeOut {
  animation: 300ms linear 750ms forwards bs-notify-fadeOut;
}
.bootstrap-select .no-results {
  padding: 3px;
  background: #f5f5f5;
  margin: 0 5px;
  white-space: nowrap;
}
.bootstrap-select.fit-width .dropdown-toggle .filter-option {
  position: static;
  display: inline;
  padding: 0;
}
.bootstrap-select.fit-width .dropdown-toggle .filter-option-inner,
.bootstrap-select.fit-width .dropdown-toggle .filter-option-inner-inner {
  display: inline;
}
.bootstrap-select.fit-width .dropdown-toggle .bs-caret:before {
  content: " ";
}
.bootstrap-select.fit-width .dropdown-toggle .caret {
  position: static;
  top: auto;
  margin-top: -1px;
}
.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
  position: absolute;
  display: inline-block;
  right: 15px;
  top: 5px;
}
.bootstrap-select.show-tick .dropdown-menu li a span.text {
  margin-right: 34px;
}
.bootstrap-select .bs-ok-default:after {
  content: "";
  display: block;
  width: 0.5em;
  height: 1em;
  border-style: solid;
  border-width: 0 0.26em 0.26em 0;
  transform-style: preserve-3d;
  transform: rotate(45deg);
}

.bootstrap-select.show-menu-arrow.open > .dropdown-toggle, .bootstrap-select.show-menu-arrow.show > .dropdown-toggle {
  z-index: 1061;
}
.bootstrap-select.show-menu-arrow .dropdown-toggle .filter-option:before {
  content: "";
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid rgba(204, 204, 204, 0.2);
  position: absolute;
  bottom: -4px;
  left: 9px;
  display: none;
}
.bootstrap-select.show-menu-arrow .dropdown-toggle .filter-option:after {
  content: "";
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid white;
  position: absolute;
  bottom: -4px;
  left: 10px;
  display: none;
}
.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle .filter-option:before {
  bottom: auto;
  top: -4px;
  border-top: 7px solid rgba(204, 204, 204, 0.2);
  border-bottom: 0;
}
.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle .filter-option:after {
  bottom: auto;
  top: -4px;
  border-top: 6px solid white;
  border-bottom: 0;
}
.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle .filter-option:before {
  right: 12px;
  left: auto;
}
.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle .filter-option:after {
  right: 13px;
  left: auto;
}
.bootstrap-select.show-menu-arrow.open > .dropdown-toggle .filter-option:before, .bootstrap-select.show-menu-arrow.open > .dropdown-toggle .filter-option:after, .bootstrap-select.show-menu-arrow.show > .dropdown-toggle .filter-option:before, .bootstrap-select.show-menu-arrow.show > .dropdown-toggle .filter-option:after {
  display: block;
}

.bs-searchbox,
.bs-actionsbox,
.bs-donebutton {
  padding: 4px 8px;
}

.bs-actionsbox {
  width: 100%;
  box-sizing: border-box;
}
.bs-actionsbox .btn-group button {
  width: 50%;
}

.bs-donebutton {
  float: left;
  width: 100%;
  box-sizing: border-box;
}
.bs-donebutton .btn-group button {
  width: 100%;
}

.bs-searchbox + .bs-actionsbox {
  padding: 0 8px 4px;
}
.bs-searchbox .form-control {
  margin-bottom: 0;
  width: 100%;
  float: none;
}

.flag-icon-background, .flag-icon {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
}

.flag-icon {
  position: relative;
  display: inline-block;
  width: 1.333333em;
  line-height: 1em;
}
.flag-icon:before {
  content: " ";
}
.flag-icon.flag-icon-squared {
  width: 1em;
}

.flag-icon-ad {
  background-image: url(../flags/4x3/ad.svg);
}
.flag-icon-ad.flag-icon-squared {
  background-image: url(../flags/1x1/ad.svg);
}

.flag-icon-ae {
  background-image: url(../flags/4x3/ae.svg);
}
.flag-icon-ae.flag-icon-squared {
  background-image: url(../flags/1x1/ae.svg);
}

.flag-icon-af {
  background-image: url(../flags/4x3/af.svg);
}
.flag-icon-af.flag-icon-squared {
  background-image: url(../flags/1x1/af.svg);
}

.flag-icon-ag {
  background-image: url(../flags/4x3/ag.svg);
}
.flag-icon-ag.flag-icon-squared {
  background-image: url(../flags/1x1/ag.svg);
}

.flag-icon-ai {
  background-image: url(../flags/4x3/ai.svg);
}
.flag-icon-ai.flag-icon-squared {
  background-image: url(../flags/1x1/ai.svg);
}

.flag-icon-al {
  background-image: url(../flags/4x3/al.svg);
}
.flag-icon-al.flag-icon-squared {
  background-image: url(../flags/1x1/al.svg);
}

.flag-icon-am {
  background-image: url(../flags/4x3/am.svg);
}
.flag-icon-am.flag-icon-squared {
  background-image: url(../flags/1x1/am.svg);
}

.flag-icon-ao {
  background-image: url(../flags/4x3/ao.svg);
}
.flag-icon-ao.flag-icon-squared {
  background-image: url(../flags/1x1/ao.svg);
}

.flag-icon-aq {
  background-image: url(../flags/4x3/aq.svg);
}
.flag-icon-aq.flag-icon-squared {
  background-image: url(../flags/1x1/aq.svg);
}

.flag-icon-ar {
  background-image: url(../flags/4x3/ar.svg);
}
.flag-icon-ar.flag-icon-squared {
  background-image: url(../flags/1x1/ar.svg);
}

.flag-icon-as {
  background-image: url(../flags/4x3/as.svg);
}
.flag-icon-as.flag-icon-squared {
  background-image: url(../flags/1x1/as.svg);
}

.flag-icon-at {
  background-image: url(../flags/4x3/at.svg);
}
.flag-icon-at.flag-icon-squared {
  background-image: url(../flags/1x1/at.svg);
}

.flag-icon-au {
  background-image: url(../flags/4x3/au.svg);
}
.flag-icon-au.flag-icon-squared {
  background-image: url(../flags/1x1/au.svg);
}

.flag-icon-aw {
  background-image: url(../flags/4x3/aw.svg);
}
.flag-icon-aw.flag-icon-squared {
  background-image: url(../flags/1x1/aw.svg);
}

.flag-icon-ax {
  background-image: url(../flags/4x3/ax.svg);
}
.flag-icon-ax.flag-icon-squared {
  background-image: url(../flags/1x1/ax.svg);
}

.flag-icon-az {
  background-image: url(../flags/4x3/az.svg);
}
.flag-icon-az.flag-icon-squared {
  background-image: url(../flags/1x1/az.svg);
}

.flag-icon-ba {
  background-image: url(../flags/4x3/ba.svg);
}
.flag-icon-ba.flag-icon-squared {
  background-image: url(../flags/1x1/ba.svg);
}

.flag-icon-bb {
  background-image: url(../flags/4x3/bb.svg);
}
.flag-icon-bb.flag-icon-squared {
  background-image: url(../flags/1x1/bb.svg);
}

.flag-icon-bd {
  background-image: url(../flags/4x3/bd.svg);
}
.flag-icon-bd.flag-icon-squared {
  background-image: url(../flags/1x1/bd.svg);
}

.flag-icon-be {
  background-image: url(../flags/4x3/be.svg);
}
.flag-icon-be.flag-icon-squared {
  background-image: url(../flags/1x1/be.svg);
}

.flag-icon-bf {
  background-image: url(../flags/4x3/bf.svg);
}
.flag-icon-bf.flag-icon-squared {
  background-image: url(../flags/1x1/bf.svg);
}

.flag-icon-bg {
  background-image: url(../flags/4x3/bg.svg);
}
.flag-icon-bg.flag-icon-squared {
  background-image: url(../flags/1x1/bg.svg);
}

.flag-icon-bh {
  background-image: url(../flags/4x3/bh.svg);
}
.flag-icon-bh.flag-icon-squared {
  background-image: url(../flags/1x1/bh.svg);
}

.flag-icon-bi {
  background-image: url(../flags/4x3/bi.svg);
}
.flag-icon-bi.flag-icon-squared {
  background-image: url(../flags/1x1/bi.svg);
}

.flag-icon-bj {
  background-image: url(../flags/4x3/bj.svg);
}
.flag-icon-bj.flag-icon-squared {
  background-image: url(../flags/1x1/bj.svg);
}

.flag-icon-bl {
  background-image: url(../flags/4x3/bl.svg);
}
.flag-icon-bl.flag-icon-squared {
  background-image: url(../flags/1x1/bl.svg);
}

.flag-icon-bm {
  background-image: url(../flags/4x3/bm.svg);
}
.flag-icon-bm.flag-icon-squared {
  background-image: url(../flags/1x1/bm.svg);
}

.flag-icon-bn {
  background-image: url(../flags/4x3/bn.svg);
}
.flag-icon-bn.flag-icon-squared {
  background-image: url(../flags/1x1/bn.svg);
}

.flag-icon-bo {
  background-image: url(../flags/4x3/bo.svg);
}
.flag-icon-bo.flag-icon-squared {
  background-image: url(../flags/1x1/bo.svg);
}

.flag-icon-bq {
  background-image: url(../flags/4x3/bq.svg);
}
.flag-icon-bq.flag-icon-squared {
  background-image: url(../flags/1x1/bq.svg);
}

.flag-icon-br {
  background-image: url(../flags/4x3/br.svg);
}
.flag-icon-br.flag-icon-squared {
  background-image: url(../flags/1x1/br.svg);
}

.flag-icon-bs {
  background-image: url(../flags/4x3/bs.svg);
}
.flag-icon-bs.flag-icon-squared {
  background-image: url(../flags/1x1/bs.svg);
}

.flag-icon-bt {
  background-image: url(../flags/4x3/bt.svg);
}
.flag-icon-bt.flag-icon-squared {
  background-image: url(../flags/1x1/bt.svg);
}

.flag-icon-bv {
  background-image: url(../flags/4x3/bv.svg);
}
.flag-icon-bv.flag-icon-squared {
  background-image: url(../flags/1x1/bv.svg);
}

.flag-icon-bw {
  background-image: url(../flags/4x3/bw.svg);
}
.flag-icon-bw.flag-icon-squared {
  background-image: url(../flags/1x1/bw.svg);
}

.flag-icon-by {
  background-image: url(../flags/4x3/by.svg);
}
.flag-icon-by.flag-icon-squared {
  background-image: url(../flags/1x1/by.svg);
}

.flag-icon-bz {
  background-image: url(../flags/4x3/bz.svg);
}
.flag-icon-bz.flag-icon-squared {
  background-image: url(../flags/1x1/bz.svg);
}

.flag-icon-ca {
  background-image: url(../flags/4x3/ca.svg);
}
.flag-icon-ca.flag-icon-squared {
  background-image: url(../flags/1x1/ca.svg);
}

.flag-icon-cc {
  background-image: url(../flags/4x3/cc.svg);
}
.flag-icon-cc.flag-icon-squared {
  background-image: url(../flags/1x1/cc.svg);
}

.flag-icon-cd {
  background-image: url(../flags/4x3/cd.svg);
}
.flag-icon-cd.flag-icon-squared {
  background-image: url(../flags/1x1/cd.svg);
}

.flag-icon-cf {
  background-image: url(../flags/4x3/cf.svg);
}
.flag-icon-cf.flag-icon-squared {
  background-image: url(../flags/1x1/cf.svg);
}

.flag-icon-cg {
  background-image: url(../flags/4x3/cg.svg);
}
.flag-icon-cg.flag-icon-squared {
  background-image: url(../flags/1x1/cg.svg);
}

.flag-icon-ch {
  background-image: url(../flags/4x3/ch.svg);
}
.flag-icon-ch.flag-icon-squared {
  background-image: url(../flags/1x1/ch.svg);
}

.flag-icon-ci {
  background-image: url(../flags/4x3/ci.svg);
}
.flag-icon-ci.flag-icon-squared {
  background-image: url(../flags/1x1/ci.svg);
}

.flag-icon-ck {
  background-image: url(../flags/4x3/ck.svg);
}
.flag-icon-ck.flag-icon-squared {
  background-image: url(../flags/1x1/ck.svg);
}

.flag-icon-cl {
  background-image: url(../flags/4x3/cl.svg);
}
.flag-icon-cl.flag-icon-squared {
  background-image: url(../flags/1x1/cl.svg);
}

.flag-icon-cm {
  background-image: url(../flags/4x3/cm.svg);
}
.flag-icon-cm.flag-icon-squared {
  background-image: url(../flags/1x1/cm.svg);
}

.flag-icon-cn {
  background-image: url(../flags/4x3/cn.svg);
}
.flag-icon-cn.flag-icon-squared {
  background-image: url(../flags/1x1/cn.svg);
}

.flag-icon-co {
  background-image: url(../flags/4x3/co.svg);
}
.flag-icon-co.flag-icon-squared {
  background-image: url(../flags/1x1/co.svg);
}

.flag-icon-cr {
  background-image: url(../flags/4x3/cr.svg);
}
.flag-icon-cr.flag-icon-squared {
  background-image: url(../flags/1x1/cr.svg);
}

.flag-icon-cu {
  background-image: url(../flags/4x3/cu.svg);
}
.flag-icon-cu.flag-icon-squared {
  background-image: url(../flags/1x1/cu.svg);
}

.flag-icon-cv {
  background-image: url(../flags/4x3/cv.svg);
}
.flag-icon-cv.flag-icon-squared {
  background-image: url(../flags/1x1/cv.svg);
}

.flag-icon-cw {
  background-image: url(../flags/4x3/cw.svg);
}
.flag-icon-cw.flag-icon-squared {
  background-image: url(../flags/1x1/cw.svg);
}

.flag-icon-cx {
  background-image: url(../flags/4x3/cx.svg);
}
.flag-icon-cx.flag-icon-squared {
  background-image: url(../flags/1x1/cx.svg);
}

.flag-icon-cy {
  background-image: url(../flags/4x3/cy.svg);
}
.flag-icon-cy.flag-icon-squared {
  background-image: url(../flags/1x1/cy.svg);
}

.flag-icon-cz {
  background-image: url(../flags/4x3/cz.svg);
}
.flag-icon-cz.flag-icon-squared {
  background-image: url(../flags/1x1/cz.svg);
}

.flag-icon-de {
  background-image: url(../flags/4x3/de.svg);
}
.flag-icon-de.flag-icon-squared {
  background-image: url(../flags/1x1/de.svg);
}

.flag-icon-dj {
  background-image: url(../flags/4x3/dj.svg);
}
.flag-icon-dj.flag-icon-squared {
  background-image: url(../flags/1x1/dj.svg);
}

.flag-icon-dk {
  background-image: url(../flags/4x3/dk.svg);
}
.flag-icon-dk.flag-icon-squared {
  background-image: url(../flags/1x1/dk.svg);
}

.flag-icon-dm {
  background-image: url(../flags/4x3/dm.svg);
}
.flag-icon-dm.flag-icon-squared {
  background-image: url(../flags/1x1/dm.svg);
}

.flag-icon-do {
  background-image: url(../flags/4x3/do.svg);
}
.flag-icon-do.flag-icon-squared {
  background-image: url(../flags/1x1/do.svg);
}

.flag-icon-dz {
  background-image: url(../flags/4x3/dz.svg);
}
.flag-icon-dz.flag-icon-squared {
  background-image: url(../flags/1x1/dz.svg);
}

.flag-icon-ec {
  background-image: url(../flags/4x3/ec.svg);
}
.flag-icon-ec.flag-icon-squared {
  background-image: url(../flags/1x1/ec.svg);
}

.flag-icon-ee {
  background-image: url(../flags/4x3/ee.svg);
}
.flag-icon-ee.flag-icon-squared {
  background-image: url(../flags/1x1/ee.svg);
}

.flag-icon-eg {
  background-image: url(../flags/4x3/eg.svg);
}
.flag-icon-eg.flag-icon-squared {
  background-image: url(../flags/1x1/eg.svg);
}

.flag-icon-eh {
  background-image: url(../flags/4x3/eh.svg);
}
.flag-icon-eh.flag-icon-squared {
  background-image: url(../flags/1x1/eh.svg);
}

.flag-icon-er {
  background-image: url(../flags/4x3/er.svg);
}
.flag-icon-er.flag-icon-squared {
  background-image: url(../flags/1x1/er.svg);
}

.flag-icon-es {
  background-image: url(../flags/4x3/es.svg);
}
.flag-icon-es.flag-icon-squared {
  background-image: url(../flags/1x1/es.svg);
}

.flag-icon-et {
  background-image: url(../flags/4x3/et.svg);
}
.flag-icon-et.flag-icon-squared {
  background-image: url(../flags/1x1/et.svg);
}

.flag-icon-fi {
  background-image: url(../flags/4x3/fi.svg);
}
.flag-icon-fi.flag-icon-squared {
  background-image: url(../flags/1x1/fi.svg);
}

.flag-icon-fj {
  background-image: url(../flags/4x3/fj.svg);
}
.flag-icon-fj.flag-icon-squared {
  background-image: url(../flags/1x1/fj.svg);
}

.flag-icon-fk {
  background-image: url(../flags/4x3/fk.svg);
}
.flag-icon-fk.flag-icon-squared {
  background-image: url(../flags/1x1/fk.svg);
}

.flag-icon-fm {
  background-image: url(../flags/4x3/fm.svg);
}
.flag-icon-fm.flag-icon-squared {
  background-image: url(../flags/1x1/fm.svg);
}

.flag-icon-fo {
  background-image: url(../flags/4x3/fo.svg);
}
.flag-icon-fo.flag-icon-squared {
  background-image: url(../flags/1x1/fo.svg);
}

.flag-icon-fr {
  background-image: url(../flags/4x3/fr.svg);
}
.flag-icon-fr.flag-icon-squared {
  background-image: url(../flags/1x1/fr.svg);
}

.flag-icon-ga {
  background-image: url(../flags/4x3/ga.svg);
}
.flag-icon-ga.flag-icon-squared {
  background-image: url(../flags/1x1/ga.svg);
}

.flag-icon-gb {
  background-image: url(../flags/4x3/gb.svg);
}
.flag-icon-gb.flag-icon-squared {
  background-image: url(../flags/1x1/gb.svg);
}

.flag-icon-gd {
  background-image: url(../flags/4x3/gd.svg);
}
.flag-icon-gd.flag-icon-squared {
  background-image: url(../flags/1x1/gd.svg);
}

.flag-icon-ge {
  background-image: url(../flags/4x3/ge.svg);
}
.flag-icon-ge.flag-icon-squared {
  background-image: url(../flags/1x1/ge.svg);
}

.flag-icon-gf {
  background-image: url(../flags/4x3/gf.svg);
}
.flag-icon-gf.flag-icon-squared {
  background-image: url(../flags/1x1/gf.svg);
}

.flag-icon-gg {
  background-image: url(../flags/4x3/gg.svg);
}
.flag-icon-gg.flag-icon-squared {
  background-image: url(../flags/1x1/gg.svg);
}

.flag-icon-gh {
  background-image: url(../flags/4x3/gh.svg);
}
.flag-icon-gh.flag-icon-squared {
  background-image: url(../flags/1x1/gh.svg);
}

.flag-icon-gi {
  background-image: url(../flags/4x3/gi.svg);
}
.flag-icon-gi.flag-icon-squared {
  background-image: url(../flags/1x1/gi.svg);
}

.flag-icon-gl {
  background-image: url(../flags/4x3/gl.svg);
}
.flag-icon-gl.flag-icon-squared {
  background-image: url(../flags/1x1/gl.svg);
}

.flag-icon-gm {
  background-image: url(../flags/4x3/gm.svg);
}
.flag-icon-gm.flag-icon-squared {
  background-image: url(../flags/1x1/gm.svg);
}

.flag-icon-gn {
  background-image: url(../flags/4x3/gn.svg);
}
.flag-icon-gn.flag-icon-squared {
  background-image: url(../flags/1x1/gn.svg);
}

.flag-icon-gp {
  background-image: url(../flags/4x3/gp.svg);
}
.flag-icon-gp.flag-icon-squared {
  background-image: url(../flags/1x1/gp.svg);
}

.flag-icon-gq {
  background-image: url(../flags/4x3/gq.svg);
}
.flag-icon-gq.flag-icon-squared {
  background-image: url(../flags/1x1/gq.svg);
}

.flag-icon-gr {
  background-image: url(../flags/4x3/gr.svg);
}
.flag-icon-gr.flag-icon-squared {
  background-image: url(../flags/1x1/gr.svg);
}

.flag-icon-gs {
  background-image: url(../flags/4x3/gs.svg);
}
.flag-icon-gs.flag-icon-squared {
  background-image: url(../flags/1x1/gs.svg);
}

.flag-icon-gt {
  background-image: url(../flags/4x3/gt.svg);
}
.flag-icon-gt.flag-icon-squared {
  background-image: url(../flags/1x1/gt.svg);
}

.flag-icon-gu {
  background-image: url(../flags/4x3/gu.svg);
}
.flag-icon-gu.flag-icon-squared {
  background-image: url(../flags/1x1/gu.svg);
}

.flag-icon-gw {
  background-image: url(../flags/4x3/gw.svg);
}
.flag-icon-gw.flag-icon-squared {
  background-image: url(../flags/1x1/gw.svg);
}

.flag-icon-gy {
  background-image: url(../flags/4x3/gy.svg);
}
.flag-icon-gy.flag-icon-squared {
  background-image: url(../flags/1x1/gy.svg);
}

.flag-icon-hk {
  background-image: url(../flags/4x3/hk.svg);
}
.flag-icon-hk.flag-icon-squared {
  background-image: url(../flags/1x1/hk.svg);
}

.flag-icon-hm {
  background-image: url(../flags/4x3/hm.svg);
}
.flag-icon-hm.flag-icon-squared {
  background-image: url(../flags/1x1/hm.svg);
}

.flag-icon-hn {
  background-image: url(../flags/4x3/hn.svg);
}
.flag-icon-hn.flag-icon-squared {
  background-image: url(../flags/1x1/hn.svg);
}

.flag-icon-hr {
  background-image: url(../flags/4x3/hr.svg);
}
.flag-icon-hr.flag-icon-squared {
  background-image: url(../flags/1x1/hr.svg);
}

.flag-icon-ht {
  background-image: url(../flags/4x3/ht.svg);
}
.flag-icon-ht.flag-icon-squared {
  background-image: url(../flags/1x1/ht.svg);
}

.flag-icon-hu {
  background-image: url(../flags/4x3/hu.svg);
}
.flag-icon-hu.flag-icon-squared {
  background-image: url(../flags/1x1/hu.svg);
}

.flag-icon-id {
  background-image: url(../flags/4x3/id.svg);
}
.flag-icon-id.flag-icon-squared {
  background-image: url(../flags/1x1/id.svg);
}

.flag-icon-ie {
  background-image: url(../flags/4x3/ie.svg);
}
.flag-icon-ie.flag-icon-squared {
  background-image: url(../flags/1x1/ie.svg);
}

.flag-icon-il {
  background-image: url(../flags/4x3/il.svg);
}
.flag-icon-il.flag-icon-squared {
  background-image: url(../flags/1x1/il.svg);
}

.flag-icon-im {
  background-image: url(../flags/4x3/im.svg);
}
.flag-icon-im.flag-icon-squared {
  background-image: url(../flags/1x1/im.svg);
}

.flag-icon-in {
  background-image: url(../flags/4x3/in.svg);
}
.flag-icon-in.flag-icon-squared {
  background-image: url(../flags/1x1/in.svg);
}

.flag-icon-io {
  background-image: url(../flags/4x3/io.svg);
}
.flag-icon-io.flag-icon-squared {
  background-image: url(../flags/1x1/io.svg);
}

.flag-icon-iq {
  background-image: url(../flags/4x3/iq.svg);
}
.flag-icon-iq.flag-icon-squared {
  background-image: url(../flags/1x1/iq.svg);
}

.flag-icon-ir {
  background-image: url(../flags/4x3/ir.svg);
}
.flag-icon-ir.flag-icon-squared {
  background-image: url(../flags/1x1/ir.svg);
}

.flag-icon-is {
  background-image: url(../flags/4x3/is.svg);
}
.flag-icon-is.flag-icon-squared {
  background-image: url(../flags/1x1/is.svg);
}

.flag-icon-it {
  background-image: url(../flags/4x3/it.svg);
}
.flag-icon-it.flag-icon-squared {
  background-image: url(../flags/1x1/it.svg);
}

.flag-icon-je {
  background-image: url(../flags/4x3/je.svg);
}
.flag-icon-je.flag-icon-squared {
  background-image: url(../flags/1x1/je.svg);
}

.flag-icon-jm {
  background-image: url(../flags/4x3/jm.svg);
}
.flag-icon-jm.flag-icon-squared {
  background-image: url(../flags/1x1/jm.svg);
}

.flag-icon-jo {
  background-image: url(../flags/4x3/jo.svg);
}
.flag-icon-jo.flag-icon-squared {
  background-image: url(../flags/1x1/jo.svg);
}

.flag-icon-jp {
  background-image: url(../flags/4x3/jp.svg);
}
.flag-icon-jp.flag-icon-squared {
  background-image: url(../flags/1x1/jp.svg);
}

.flag-icon-ke {
  background-image: url(../flags/4x3/ke.svg);
}
.flag-icon-ke.flag-icon-squared {
  background-image: url(../flags/1x1/ke.svg);
}

.flag-icon-kg {
  background-image: url(../flags/4x3/kg.svg);
}
.flag-icon-kg.flag-icon-squared {
  background-image: url(../flags/1x1/kg.svg);
}

.flag-icon-kh {
  background-image: url(../flags/4x3/kh.svg);
}
.flag-icon-kh.flag-icon-squared {
  background-image: url(../flags/1x1/kh.svg);
}

.flag-icon-ki {
  background-image: url(../flags/4x3/ki.svg);
}
.flag-icon-ki.flag-icon-squared {
  background-image: url(../flags/1x1/ki.svg);
}

.flag-icon-km {
  background-image: url(../flags/4x3/km.svg);
}
.flag-icon-km.flag-icon-squared {
  background-image: url(../flags/1x1/km.svg);
}

.flag-icon-kn {
  background-image: url(../flags/4x3/kn.svg);
}
.flag-icon-kn.flag-icon-squared {
  background-image: url(../flags/1x1/kn.svg);
}

.flag-icon-kp {
  background-image: url(../flags/4x3/kp.svg);
}
.flag-icon-kp.flag-icon-squared {
  background-image: url(../flags/1x1/kp.svg);
}

.flag-icon-kr {
  background-image: url(../flags/4x3/kr.svg);
}
.flag-icon-kr.flag-icon-squared {
  background-image: url(../flags/1x1/kr.svg);
}

.flag-icon-kw {
  background-image: url(../flags/4x3/kw.svg);
}
.flag-icon-kw.flag-icon-squared {
  background-image: url(../flags/1x1/kw.svg);
}

.flag-icon-ky {
  background-image: url(../flags/4x3/ky.svg);
}
.flag-icon-ky.flag-icon-squared {
  background-image: url(../flags/1x1/ky.svg);
}

.flag-icon-kz {
  background-image: url(../flags/4x3/kz.svg);
}
.flag-icon-kz.flag-icon-squared {
  background-image: url(../flags/1x1/kz.svg);
}

.flag-icon-la {
  background-image: url(../flags/4x3/la.svg);
}
.flag-icon-la.flag-icon-squared {
  background-image: url(../flags/1x1/la.svg);
}

.flag-icon-lb {
  background-image: url(../flags/4x3/lb.svg);
}
.flag-icon-lb.flag-icon-squared {
  background-image: url(../flags/1x1/lb.svg);
}

.flag-icon-lc {
  background-image: url(../flags/4x3/lc.svg);
}
.flag-icon-lc.flag-icon-squared {
  background-image: url(../flags/1x1/lc.svg);
}

.flag-icon-li {
  background-image: url(../flags/4x3/li.svg);
}
.flag-icon-li.flag-icon-squared {
  background-image: url(../flags/1x1/li.svg);
}

.flag-icon-lk {
  background-image: url(../flags/4x3/lk.svg);
}
.flag-icon-lk.flag-icon-squared {
  background-image: url(../flags/1x1/lk.svg);
}

.flag-icon-lr {
  background-image: url(../flags/4x3/lr.svg);
}
.flag-icon-lr.flag-icon-squared {
  background-image: url(../flags/1x1/lr.svg);
}

.flag-icon-ls {
  background-image: url(../flags/4x3/ls.svg);
}
.flag-icon-ls.flag-icon-squared {
  background-image: url(../flags/1x1/ls.svg);
}

.flag-icon-lt {
  background-image: url(../flags/4x3/lt.svg);
}
.flag-icon-lt.flag-icon-squared {
  background-image: url(../flags/1x1/lt.svg);
}

.flag-icon-lu {
  background-image: url(../flags/4x3/lu.svg);
}
.flag-icon-lu.flag-icon-squared {
  background-image: url(../flags/1x1/lu.svg);
}

.flag-icon-lv {
  background-image: url(../flags/4x3/lv.svg);
}
.flag-icon-lv.flag-icon-squared {
  background-image: url(../flags/1x1/lv.svg);
}

.flag-icon-ly {
  background-image: url(../flags/4x3/ly.svg);
}
.flag-icon-ly.flag-icon-squared {
  background-image: url(../flags/1x1/ly.svg);
}

.flag-icon-ma {
  background-image: url(../flags/4x3/ma.svg);
}
.flag-icon-ma.flag-icon-squared {
  background-image: url(../flags/1x1/ma.svg);
}

.flag-icon-mc {
  background-image: url(../flags/4x3/mc.svg);
}
.flag-icon-mc.flag-icon-squared {
  background-image: url(../flags/1x1/mc.svg);
}

.flag-icon-md {
  background-image: url(../flags/4x3/md.svg);
}
.flag-icon-md.flag-icon-squared {
  background-image: url(../flags/1x1/md.svg);
}

.flag-icon-me {
  background-image: url(../flags/4x3/me.svg);
}
.flag-icon-me.flag-icon-squared {
  background-image: url(../flags/1x1/me.svg);
}

.flag-icon-mf {
  background-image: url(../flags/4x3/mf.svg);
}
.flag-icon-mf.flag-icon-squared {
  background-image: url(../flags/1x1/mf.svg);
}

.flag-icon-mg {
  background-image: url(../flags/4x3/mg.svg);
}
.flag-icon-mg.flag-icon-squared {
  background-image: url(../flags/1x1/mg.svg);
}

.flag-icon-mh {
  background-image: url(../flags/4x3/mh.svg);
}
.flag-icon-mh.flag-icon-squared {
  background-image: url(../flags/1x1/mh.svg);
}

.flag-icon-mk {
  background-image: url(../flags/4x3/mk.svg);
}
.flag-icon-mk.flag-icon-squared {
  background-image: url(../flags/1x1/mk.svg);
}

.flag-icon-ml {
  background-image: url(../flags/4x3/ml.svg);
}
.flag-icon-ml.flag-icon-squared {
  background-image: url(../flags/1x1/ml.svg);
}

.flag-icon-mm {
  background-image: url(../flags/4x3/mm.svg);
}
.flag-icon-mm.flag-icon-squared {
  background-image: url(../flags/1x1/mm.svg);
}

.flag-icon-mn {
  background-image: url(../flags/4x3/mn.svg);
}
.flag-icon-mn.flag-icon-squared {
  background-image: url(../flags/1x1/mn.svg);
}

.flag-icon-mo {
  background-image: url(../flags/4x3/mo.svg);
}
.flag-icon-mo.flag-icon-squared {
  background-image: url(../flags/1x1/mo.svg);
}

.flag-icon-mp {
  background-image: url(../flags/4x3/mp.svg);
}
.flag-icon-mp.flag-icon-squared {
  background-image: url(../flags/1x1/mp.svg);
}

.flag-icon-mq {
  background-image: url(../flags/4x3/mq.svg);
}
.flag-icon-mq.flag-icon-squared {
  background-image: url(../flags/1x1/mq.svg);
}

.flag-icon-mr {
  background-image: url(../flags/4x3/mr.svg);
}
.flag-icon-mr.flag-icon-squared {
  background-image: url(../flags/1x1/mr.svg);
}

.flag-icon-ms {
  background-image: url(../flags/4x3/ms.svg);
}
.flag-icon-ms.flag-icon-squared {
  background-image: url(../flags/1x1/ms.svg);
}

.flag-icon-mt {
  background-image: url(../flags/4x3/mt.svg);
}
.flag-icon-mt.flag-icon-squared {
  background-image: url(../flags/1x1/mt.svg);
}

.flag-icon-mu {
  background-image: url(../flags/4x3/mu.svg);
}
.flag-icon-mu.flag-icon-squared {
  background-image: url(../flags/1x1/mu.svg);
}

.flag-icon-mv {
  background-image: url(../flags/4x3/mv.svg);
}
.flag-icon-mv.flag-icon-squared {
  background-image: url(../flags/1x1/mv.svg);
}

.flag-icon-mw {
  background-image: url(../flags/4x3/mw.svg);
}
.flag-icon-mw.flag-icon-squared {
  background-image: url(../flags/1x1/mw.svg);
}

.flag-icon-mx {
  background-image: url(../flags/4x3/mx.svg);
}
.flag-icon-mx.flag-icon-squared {
  background-image: url(../flags/1x1/mx.svg);
}

.flag-icon-my {
  background-image: url(../flags/4x3/my.svg);
}
.flag-icon-my.flag-icon-squared {
  background-image: url(../flags/1x1/my.svg);
}

.flag-icon-mz {
  background-image: url(../flags/4x3/mz.svg);
}
.flag-icon-mz.flag-icon-squared {
  background-image: url(../flags/1x1/mz.svg);
}

.flag-icon-na {
  background-image: url(../flags/4x3/na.svg);
}
.flag-icon-na.flag-icon-squared {
  background-image: url(../flags/1x1/na.svg);
}

.flag-icon-nc {
  background-image: url(../flags/4x3/nc.svg);
}
.flag-icon-nc.flag-icon-squared {
  background-image: url(../flags/1x1/nc.svg);
}

.flag-icon-ne {
  background-image: url(../flags/4x3/ne.svg);
}
.flag-icon-ne.flag-icon-squared {
  background-image: url(../flags/1x1/ne.svg);
}

.flag-icon-nf {
  background-image: url(../flags/4x3/nf.svg);
}
.flag-icon-nf.flag-icon-squared {
  background-image: url(../flags/1x1/nf.svg);
}

.flag-icon-ng {
  background-image: url(../flags/4x3/ng.svg);
}
.flag-icon-ng.flag-icon-squared {
  background-image: url(../flags/1x1/ng.svg);
}

.flag-icon-ni {
  background-image: url(../flags/4x3/ni.svg);
}
.flag-icon-ni.flag-icon-squared {
  background-image: url(../flags/1x1/ni.svg);
}

.flag-icon-nl {
  background-image: url(../flags/4x3/nl.svg);
}
.flag-icon-nl.flag-icon-squared {
  background-image: url(../flags/1x1/nl.svg);
}

.flag-icon-no {
  background-image: url(../flags/4x3/no.svg);
}
.flag-icon-no.flag-icon-squared {
  background-image: url(../flags/1x1/no.svg);
}

.flag-icon-np {
  background-image: url(../flags/4x3/np.svg);
}
.flag-icon-np.flag-icon-squared {
  background-image: url(../flags/1x1/np.svg);
}

.flag-icon-nr {
  background-image: url(../flags/4x3/nr.svg);
}
.flag-icon-nr.flag-icon-squared {
  background-image: url(../flags/1x1/nr.svg);
}

.flag-icon-nu {
  background-image: url(../flags/4x3/nu.svg);
}
.flag-icon-nu.flag-icon-squared {
  background-image: url(../flags/1x1/nu.svg);
}

.flag-icon-nz {
  background-image: url(../flags/4x3/nz.svg);
}
.flag-icon-nz.flag-icon-squared {
  background-image: url(../flags/1x1/nz.svg);
}

.flag-icon-om {
  background-image: url(../flags/4x3/om.svg);
}
.flag-icon-om.flag-icon-squared {
  background-image: url(../flags/1x1/om.svg);
}

.flag-icon-pa {
  background-image: url(../flags/4x3/pa.svg);
}
.flag-icon-pa.flag-icon-squared {
  background-image: url(../flags/1x1/pa.svg);
}

.flag-icon-pe {
  background-image: url(../flags/4x3/pe.svg);
}
.flag-icon-pe.flag-icon-squared {
  background-image: url(../flags/1x1/pe.svg);
}

.flag-icon-pf {
  background-image: url(../flags/4x3/pf.svg);
}
.flag-icon-pf.flag-icon-squared {
  background-image: url(../flags/1x1/pf.svg);
}

.flag-icon-pg {
  background-image: url(../flags/4x3/pg.svg);
}
.flag-icon-pg.flag-icon-squared {
  background-image: url(../flags/1x1/pg.svg);
}

.flag-icon-ph {
  background-image: url(../flags/4x3/ph.svg);
}
.flag-icon-ph.flag-icon-squared {
  background-image: url(../flags/1x1/ph.svg);
}

.flag-icon-pk {
  background-image: url(../flags/4x3/pk.svg);
}
.flag-icon-pk.flag-icon-squared {
  background-image: url(../flags/1x1/pk.svg);
}

.flag-icon-pl {
  background-image: url(../flags/4x3/pl.svg);
}
.flag-icon-pl.flag-icon-squared {
  background-image: url(../flags/1x1/pl.svg);
}

.flag-icon-pm {
  background-image: url(../flags/4x3/pm.svg);
}
.flag-icon-pm.flag-icon-squared {
  background-image: url(../flags/1x1/pm.svg);
}

.flag-icon-pn {
  background-image: url(../flags/4x3/pn.svg);
}
.flag-icon-pn.flag-icon-squared {
  background-image: url(../flags/1x1/pn.svg);
}

.flag-icon-pr {
  background-image: url(../flags/4x3/pr.svg);
}
.flag-icon-pr.flag-icon-squared {
  background-image: url(../flags/1x1/pr.svg);
}

.flag-icon-ps {
  background-image: url(../flags/4x3/ps.svg);
}
.flag-icon-ps.flag-icon-squared {
  background-image: url(../flags/1x1/ps.svg);
}

.flag-icon-pt {
  background-image: url(../flags/4x3/pt.svg);
}
.flag-icon-pt.flag-icon-squared {
  background-image: url(../flags/1x1/pt.svg);
}

.flag-icon-pw {
  background-image: url(../flags/4x3/pw.svg);
}
.flag-icon-pw.flag-icon-squared {
  background-image: url(../flags/1x1/pw.svg);
}

.flag-icon-py {
  background-image: url(../flags/4x3/py.svg);
}
.flag-icon-py.flag-icon-squared {
  background-image: url(../flags/1x1/py.svg);
}

.flag-icon-qa {
  background-image: url(../flags/4x3/qa.svg);
}
.flag-icon-qa.flag-icon-squared {
  background-image: url(../flags/1x1/qa.svg);
}

.flag-icon-re {
  background-image: url(../flags/4x3/re.svg);
}
.flag-icon-re.flag-icon-squared {
  background-image: url(../flags/1x1/re.svg);
}

.flag-icon-ro {
  background-image: url(../flags/4x3/ro.svg);
}
.flag-icon-ro.flag-icon-squared {
  background-image: url(../flags/1x1/ro.svg);
}

.flag-icon-rs {
  background-image: url(../flags/4x3/rs.svg);
}
.flag-icon-rs.flag-icon-squared {
  background-image: url(../flags/1x1/rs.svg);
}

.flag-icon-ru {
  background-image: url(../flags/4x3/ru.svg);
}
.flag-icon-ru.flag-icon-squared {
  background-image: url(../flags/1x1/ru.svg);
}

.flag-icon-rw {
  background-image: url(../flags/4x3/rw.svg);
}
.flag-icon-rw.flag-icon-squared {
  background-image: url(../flags/1x1/rw.svg);
}

.flag-icon-sa {
  background-image: url(../flags/4x3/sa.svg);
}
.flag-icon-sa.flag-icon-squared {
  background-image: url(../flags/1x1/sa.svg);
}

.flag-icon-sb {
  background-image: url(../flags/4x3/sb.svg);
}
.flag-icon-sb.flag-icon-squared {
  background-image: url(../flags/1x1/sb.svg);
}

.flag-icon-sc {
  background-image: url(../flags/4x3/sc.svg);
}
.flag-icon-sc.flag-icon-squared {
  background-image: url(../flags/1x1/sc.svg);
}

.flag-icon-sd {
  background-image: url(../flags/4x3/sd.svg);
}
.flag-icon-sd.flag-icon-squared {
  background-image: url(../flags/1x1/sd.svg);
}

.flag-icon-se {
  background-image: url(../flags/4x3/se.svg);
}
.flag-icon-se.flag-icon-squared {
  background-image: url(../flags/1x1/se.svg);
}

.flag-icon-sg {
  background-image: url(../flags/4x3/sg.svg);
}
.flag-icon-sg.flag-icon-squared {
  background-image: url(../flags/1x1/sg.svg);
}

.flag-icon-sh {
  background-image: url(../flags/4x3/sh.svg);
}
.flag-icon-sh.flag-icon-squared {
  background-image: url(../flags/1x1/sh.svg);
}

.flag-icon-si {
  background-image: url(../flags/4x3/si.svg);
}
.flag-icon-si.flag-icon-squared {
  background-image: url(../flags/1x1/si.svg);
}

.flag-icon-sj {
  background-image: url(../flags/4x3/sj.svg);
}
.flag-icon-sj.flag-icon-squared {
  background-image: url(../flags/1x1/sj.svg);
}

.flag-icon-sk {
  background-image: url(../flags/4x3/sk.svg);
}
.flag-icon-sk.flag-icon-squared {
  background-image: url(../flags/1x1/sk.svg);
}

.flag-icon-sl {
  background-image: url(../flags/4x3/sl.svg);
}
.flag-icon-sl.flag-icon-squared {
  background-image: url(../flags/1x1/sl.svg);
}

.flag-icon-sm {
  background-image: url(../flags/4x3/sm.svg);
}
.flag-icon-sm.flag-icon-squared {
  background-image: url(../flags/1x1/sm.svg);
}

.flag-icon-sn {
  background-image: url(../flags/4x3/sn.svg);
}
.flag-icon-sn.flag-icon-squared {
  background-image: url(../flags/1x1/sn.svg);
}

.flag-icon-so {
  background-image: url(../flags/4x3/so.svg);
}
.flag-icon-so.flag-icon-squared {
  background-image: url(../flags/1x1/so.svg);
}

.flag-icon-sr {
  background-image: url(../flags/4x3/sr.svg);
}
.flag-icon-sr.flag-icon-squared {
  background-image: url(../flags/1x1/sr.svg);
}

.flag-icon-ss {
  background-image: url(../flags/4x3/ss.svg);
}
.flag-icon-ss.flag-icon-squared {
  background-image: url(../flags/1x1/ss.svg);
}

.flag-icon-st {
  background-image: url(../flags/4x3/st.svg);
}
.flag-icon-st.flag-icon-squared {
  background-image: url(../flags/1x1/st.svg);
}

.flag-icon-sv {
  background-image: url(../flags/4x3/sv.svg);
}
.flag-icon-sv.flag-icon-squared {
  background-image: url(../flags/1x1/sv.svg);
}

.flag-icon-sx {
  background-image: url(../flags/4x3/sx.svg);
}
.flag-icon-sx.flag-icon-squared {
  background-image: url(../flags/1x1/sx.svg);
}

.flag-icon-sy {
  background-image: url(../flags/4x3/sy.svg);
}
.flag-icon-sy.flag-icon-squared {
  background-image: url(../flags/1x1/sy.svg);
}

.flag-icon-sz {
  background-image: url(../flags/4x3/sz.svg);
}
.flag-icon-sz.flag-icon-squared {
  background-image: url(../flags/1x1/sz.svg);
}

.flag-icon-tc {
  background-image: url(../flags/4x3/tc.svg);
}
.flag-icon-tc.flag-icon-squared {
  background-image: url(../flags/1x1/tc.svg);
}

.flag-icon-td {
  background-image: url(../flags/4x3/td.svg);
}
.flag-icon-td.flag-icon-squared {
  background-image: url(../flags/1x1/td.svg);
}

.flag-icon-tf {
  background-image: url(../flags/4x3/tf.svg);
}
.flag-icon-tf.flag-icon-squared {
  background-image: url(../flags/1x1/tf.svg);
}

.flag-icon-tg {
  background-image: url(../flags/4x3/tg.svg);
}
.flag-icon-tg.flag-icon-squared {
  background-image: url(../flags/1x1/tg.svg);
}

.flag-icon-th {
  background-image: url(../flags/4x3/th.svg);
}
.flag-icon-th.flag-icon-squared {
  background-image: url(../flags/1x1/th.svg);
}

.flag-icon-tj {
  background-image: url(../flags/4x3/tj.svg);
}
.flag-icon-tj.flag-icon-squared {
  background-image: url(../flags/1x1/tj.svg);
}

.flag-icon-tk {
  background-image: url(../flags/4x3/tk.svg);
}
.flag-icon-tk.flag-icon-squared {
  background-image: url(../flags/1x1/tk.svg);
}

.flag-icon-tl {
  background-image: url(../flags/4x3/tl.svg);
}
.flag-icon-tl.flag-icon-squared {
  background-image: url(../flags/1x1/tl.svg);
}

.flag-icon-tm {
  background-image: url(../flags/4x3/tm.svg);
}
.flag-icon-tm.flag-icon-squared {
  background-image: url(../flags/1x1/tm.svg);
}

.flag-icon-tn {
  background-image: url(../flags/4x3/tn.svg);
}
.flag-icon-tn.flag-icon-squared {
  background-image: url(../flags/1x1/tn.svg);
}

.flag-icon-to {
  background-image: url(../flags/4x3/to.svg);
}
.flag-icon-to.flag-icon-squared {
  background-image: url(../flags/1x1/to.svg);
}

.flag-icon-tr {
  background-image: url(../flags/4x3/tr.svg);
}
.flag-icon-tr.flag-icon-squared {
  background-image: url(../flags/1x1/tr.svg);
}

.flag-icon-tt {
  background-image: url(../flags/4x3/tt.svg);
}
.flag-icon-tt.flag-icon-squared {
  background-image: url(../flags/1x1/tt.svg);
}

.flag-icon-tv {
  background-image: url(../flags/4x3/tv.svg);
}
.flag-icon-tv.flag-icon-squared {
  background-image: url(../flags/1x1/tv.svg);
}

.flag-icon-tw {
  background-image: url(../flags/4x3/tw.svg);
}
.flag-icon-tw.flag-icon-squared {
  background-image: url(../flags/1x1/tw.svg);
}

.flag-icon-tz {
  background-image: url(../flags/4x3/tz.svg);
}
.flag-icon-tz.flag-icon-squared {
  background-image: url(../flags/1x1/tz.svg);
}

.flag-icon-ua {
  background-image: url(../flags/4x3/ua.svg);
}
.flag-icon-ua.flag-icon-squared {
  background-image: url(../flags/1x1/ua.svg);
}

.flag-icon-ug {
  background-image: url(../flags/4x3/ug.svg);
}
.flag-icon-ug.flag-icon-squared {
  background-image: url(../flags/1x1/ug.svg);
}

.flag-icon-um {
  background-image: url(../flags/4x3/um.svg);
}
.flag-icon-um.flag-icon-squared {
  background-image: url(../flags/1x1/um.svg);
}

.flag-icon-us {
  background-image: url(../flags/4x3/us.svg);
}
.flag-icon-us.flag-icon-squared {
  background-image: url(../flags/1x1/us.svg);
}

.flag-icon-uy {
  background-image: url(../flags/4x3/uy.svg);
}
.flag-icon-uy.flag-icon-squared {
  background-image: url(../flags/1x1/uy.svg);
}

.flag-icon-uz {
  background-image: url(../flags/4x3/uz.svg);
}
.flag-icon-uz.flag-icon-squared {
  background-image: url(../flags/1x1/uz.svg);
}

.flag-icon-va {
  background-image: url(../flags/4x3/va.svg);
}
.flag-icon-va.flag-icon-squared {
  background-image: url(../flags/1x1/va.svg);
}

.flag-icon-vc {
  background-image: url(../flags/4x3/vc.svg);
}
.flag-icon-vc.flag-icon-squared {
  background-image: url(../flags/1x1/vc.svg);
}

.flag-icon-ve {
  background-image: url(../flags/4x3/ve.svg);
}
.flag-icon-ve.flag-icon-squared {
  background-image: url(../flags/1x1/ve.svg);
}

.flag-icon-vg {
  background-image: url(../flags/4x3/vg.svg);
}
.flag-icon-vg.flag-icon-squared {
  background-image: url(../flags/1x1/vg.svg);
}

.flag-icon-vi {
  background-image: url(../flags/4x3/vi.svg);
}
.flag-icon-vi.flag-icon-squared {
  background-image: url(../flags/1x1/vi.svg);
}

.flag-icon-vn {
  background-image: url(../flags/4x3/vn.svg);
}
.flag-icon-vn.flag-icon-squared {
  background-image: url(../flags/1x1/vn.svg);
}

.flag-icon-vu {
  background-image: url(../flags/4x3/vu.svg);
}
.flag-icon-vu.flag-icon-squared {
  background-image: url(../flags/1x1/vu.svg);
}

.flag-icon-wf {
  background-image: url(../flags/4x3/wf.svg);
}
.flag-icon-wf.flag-icon-squared {
  background-image: url(../flags/1x1/wf.svg);
}

.flag-icon-ws {
  background-image: url(../flags/4x3/ws.svg);
}
.flag-icon-ws.flag-icon-squared {
  background-image: url(../flags/1x1/ws.svg);
}

.flag-icon-ye {
  background-image: url(../flags/4x3/ye.svg);
}
.flag-icon-ye.flag-icon-squared {
  background-image: url(../flags/1x1/ye.svg);
}

.flag-icon-yt {
  background-image: url(../flags/4x3/yt.svg);
}
.flag-icon-yt.flag-icon-squared {
  background-image: url(../flags/1x1/yt.svg);
}

.flag-icon-za {
  background-image: url(../flags/4x3/za.svg);
}
.flag-icon-za.flag-icon-squared {
  background-image: url(../flags/1x1/za.svg);
}

.flag-icon-zm {
  background-image: url(../flags/4x3/zm.svg);
}
.flag-icon-zm.flag-icon-squared {
  background-image: url(../flags/1x1/zm.svg);
}

.flag-icon-zw {
  background-image: url(../flags/4x3/zw.svg);
}
.flag-icon-zw.flag-icon-squared {
  background-image: url(../flags/1x1/zw.svg);
}

.flag-icon-ac {
  background-image: url(../flags/4x3/ac.svg);
}
.flag-icon-ac.flag-icon-squared {
  background-image: url(../flags/1x1/ac.svg);
}

.flag-icon-cp {
  background-image: url(../flags/4x3/cp.svg);
}
.flag-icon-cp.flag-icon-squared {
  background-image: url(../flags/1x1/cp.svg);
}

.flag-icon-dg {
  background-image: url(../flags/4x3/dg.svg);
}
.flag-icon-dg.flag-icon-squared {
  background-image: url(../flags/1x1/dg.svg);
}

.flag-icon-ea {
  background-image: url(../flags/4x3/ea.svg);
}
.flag-icon-ea.flag-icon-squared {
  background-image: url(../flags/1x1/ea.svg);
}

.flag-icon-es-ct {
  background-image: url(../flags/4x3/es-ct.svg);
}
.flag-icon-es-ct.flag-icon-squared {
  background-image: url(../flags/1x1/es-ct.svg);
}

.flag-icon-es-ga {
  background-image: url(../flags/4x3/es-ga.svg);
}
.flag-icon-es-ga.flag-icon-squared {
  background-image: url(../flags/1x1/es-ga.svg);
}

.flag-icon-eu {
  background-image: url(../flags/4x3/eu.svg);
}
.flag-icon-eu.flag-icon-squared {
  background-image: url(../flags/1x1/eu.svg);
}

.flag-icon-gb-eng {
  background-image: url(../flags/4x3/gb-eng.svg);
}
.flag-icon-gb-eng.flag-icon-squared {
  background-image: url(../flags/1x1/gb-eng.svg);
}

.flag-icon-gb-nir {
  background-image: url(../flags/4x3/gb-nir.svg);
}
.flag-icon-gb-nir.flag-icon-squared {
  background-image: url(../flags/1x1/gb-nir.svg);
}

.flag-icon-gb-sct {
  background-image: url(../flags/4x3/gb-sct.svg);
}
.flag-icon-gb-sct.flag-icon-squared {
  background-image: url(../flags/1x1/gb-sct.svg);
}

.flag-icon-gb-wls {
  background-image: url(../flags/4x3/gb-wls.svg);
}
.flag-icon-gb-wls.flag-icon-squared {
  background-image: url(../flags/1x1/gb-wls.svg);
}

.flag-icon-ic {
  background-image: url(../flags/4x3/ic.svg);
}
.flag-icon-ic.flag-icon-squared {
  background-image: url(../flags/1x1/ic.svg);
}

.flag-icon-ta {
  background-image: url(../flags/4x3/ta.svg);
}
.flag-icon-ta.flag-icon-squared {
  background-image: url(../flags/1x1/ta.svg);
}

.flag-icon-un {
  background-image: url(../flags/4x3/un.svg);
}
.flag-icon-un.flag-icon-squared {
  background-image: url(../flags/1x1/un.svg);
}

.flag-icon-xk {
  background-image: url(../flags/4x3/xk.svg);
}
.flag-icon-xk.flag-icon-squared {
  background-image: url(../flags/1x1/xk.svg);
}

.flag-icon-xx {
  background-image: url(../flags/4x3/xx.svg);
}
.flag-icon-xx.flag-icon-squared {
  background-image: url(../flags/1x1/xx.svg);
}

.chart-container {
  position: relative; /* for absolutely positioned tooltip */
  /* https://www.smashingmagazine.com/2015/11/using-system-ui-fonts-practical-guide/ */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}
.chart-container .axis, .chart-container .chart-label {
  fill: #555b51;
}
.chart-container .axis line, .chart-container .chart-label line {
  stroke: #dadada;
}
.chart-container .dataset-units circle {
  stroke: #fff;
  stroke-width: 2;
}
.chart-container .dataset-units path {
  fill: none;
  stroke-opacity: 1;
  stroke-width: 2px;
}
.chart-container .dataset-path {
  stroke-width: 2px;
}
.chart-container .path-group path {
  fill: none;
  stroke-opacity: 1;
  stroke-width: 2px;
}
.chart-container line.dashed {
  stroke-dasharray: 5, 3;
}
.chart-container .axis-line .specific-value {
  text-anchor: start;
}
.chart-container .axis-line .y-line {
  text-anchor: end;
}
.chart-container .axis-line .x-line {
  text-anchor: middle;
}
.chart-container .legend-dataset-text {
  fill: #6c7680;
  font-weight: 600;
}

.graph-svg-tip {
  position: absolute;
  z-index: 99999;
  padding: 10px;
  font-size: 12px;
  color: #959da5;
  text-align: center;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 3px;
}
.graph-svg-tip ul {
  padding-left: 0;
  display: flex;
}
.graph-svg-tip ol {
  padding-left: 0;
  display: flex;
}
.graph-svg-tip ul.data-point-list li {
  min-width: 90px;
  flex: 1;
  font-weight: 600;
}
.graph-svg-tip strong {
  color: #dfe2e5;
  font-weight: 600;
}
.graph-svg-tip .svg-pointer {
  position: absolute;
  height: 5px;
  margin: 0 0 0 -5px;
  content: " ";
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
}
.graph-svg-tip.comparison {
  padding: 0;
  text-align: left;
  pointer-events: none;
}
.graph-svg-tip.comparison .title {
  display: block;
  padding: 10px;
  margin: 0;
  font-weight: 600;
  line-height: 1;
  pointer-events: none;
}
.graph-svg-tip.comparison ul {
  margin: 0;
  white-space: nowrap;
  list-style: none;
}
.graph-svg-tip.comparison li {
  display: inline-block;
  padding: 5px 10px;
}

@font-face {
  font-family: "Helvetica Neue";
  src: url("../fonts/HelveticaNeue.woff2") format("woff2"), url("../fonts/HelveticaNeue.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Helvetica Neue";
  src: url("../fonts/HelveticaNeue-Medium.woff2") format("woff2"), url("../fonts/HelveticaNeue-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Helvetica Neue";
  src: url("../fonts/HelveticaNeue-Bold.woff2") format("woff2"), url("../fonts/HelveticaNeue-Bold.woff") format("woff");
  font-weight: Bold;
  font-style: normal;
  font-display: swap;
}
body.dark-theme {
  background-color: #181c34;
}
body.dark-theme .preloader-container,
body.dark-theme .bg-additional-grey {
  background-color: #181c34;
}

.dropify-wrapper {
  border-radius: 0.25rem;
  border: 1px solid #e8eef3;
  z-index: 0;
}

.dt-buttons button {
  font-size: 14px;
  line-height: unset;
  padding: 0.5rem;
}

/*********** CUSTOM SELECT *************/
.select2-container--default .select2-selection--single {
  border: 1px solid #e8eef3;
  border-radius: 3px;
  height: 35px;
}

.select2-container .select2-selection--single .select2-selection__rendered,
.select2-results__option {
  font-size: 14px;
  text-transform: capitalize;
  color: #28313c;
  line-height: 2.5;
  padding-left: 6px;
}

.select2-container--open .select2-dropdown {
  border: 0;
  box-shadow: 1px 4px 6px 4px rgba(104, 104, 104, 0.0784313725);
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border: none;
  font-family: "simple-line-icons";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin-top: -2px;
  font-size: 10px;
  color: #99A5B5;
  font-weight: 800 !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  right: 5px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #1d82f5;
  color: #fff;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b:before {
  content: "\e604";
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b:before {
  content: "\e607";
}

.select2-results__option[aria-selected] {
  padding: 5px 8px !important;
}

.filter-box .select2-container--default .select2-selection--single {
  border: none;
}

.filter-box .select2-selection__rendered {
  font-weight: 500;
}

.bootstrap-select .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.bootstrap-select .dropup .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}

.task-detail-panel {
  white-space: unset;
}

.no-js #loader {
  display: none;
}

.js #loader {
  display: block;
  position: absolute;
  left: 100px;
  top: 0;
}

.preloader-container {
  position: fixed;
  right: 0;
  left: 0;
  top: 0px;
  width: calc(100% - 240px);
  z-index: 9999;
  background: #F2F4F7;
  margin-left: 240px;
  margin-top: 63px;
  min-height: calc(100vh - 63px);
}

.sidebar-toggled .preloader-container {
  margin-left: 60px;
  width: calc(100% - 60px);
}

.rtl .preloader-container {
  margin-right: 240px;
  margin-left: 0px;
  width: calc(100% - 60px);
}

.rtl.sidebar-toggled .preloader-container {
  margin-right: 60px;
  margin-left: 0px;
}

.input-group .bootstrap-select.form-control .dropdown-toggle,
.bootstrap-select > .dropdown-toggle {
  border-color: #e8eef3;
  background-color: #fff;
  padding: 0.5rem;
  font-size: 14px;
}

.ql-toolbar.ql-snow {
  border-color: #e8eef3;
  border-radius: 0.25rem 0.25rem 0 0;
}
@media (max-width: 991.98px) {
  .ql-toolbar.ql-snow {
    display: flex;
    flex-wrap: wrap;
  }
}

.ql-container.ql-snow {
  border-color: #e8eef3;
  border-radius: 0 0 0.25rem 0.25rem;
}

label sup {
  color: #D30000;
}

table h5 a {
  color: #28313c;
}
table h5 a:hover {
  text-decoration: underline;
}

.filter-box .select-box .bootstrap-select .dropdown-toggle,
.filter-box .select-box .bootstrap-select .btn-light:not(:disabled):not(.disabled):active,
.filter-box .select-box .bootstrap-select .btn-light:not(:disabled):not(.disabled).active {
  border: none;
  background-color: unset;
  font-size: 14px;
  text-transform: capitalize;
  color: #28313c;
  padding: 0.2rem 0.5rem;
}

.filter-box .bootstrap-select .dropdown-toggle .filter-option-inner-inner {
  font-weight: 500;
}

.table [contenteditable=true]:hover,
.table [contenteditable=true]:focus {
  background-color: #F2F4F7;
}

.table [contenteditable=true]:hover::after {
  content: "Click to edit";
  display: block;
  font-size: 11px;
  color: #99A5B5;
  position: absolute;
}

.table [contenteditable=true]:focus::after {
  content: "Click anywhere to save";
  display: block;
  font-size: 11px;
  color: #99A5B5;
  position: absolute;
}

.bootstrap-select.form-control.is-invalid {
  border: 1px solid #D30000;
}

.w-15 {
  width: 15px;
}

.w-20 {
  width: 20px;
}

.more-filters .more-filter-tab {
  top: 112px;
}

.multiple-users .btn .badge {
  top: 0;
}

.input-group-prepend .btn,
.input-group-append .btn {
  z-index: 1;
}

#myModal {
  z-index: 1051;
}

.taskEmployeeImg {
  border: 1px solid #e8eef3;
}

.taskEmployeeImg img {
  width: 25px;
  height: 25px;
  -o-object-fit: cover;
     object-fit: cover;
}

img.taskEmployeeImg {
  width: 30px;
  height: 30px;
}

.badge img.taskEmployeeImg {
  width: 25px;
  height: 25px;
}

.filter-option-inner-inner img.taskEmployeeImg {
  width: 20px;
  height: 20px;
}

.bootstrap-timepicker-widget table td input {
  width: 100%;
  height: 35px;
}

.bootstrap-timepicker-widget.dropdown-menu.open {
  width: 100%;
}

.glyphicon {
  cursor: pointer;
  background-color: transparent;
  font-family: "simple-line-icons";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
}

.glyphicon-chevron-up:before {
  content: "\e607";
}

.glyphicon-chevron-down:before {
  content: "\e604";
}

.dataTable .bootstrap-select .filter-option-inner-inner {
  font-size: 13px;
}

.modal-dialog-scrollable .modal-body {
  overflow-y: visible;
}

.modal-dialog-scrollable .modal-content {
  overflow: visible;
}

.file-card .card-img,
.file-card .card-img img {
  width: 40px;
  height: 40px;
  border: none;
}

.file-card .card-img svg {
  font-size: 40px;
}

.file-card .card-body {
  width: 158px;
}

.dropzone .dz-preview.dz-image-preview {
  z-index: 0;
}

ul.thumbnails.image_picker_selector li .thumbnail {
  padding: 4px !important;
}

ul.thumbnails.image_picker_selector li .thumbnail.selected {
  background: #1d82f5 !important;
}

.pr-20 {
  padding-right: 20px !important;
}

.icon-11 {
  width: 11px;
  height: 11px;
}

.fc .fc-col-header-cell-cushion {
  color: #28313c;
  padding: 10px 4px !important;
}

.btn-active,
.btn-active.btn-secondary {
  background-color: black !important;
  border: solid 1px #000 !important;
  color: #fff !important;
}

.active-timer-count {
  left: 10px;
  top: -7px;
}

/* attendance info */
.punch-info {
  margin-bottom: 20px;
}

.punch-hours {
  align-items: center;
  border: 5px solid #1d82f5;
  border-radius: 50%;
  display: flex;
  font-size: 18px;
  height: 120px;
  justify-content: center;
  margin: 0 auto;
  width: 120px;
}

.statistics .row {
  margin-left: -5px;
  margin-right: -5px;
}

.statistics .row > div {
  padding-left: 5px;
  padding-right: 5px;
}

.punch-status .stats-box {
  margin-bottom: 0;
}

.stats-box {
  background-color: #f9f9f9;
  border: 1px solid #e3e3e3;
  margin-bottom: 15px;
  padding: 5px;
}

.stats-box p {
  margin: 0;
  font-size: 12px;
}

.stats-box > h6 {
  margin-bottom: 0;
}

.recent-activity {
  height: 318px;
  overflow-y: auto;
  overflow-x: hidden;
}

.recent-activity .res-activity-list {
  list-style-type: none;
  margin-bottom: 0;
  padding-left: 30px;
  position: relative;
}

.recent-activity .res-activity-list li {
  margin-bottom: 15px;
  position: relative;
}

.recent-activity .res-activity-list li:before {
  content: "";
  position: absolute;
  border-radius: 100%;
  width: 10px;
  height: 10px;
  left: -15px;
  top: 6px;
  border: 2px solid #1d82f5;
  margin-right: 15px;
  z-index: 2;
  background: #fff;
}

.recent-activity p {
  font-size: 13px;
  margin-bottom: 0;
}

.recent-activity .res-activity-time {
  color: #bbb;
  font-size: 12px;
}

.recent-activity .res-activity-list:after {
  content: "";
  border: 1px solid #e8eef3;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 19px;
}

.ticket-message:hover {
  background-color: #e8eef3 !important;
}
.ticket-message:hover p {
  color: #28313c;
}

.dropify-wrapper ~ .invalid-feedback {
  display: block;
}

.input-daterange input {
  width: 99px !important;
}
@media (max-width: 767.98px) {
  .input-daterange input {
    width: 96px !important;
  }
}

.sidebar-menu li {
  box-shadow: 0 1px 0 0 rgba(232, 238, 243, 0.0784313725);
}

.content-wrapper {
  padding: 20px 28px;
}

.settings-box {
  padding: 18px 20px;
}

.sticky-note {
  height: 215px;
}

.sticky-note .card-body:first-child {
  overflow-y: clip;
}

.sidebar-brand-name img {
  height: 25px;
}

.sidebar-dark .main-sidebar {
  background-color: #171f29 !important;
}
.sidebar-dark .sidebar-brand-box {
  background-color: #171f29 !important;
}
.sidebar-dark .sidebar-menu {
  background-color: #171f29 !important;
}
.sidebar-dark .sidebarTogglerBox {
  background-color: #171f29 !important;
}

.sidebar-light .main-sidebar {
  background-color: #f7faff;
  border-right: 1px solid #e8eef3;
}
.sidebar-light .sidebar-brand-box {
  background-color: unset;
}
.sidebar-light .sidebar-menu {
  background-color: unset;
}
.sidebar-light .sidebar-menu li {
  box-shadow: 0 1px 0 0 #e8eef3;
}
.sidebar-light .sidebar-menu li .nav-item {
  color: #616e80;
}
.sidebar-light .sidebarTogglerBox {
  background-color: #f7faff;
  border-right: 1px solid #e8eef3;
  border-top: 1px solid #e8eef3;
}
.sidebar-light .sidebar-brand-box .sidebar-brand {
  border-bottom: 1px solid #e8eef3;
}
.sidebar-light .sidebar-brand-name h1 {
  color: #28313c;
}
.sidebar-light .sidebar-brand-logo {
  color: #28313c;
}

a.btn-primary {
  display: inline-block;
}

#datatableRange,
#datatableRange2 {
  width: 220px;
}

.daterangepicker td.in-range {
  background-color: #f1f1f3;
}

.daterangepicker .ranges li.active {
  background-color: #616e80;
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: #616e80;
}

.user-online {
  bottom: -5px;
  right: 8px;
}

@media (max-width: 991.98px) {
  .preloader-container {
    margin-left: 0px;
    width: unset;
  }
  .rtl .preloader-container {
    margin-right: 0;
  }
}
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

@media (max-width: 991.98px) {
  .daterangepicker {
    position: absolute;
    z-index: 1;
  }
}

@media (max-width: 991.98px) {
  .w-50 {
    width: 100% !important;
  }
  .table-md-responsive {
    width: 100% !important;
  }
}
@media (max-width: 767.98px) {
  .table-sm-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
  }
  ul.thumbnails.image_picker_selector li {
    width: 100%;
  }
  ul.thumbnails.image_picker_selector li img {
    width: 100%;
  }
  #leave-detail-section .w-30,
#leave-detail-section .w-70,
#right-modal-content .w-30,
#right-modal-content .w-70 {
    width: 100%;
  }
  .fc .fc-toolbar {
    display: block !important;
  }
  .fc-toolbar-chunk {
    margin-bottom: 20px;
  }
  .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 100% !important;
  }
  .w-sm-100 {
    width: 100%;
  }
}
#calendar .fc-toolbar-title {
  font-size: 13px !important;
}

.tagify {
  overflow: hidden;
}

.input-group .input-icon {
  width: 100%;
}

.right-sidebar {
  height: 100%;
  width: 100%;
  white-space: nowrap;
}
.right-sidebar li a {
  padding: 15px 24px;
}
.right-sidebar li a:hover {
  color: #28313c !important;
  background-color: rgba(232, 238, 243, 0.5215686275);
  border-right: 2px solid rgba(40, 49, 60, 0.5215686275);
}

.select-filter-project .dropdown-menu {
  left: 29px;
}
.select-filter-project .dropdown-menu .right-sidebar {
  height: 100%;
  width: 100%;
  white-space: nowrap;
}
.select-filter-project .dropdown-menu .right-sidebar li a {
  padding: 15px 24px;
}
.select-filter-project .dropdown-menu .right-sidebar li a:hover {
  color: #28313c !important;
  background-color: rgba(232, 238, 243, 0.5215686275);
  border-right: 2px solid rgba(40, 49, 60, 0.5215686275);
}

.swal2-popup.swal2-toast {
  flex-direction: row !important;
}

#user-search {
  width: 249px;
}

@media (max-width: 1199.98px) {
  #user-search {
    width: 225px;
  }
}
@media (max-width: 991.98px) {
  .w-tables {
    overflow-x: scroll;
  }
}
@media (max-width: 767.98px) {
  .table-sm-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
  }
}
.fc .fc-daygrid-body {
  position: relative;
  z-index: 0 !important;
}

.fc .fc-button-group > .fc-button.fc-button-active,
.fc .fc-button-group > .fc-button:active,
.fc .fc-button-group > .fc-button:focus,
.fc .fc-button-group > .fc-button:hover {
  z-index: 0 !important;
}

.swal2-title {
  font-size: 1.1em !important;
}

.swal2-content {
  font-size: 0.8em !important;
}

.mw-250 {
  max-width: 250px;
  min-width: 140px;
}

.mw-120 {
  max-width: 120px;
}

.typing {
  align-items: center;
  display: flex;
  height: 13px;
}

.typing .dot {
  animation: mercuryTypingAnimation 1.8s infinite ease-in-out;
  background-color: #e8eef2;
  border-radius: 50%;
  height: 4px;
  margin-right: 2px;
  vertical-align: middle;
  width: 4px;
  display: inline-block;
}

.typing .dot:nth-child(1) {
  animation-delay: 200ms;
}

.typing .dot:nth-child(2) {
  animation-delay: 300ms;
}

.typing .dot:nth-child(3) {
  animation-delay: 400ms;
}

.typing .dot:last-child {
  margin-right: 0;
}

.autocomplete-password {
  opacity: 0;
  position: absolute;
  width: 0;
}

@keyframes mercuryTypingAnimation {
  0% {
    transform: translateY(0px);
    background-color: #e8eef2;
  }
  28% {
    transform: translateY(-7px);
    background-color: #aaadaf;
  }
  44% {
    transform: translateY(0px);
    background-color: #87888a;
  }
}
.more-user-count {
  padding: 2px 3px;
}

.custom-control {
  z-index: auto;
}

.flag-icon-ja-jp.flag-icon-squared {
  background-image: url(../flags/1x1/ja-JP.svg);
}

.bootstrap-select > select.mobile-device {
  width: auto !important;
  z-index: auto !important;
}

.importBox {
  min-height: 280px !important;
  width: 250px !important;
  margin: 5px 0 !important;
}

.importOptions {
  padding: 10px !important;
  /* height: 150px !important; */
  vertical-align: middle !important;
}

.unchanged {
  border: 2px solid #d0d0d0;
}

.matched {
  border: 2px solid #52bad5;
}

.importSample .sampleHeading, .importSample .sample {
  padding: 5px 15px;
  margin: 0px;
}

.unchanged .sampleHeading {
  background-color: #e0e0e0;
}

.unchanged .sample {
  background-color: #ffffff;
}

.matched .sampleHeading {
  background-color: #52BAD5;
}

.matched .sample {
  background-color: #B1E0EC;
}

.unmatched .sampleHeading {
  background-color: #EE836E;
}

.unmatched .sample {
  background-color: #f1f1f3;
}

.importBox .notimported {
  padding: 5px 10px;
  margin: 5px 0px;
}

.sampleHeading, .sample {
  overflow-x: hidden;
  max-height: 31px;
  overflow-y: hidden;
}

.dropzone .dz-preview .dz-image img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.dropzone .dz-preview .dz-progress {
  z-index: 999 !important;
}

.w-180 {
  width: 180px;
}

.w-150 {
  width: 150px;
}

.view-notification p {
  line-height: 16px;
}

.fc-view-harness {
  z-index: 0;
}

.fc-daygrid-dot-event .fc-event-title {
  font-weight: normal !important;
}

.deactive {
  opacity: 0.6;
}

.icon-background {
  font-size: 40px;
}

.notification-apr-icon {
  top: 9px;
  left: 9px;
}

.fs-40 {
  font-size: 40px;
}

.fs-30 {
  font-size: 30px;
}

.dropdown-item.active .text-muted {
  color: #fff !important;
}

.dropdown-item.active .text-darkest-grey {
  color: #fff !important;
}

.select2-selection {
  height: 37px !important;
}

.select2-container--default .select2-selection--single {
  border-color: #e8eef3;
  background-color: #fff;
  padding: 0.2rem;
  font-size: 14px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-style: none;
}

.width-35 {
  width: 35px;
}

.width-40 {
  width: 40px;
}

.btrr {
  border-top-right-radius: 4px;
}

.mw-300 {
  max-width: 300px;
}

#terms_link {
  display: contents !important;
  color: #007bff !important;
  font-size: 15px;
}

.dark-theme .ql-mention-list-item {
  background-color: #29304c !important;
}

.dark-theme .ql-mention-list-item.selected {
  background-color: #181c34 !important;
}

.dark-theme .ql-mention-list-container {
  border: 1px solid #4B4E69 !important;
}

.w-30 {
  width: 30%;
}

.w-70 {
  width: 70%;
}

.height-35 {
  height: 39px !important;
}

.height-40 {
  height: 40px !important;
}

.height-44 {
  height: 44px !important;
}

.height-50 {
  height: 50px !important;
}

.px-6 {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.p-20 {
  padding: 20px !important;
}

.pl-20 {
  padding-left: 20px !important;
}

.py-20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.mt-94 {
  margin-top: 94px;
}

.mt-105 {
  margin-top: 105px;
}
@media (max-width: 991.98px) {
  .mt-105 {
    margin-top: 0px;
  }
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mr-30 {
  margin-right: 30px;
}

.b-shadow-4 {
  box-shadow: 0 0 4px 0 #e8eef3;
}

.b-r-8 {
  border-radius: 8px !important;
}

.d-grid {
  display: grid;
}

@media (max-width: 991.98px) {
  .mt-md-94 {
    margin-top: 94px;
  }
}
.login_section {
  min-height: calc(100vh - 56px);
}

.login_header {
  box-shadow: 0 1px 0 0 #e8eef3;
  padding: 12px 0px;
}
.login_header img {
  max-height: 32px;
}
.login_header h3 {
  font-size: 21px;
  color: #28313c;
  font-weight: normal;
}

.login_box {
  width: 452px;
  padding: 30px 30px;
}
@media (max-width: 767.98px) {
  .login_box {
    width: 95%;
  }
}
.login_box h3 {
  font-size: 21px;
  color: #28313c;
  font-weight: normal;
}
.login_box a {
  border: 1px solid #e8eef3;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0px;
  color: #28313c;
  font-size: 18px;
}
.login_box a:focus, .login_box a:hover {
  border-color: #f8f9fa;
  box-shadow: none;
}
.login_box a span {
  width: 28px;
  height: 28px;
  background-color: #F3F3F3;
  border-radius: 50%;
  padding: 5px;
  margin-right: 13px;
  overflow: hidden;
  line-height: 17px;
}
.login_box p {
  font-size: 15px;
  color: #616e80;
  font-weight: normal;
}
.login_box p:after, .login_box p:before {
  content: "";
  width: 81px;
  height: 1px;
  position: absolute;
  background-color: #99A5B5;
  top: 50%;
}
@media (max-width: 767.98px) {
  .login_box p:after, .login_box p:before {
    content: none;
  }
}
.login_box p:before {
  left: 0;
}
.login_box p:after {
  right: 0;
}

.login_box .form-group label {
  font-size: 15px;
  color: #616e80;
}

.forgot_pswd a {
  color: #616e80;
  padding: 0;
  border: none;
  text-decoration: underline;
  font-size: 12px;
  justify-content: left;
}

.form-control {
  border: 1px solid #e8eef3;
  box-shadow: none;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
  background-color: #fff;
  color: #28313C;
  font-weight: 400;
  position: relative;
  padding: 0px 6px 0px;
  height: auto;
}
.form-control:focus, .form-control:hover {
  border-color: #28313C;
  box-shadow: none;
}

:focus::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  opacity: 0.5;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
:focus::-moz-placeholder { /* Firefox 19+ */
  opacity: 0.5;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
:focus:-ms-input-placeholder { /* IE 10+ */
  opacity: 0.5;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
:focus:-moz-placeholder { /* Firefox 18- */
  opacity: 0.5;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

textarea {
  overflow: hidden;
}

.form-control::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: #99A5B5;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.form-control::-moz-placeholder { /* Firefox 19+ */
  color: #99A5B5;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.form-control:-ms-input-placeholder { /* IE 10+ */
  color: #99A5B5;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.form-control:-moz-placeholder { /* Firefox 18- */
  color: #99A5B5;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.cursor-pointer {
  cursor: pointer;
}

.form-check-input {
  height: 20px;
  width: 20px;
  vertical-align: middle;
}

/*********** CUSTOM CHECKBOX *************/
.form_custom_label {
  display: flex;
  position: relative;
  padding-left: 25px;
  cursor: pointer;
  font-size: 13px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: #1d82f5;
  color: #28313c;
  align-items: center;
  justify-content: center;
}

.form_custom_label input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  border: solid 2px #1d82f5;
  background-color: #fff;
}
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.form_custom_label.checkmark-20 {
  padding-top: 3px;
}
.form_custom_label.checkmark-20 .checkmark {
  height: 20px;
  width: 20px;
  border-radius: 4px;
}
.form_custom_label.checkmark-20 .checkmark:after {
  left: 5.5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form_custom_label.checkmark-15 {
  padding-top: 1px;
}
.form_custom_label.checkmark-15 .checkmark {
  height: 15px;
  width: 15px;
  border-radius: 3px;
}
.form_custom_label.checkmark-15 .checkmark:after {
  left: 3.5px;
  top: 0.5px;
  width: 5px;
  height: 8px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form_custom_label input:checked ~ .checkmark {
  background-color: #1d82f5;
}

.form_custom_label input:checked ~ .checkmark:after {
  display: block;
}

.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #1d82f5;
  background-color: #1d82f5;
}

.custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(29, 130, 245, 0.1607843137);
}

/*********** DROPDOWN *************/
.dropdown-toggle::after, .dropup .dropdown-toggle::after {
  content: none;
}

.dropdown-item {
  font-size: 14px;
  line-height: 1.99;
  color: #28313c;
}

.form-group label sup {
  color: #fd0202;
  top: 0px;
  right: -4px;
}

.dropdown-item span {
  padding-right: 20px;
}

/*********** CUSTOM DATEPICKER *************/
.input-daterange input {
  width: 99px;
}
@media (max-width: 767.98px) {
  .input-daterange input {
    width: 96px;
  }
}

.input-daterange input::-moz-placeholder {
  color: #28313c !important;
  font-weight: 500;
}

.input-daterange input::placeholder {
  color: #28313c !important;
  font-weight: 500;
}

.qs-datepicker-container .qs-overlay input::-moz-placeholder {
  color: #fff !important;
}

.qs-datepicker-container .qs-overlay input::placeholder {
  color: #fff !important;
}

.qs-controls {
  background: #F2F4F7 !important;
  padding: 8px !important;
}

.qs-month-year {
  font-size: 15px;
}

.qs-day {
  font-weight: 700;
  color: black;
  font-size: 14px;
  margin: 10px 0px;
}

.qs-num {
  font-size: 14px;
  padding: 16px 0px;
}

.qs-datepicker-container {
  border: 0px;
}

.qs-square:not(.qs-empty):not(.qs-disabled):not(.qs-day):not(.qs-active):hover {
  background: #f2f4f7;
}

.qs-current {
  background: #1d82f5;
  color: #fff;
  text-decoration: none;
}
.qs-current:hover {
  background: #1d82f5 !important;
}

.qs-active, .qs-range-end, .qs-range-start {
  background: #616e80;
  color: #fff;
}

.input-group-text {
  border: 1px solid #e8eef3;
}

.input-group-append .btn {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* =====  Button ===== */
button:focus {
  box-shadow: none !important;
}

button.disabled, button:disabled {
  opacity: 0.65;
}

.btn-primary {
  text-transform: capitalize;
  padding: 9px 11px;
  background-color: #1d82f5 !important;
  color: #fff !important;
  border: 1px solid #1d82f5 !important;
  position: relative;
}
.btn-primary:hover {
  background-color: black !important;
  border: 1px solid black !important;
  color: #fff !important;
}

.btn-primary.disabled, .btn-primary:disabled {
  background-color: #1d82f5;
}
.btn-primary.disabled:hover, .btn-primary:disabled:hover {
  background-color: #1d82f5 !important;
  border: 1px solid #1d82f5 !important;
  cursor: not-allowed;
}

.btn-secondary {
  text-transform: capitalize;
  padding: 9px 11px;
  border: solid 1px #616e80;
  background-color: #fff !important;
  color: #616e80 !important;
  position: relative;
}
.btn-secondary:hover {
  background-color: black !important;
  border: solid 1px #000 !important;
  color: #fff !important;
}

.btn-secondary.disabled:hover, .btn-secondary:disabled:hover {
  border: solid 1px #616e80 !important;
  background-color: #fff !important;
  color: #616e80 !important;
  cursor: not-allowed;
}

.btn-cancel {
  text-transform: capitalize;
  padding: 9px 11px;
  border: 1px solid #fff;
  background-color: #fff !important;
  color: #99A5B5 !important;
  position: relative;
}
.btn-cancel:hover {
  background-color: black !important;
  border: solid 1px #000 !important;
  color: #fff !important;
}

.btn-cancel.disabled:hover, .btn-cancel:disabled:hover {
  border: 1px solid #fff !important;
  background-color: #fff !important;
  color: #99A5B5 !important;
  cursor: not-allowed;
}

.btn-danger {
  text-transform: capitalize;
  background-color: #D30000;
  color: #fff;
  border: 0px;
  position: relative;
  padding: 9px 11px;
}
.btn-danger:hover {
  background-color: black;
}

.btn-danger.disabled:hover, .btn-danger:disabled:hover {
  border: 0px !important;
  background-color: #D30000 !important;
  cursor: not-allowed;
}

/*********************************** MAIN CONTENT *********************************/
.content-wrapper {
  padding: 20px 28px;
}
@media (max-width: 991.98px) {
  .content-wrapper {
    padding: 0px 25px 44px 25px;
  }
}

.main-container {
  margin-left: 240px;
  min-height: calc(100vh - 63px);
  position: relative;
  transition: width 0.3s ease-in-out, margin-left 0.3s ease-in-out;
}
@media (max-width: 991.98px) {
  .main-container {
    margin-left: 0px;
  }
}

.page-title {
  padding: 10px 28px;
  transition: width 0.3s ease-in-out;
}
@media (max-width: 991.98px) {
  .page-title {
    padding: 24px 24px;
  }
}

.taskExportBtns button {
  padding: 9px 11px;
}

@media (max-width: 767.98px) {
  div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    justify-content: start !important;
  }
}
.task-detail-panel {
  background-color: #fff;
  position: fixed;
  top: 0px;
  right: -320px;
  width: 240px;
  z-index: 3;
  transition: all 0.5s ease;
  border: solid 1px #e8eef3;
  height: 100vh;
}

.task-detail-panel.in {
  transition: all 0.5s ease;
  width: 85%;
  right: 0px;
}
@media (max-width: 767.98px) {
  .task-detail-panel.in {
    width: 100%;
  }
  .task-detail-panel.in .w-25 {
    width: 40% !important;
  }
}

.close-task-detail {
  position: absolute;
  left: -55px;
  top: 17px;
  background-color: #1D82F5;
  padding: 9px 17px !important;
  color: #fff;
  border-radius: 22px 0px 0px 22px;
  font-size: 12px;
  transition: width 0.7s;
  opacity: 0;
}
@media (max-width: 767.98px) {
  .close-task-detail {
    left: auto;
    right: 0;
    background-color: transparent;
    position: fixed;
    z-index: -1;
  }
}
.close-task-detail span {
  width: 20px;
  height: 20px;
  border: 1px solid #1D82F5;
  border-radius: 100%;
  position: relative;
  display: block;
  transition: all 0.5s ease;
}
@media (max-width: 767.98px) {
  .close-task-detail span {
    border: 1px solid #28313c;
  }
}
.close-task-detail span .fa-times {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
  top: 3px;
  color: white;
}
.close-task-detail:hover span {
  border: 1px solid #fff;
  transition: all 0.5s ease;
}

.close-task-detail.in {
  opacity: 1;
  transition: width 0.7s;
  z-index: 1;
  display: block !important;
}

@media (max-width: 767.98px) {
  .task-detail-panel.in .close-task-detail.in {
    opacity: 1;
    transition: width 0.7s;
    display: block !important;
  }
  .task-detail-panel .close-task-detail {
    opacity: 0;
    transition: width 0.7s;
  }
}
.t-d-inner-panel {
  height: 100%;
  overflow: auto;
}

.task-overlay {
  position: fixed;
  top: 0;
  right: 0px;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 3;
  display: none;
  transition: width 0.3s;
}

.task-overlay.in {
  display: block !important;
  transition: width 0.3s;
}

.filter-box {
  box-shadow: 0 1px 0 0 #e8eef3;
  width: 100%;
  top: 63px;
  position: sticky;
  z-index: 1;
}
@media (max-width: 991.98px) {
  .filter-box {
    width: 100%;
    position: initial;
  }
}

.more-filters {
  border-left: solid 1px #e8eef3;
}
@media (max-width: 991.98px) {
  .more-filters {
    border-left: 0px;
  }
}
.more-filters a {
  line-height: 2;
}
.more-filters .more-filter-tab {
  background-color: #fff;
  position: fixed;
  top: 62px;
  right: 0;
  z-index: 9;
  border: solid 1px #e8eef3;
  white-space: nowrap;
  overflow-x: hidden;
  transform: translate3d(273px, 0, 0);
  height: 100%;
  width: 268px;
  transition: all ease-in 0.3s;
}
.more-filters .more-filter-tab .clear-all {
  transition: all ease 0.3s;
  position: fixed;
  width: 100% !important;
}
.more-filters .more-filter-tab h3 {
  line-height: 1.17;
  padding: 18px 28px 0;
}
.more-filters .more-filter-tab label {
  line-height: 1.14;
  padding: 0px 28px;
  margin-bottom: 12px;
}
.more-filters .more-filter-tab.in {
  transform: translate3d(0px, 0, 0);
  transition: all ease-in 0.3s;
  width: 268px;
}
.more-filters .more-filter-tab.in .filter-detail {
  height: calc(100vh - 197px);
  overflow-y: auto;
  overflow-x: hidden;
}
.more-filters .more-filter-tab.in .clear-all {
  transition: width ease 0.3s;
  padding: 0px 28px;
  border-top: solid 1px #e8eef3;
}
.more-filters .more-filter-tab.in .clear-all button {
  padding: 9px 11px;
  margin-right: 9px;
}
.more-filters .filter-detail {
  height: calc(100vh - 197px);
}

.close-more-filter {
  position: absolute;
  right: 28px;
  top: 14px;
}
.close-more-filter:hover {
  color: #000;
}

.select-filter {
  padding: 0px 28px;
}

/******************** MAIN SIDEBAR *********************/
.mobile-close-sidebar-panel {
  display: none;
}

@media (max-width: 991.98px) {
  .mobile-close-sidebar-panel.toggled {
    height: 100%;
    position: fixed;
    top: 0;
    left: 100px;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 99;
    display: block;
  }
}
.main-sidebar {
  z-index: 2;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 240px;
  transition: width 0.3s;
}
@media (max-width: 991.98px) {
  .main-sidebar {
    transition: 0.3s;
    top: 0px;
    overflow-x: auto;
    transform: translate3d(-293px, 0, 0);
  }
}

/******************** SIDEBAR BRAND *********************/
.sidebar-brand-box {
  z-index: 1 !important;
  transition: width 0.3s;
}
.sidebar-brand-box .sidebar-brand {
  padding: 8px 20px 8px 20px;
  transition: width 0.3s;
  border-bottom: 1px solid #252d37;
}
.sidebar-brand-box .sidebar-brand:after {
  content: none;
}
.sidebar-brand-box .sidebar-brand .sidebar-brand-name {
  width: 80%;
  word-break: break-word;
  overflow: hidden;
}
.sidebar-brand-box .sidebar-brand .sidebar-brand-name h1 {
  line-height: 1.38;
  white-space: nowrap;
  width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sidebar-brand-box .sidebar-brand .sidebar-brand-name h1 i {
  font-size: 10px;
  font-weight: 700;
}
.sidebar-brand-box .sidebar-brand .sidebar-brand-name .pro-name {
  line-height: 1.69;
  margin-left: 20px;
}
.sidebar-brand-box .sidebar-brand .sidebar-brand-name .pro-name span {
  width: 12px;
  height: 12px;
  position: absolute;
  top: 4px;
  left: -20px;
}
.sidebar-brand-box .sidebar-brand .sidebar-brand-name .pro-name p {
  white-space: nowrap;
  width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sidebar-brand-box .sidebar-brand-dropdown {
  width: 300px;
  left: 0px !important;
  border-radius: 3px;
  border: 1px solid #e8eef3;
}
@media (max-width: 991.98px) {
  .sidebar-brand-box .sidebar-brand-dropdown {
    transform: translate3d(-2px, 60px, 0px) !important;
    padding-bottom: 10px;
    position: fixed !important;
    top: 0px;
  }
}
.sidebar-brand-box .sidebar-brand-dropdown .profile-box {
  padding: 16px 24px;
}
.sidebar-brand-box .sidebar-brand-dropdown .profile-box a {
  color: #616e80;
}
.sidebar-brand-box .sidebar-brand-dropdown .profile-box a:hover {
  color: #1d82f5;
}
.sidebar-brand-box .sidebar-brand-dropdown .profile-box .profileImg {
  width: 36px;
  height: 36px;
  border-radius: 3px;
  border: solid 1px #e8eef3;
  overflow: hidden;
}
.sidebar-brand-box .sidebar-brand-dropdown .profile-box .profileImg img {
  -o-object-fit: cover;
     object-fit: cover;
}
.sidebar-brand-box .sidebar-brand-dropdown .profile-box .ProfileData h3 {
  line-height: 1.13;
  margin-bottom: 5px;
  word-break: break-all;
  white-space: nowrap;
  width: 177px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sidebar-brand-box .sidebar-brand-dropdown .profile-box .ProfileData p {
  line-height: 1.09;
}
.sidebar-brand-box .sidebar-brand-dropdown a.dropdown-item {
  line-height: 1.87;
  white-space: initial;
  line-break: anywhere;
}
.sidebar-brand-box .sidebar-brand-dropdown a.dropdown-item:hover {
  background-color: #1d82f5;
  color: #fff !important;
}
.sidebar-brand-box .sidebar-brand-dropdown .custom-control.custom-switch {
  margin-right: -8px;
}

/******************** SIDEBAR MENU *********************/
.closeIt .accordionItemContent {
  height: 0px;
  transform: scaleY(0);
  float: left;
  display: block;
}

.openIt .accordionItemContent {
  display: block;
  transform: scaleY(1);
  transform-origin: top;
  box-sizing: border-box;
}

.accordionItem a.active {
  color: #f7faff;
}

.sidebar-menu {
  height: calc(100% - 111px);
  z-index: 0;
  display: block !important;
  white-space: nowrap;
  overflow: auto;
  overflow-x: hidden;
}
@media (max-width: 991.98px) {
  .sidebar-menu {
    height: calc(100% - 63px);
  }
}
.sidebar-menu li {
  position: relative;
}
.sidebar-menu li .accordionItemContent a {
  padding: 0.5rem 1.2rem;
  display: block;
  white-space: nowrap;
  margin-left: 36px;
}
.sidebar-menu li .accordionItemContent a:hover {
  color: #f7faff !important;
}
.sidebar-menu li .nav-item {
  line-height: 1.87;
  white-space: nowrap;
  display: block;
  width: 230px;
  overflow: hidden;
  text-overflow: ellipsis;
  align-items: center;
  padding: 10px 20px 10px 20px;
}
.sidebar-menu li .nav-item:focus, .sidebar-menu li .nav-item:hover {
  color: #f7faff !important;
}

.side-icon {
  width: 16px;
  vertical-align: middle;
  font-size: 16px;
  display: inline-block;
  position: relative;
}

.accordionItem.openIt .accordionItemHeading::after {
  content: "\e604";
  font-family: "simple-line-icons";
  display: inline-block;
  font-style: normal;
  font-stretch: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font-weight: 900;
  line-height: 27px;
  font-size: 10px;
  position: absolute;
  right: 15px;
}

.accordionItem.closeIt .accordionItemHeading::after {
  content: "\e606";
  font-family: "simple-line-icons";
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font-weight: 900;
  line-height: 27px;
  font-size: 10px;
  position: absolute;
  right: 15px;
}

.sidebar-brand-logo {
  max-width: 32px;
  border-radius: 4px;
  overflow: hidden;
}

/******************** SIDEBAR TOGGLER BOX *********************/
.sidebarTogglerBox {
  width: 240px;
  transition: width 0.3s;
  z-index: 2;
  height: 48px;
  bottom: 0;
  padding: 0 16px;
  border-top: 1px solid #252d37;
}
@media (max-width: 991.98px) {
  .sidebarTogglerBox {
    display: none !important;
  }
}
.sidebarTogglerBox #sidebarToggle {
  font-family: "Font Awesome 5 Free";
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  background-color: transparent;
}
.sidebarTogglerBox p {
  display: block;
}

#sidebarToggle::before {
  content: "\f053";
}

/******************** SIDEBAR TOGGLED *********************/
.sidebar-toggled #sidebarToggle::before {
  content: "\f054";
}
.sidebar-toggled .main-header {
  margin-left: 60px;
  transition: width 0.3s ease-in-out;
}
.sidebar-toggled .main-container {
  margin-left: 60px;
  transition: width 0.3s ease-in-out;
}
.sidebar-toggled .sidebarTogglerBox, .sidebar-toggled .main-sidebar {
  width: 3.77rem !important;
  transition: width 0.3s;
}
.sidebar-toggled .sidebar-brand-box .sidebar-brand {
  padding: 14px;
  height: 63px;
  transition: width 0.3s;
}
.sidebar-toggled .accordionItem.closeIt .accordionItemHeading::after, .sidebar-toggled .accordionItem.openIt .accordionItemHeading::after, .sidebar-toggled .sidebarTogglerBox p, .sidebar-toggled .nav-item span, .sidebar-toggled .sidebar-brand-name, .sidebar-toggled .accordionItemContent {
  display: none;
}
.sidebar-toggled .sidebarTogglerBox button {
  margin: 0 auto;
}
.sidebar-toggled .main-sidebar:hover, .sidebar-toggled .main-sidebar:hover ~ .sidebarTogglerBox {
  width: 16rem !important;
  z-index: 99;
  transition: width 0.3s;
}
.sidebar-toggled .main-sidebar:hover ~ .sidebarTogglerBox p {
  display: block;
}
.sidebar-toggled .main-sidebar:hover .sidebar-brand-logo {
  margin: 0;
  padding: 0;
}
.sidebar-toggled .main-sidebar:hover .sidebar-brand {
  padding: 10px 16px 10px 20px;
}
.sidebar-toggled .main-sidebar:hover .collapse-inner {
  display: block;
}
.sidebar-toggled .main-sidebar:hover .accordionItem.closeIt .accordionItemHeading::after, .sidebar-toggled .main-sidebar:hover .sidebarTogglerBox p, .sidebar-toggled .main-sidebar:hover .nav-item span, .sidebar-toggled .main-sidebar:hover .sidebar-brand-name, .sidebar-toggled .main-sidebar:hover .accordionItemContent, .sidebar-toggled .main-sidebar:hover .accordionItem.openIt .accordionItemHeading::after {
  display: initial;
  transition: width 0.3s;
}

@media (max-width: 991.98px) {
  .main-sidebar.toggled {
    z-index: 9999;
    transition: 0.3s;
    overflow-x: inherit;
    transform: translate3d(0px, 0, 0);
  }
}
.sidebar-dark .sidebar-menu::-webkit-scrollbar {
  width: 10px;
  background: #262f3a;
}

.sidebar-dark .sidebar-menu::-webkit-scrollbar-thumb {
  background-color: #585858;
  border: 3px solid #262f3a;
}

.ui_header {
  background-color: #131523;
}
.ui_header h2 {
  padding: 34px 0px;
  font-size: 28px;
  font-weight: bold;
  line-height: 1.14;
  letter-spacing: 0.12px;
  color: #fff;
}

.ui_box {
  padding: 77px 0px;
}
.ui_box .ui_heading h4 {
  font-size: 28px;
  font-weight: 500;
  line-height: 1.14;
  letter-spacing: 0.12px;
  color: #131523;
  margin-bottom: 65px;
}
.ui_box .ntfcn_add_box {
  margin-top: 94px;
}
.ui_box .ui_sub_heading h4 {
  font-size: 28px;
  font-weight: 500;
  line-height: 1.14;
  letter-spacing: 0.12px;
  color: #28313c;
  margin-bottom: 31.9px;
}

@media (max-width: 767.98px) {
  .choose_ui_color .card {
    padding-left: 0px;
    padding-right: 0px;
  }
}
.choose_ui_color .ui_color_title {
  border-bottom: 1px solid #D5D7E3;
  margin-bottom: 20.8px;
}
.choose_ui_color .ui_color_title p {
  font-size: 16px;
  line-height: 1.13;
  letter-spacing: 0.1px;
  color: #7e84a3;
  margin-bottom: 23.5px;
}
.choose_ui_color .card-header {
  height: 130px;
  margin-bottom: 23.8px;
}
.choose_ui_color .card-body p {
  font-size: 23.4px;
  font-weight: 500;
  line-height: 1.33;
  text-align: left;
  color: #354052;
  margin-bottom: 0px;
}

.typo_heading p span:nth-child(5), .typo_heading p span:nth-child(6) {
  color: #131523;
}

.typography_and_elements {
  /****************** UI ELEMENTS **********************/
}
.typography_and_elements .typo_font p {
  font-size: 13px;
  line-height: 1.15;
  color: #131523;
}
.typography_and_elements .typo_font h2 {
  font-size: 55px;
  line-height: 1.16;
  color: #131523;
}
.typography_and_elements .typo_heading {
  margin-top: 41px;
  font-weight: normal;
  color: #28313c;
}
.typography_and_elements .typo_heading span {
  margin-right: 31px;
}
@media (max-width: 767.98px) {
  .typography_and_elements .typo_heading span {
    display: block;
    margin-bottom: 3rem;
  }
}
.typography_and_elements .ui_elements {
  margin-top: 105px;
}
.typography_and_elements .ui_elements .col {
  width: 20%;
}
@media (max-width: 991.98px) {
  .typography_and_elements .ui_elements .col {
    width: 50%;
  }
}
@media (max-width: 767.98px) {
  .typography_and_elements .ui_elements .col {
    width: 100%;
  }
}

.ui_cards_tables {
  background-color: #F2F4F7;
}

.ui_select .select2-container {
  width: 100% !important;
}

/*************************** BACKGROUND COLORS **************************/
.bg-dark {
  background-color: #171f29 !important;
}

.bg-grey {
  background-color: #e8eef3;
}

.bg-light-grey {
  background-color: #f1f1f3;
}

.bg-dark-grey {
  background-color: #616e80;
}

.bg-darkest-grey {
  background-color: #28313c;
}

.bg-lightest-grey {
  background-color: #99A5B5;
}

.bg-blue {
  background-color: #1d82f5;
}

.bg-red {
  background-color: #D30000;
}

.bg-yellow {
  background-color: #FCBD01;
}

.bg-light-green {
  background-color: #39e500;
}

.bg-dark-green {
  background-color: #2CB100;
}

.bg-additional-grey {
  background-color: #F2F4F7;
}

.bg-amt-grey {
  background-color: #e7e9eb;
}

.bg-white-shade {
  background-color: #f7faff;
}

/*************************** TEXT COLORS **************************/
.text-darkest-grey {
  color: #4d4f5c !important;
}

.text-dark-grey {
  color: #616e80;
}

.text-dark {
  color: #28313c !important;
}

.text-lightest {
  color: #99A5B5;
}

.text-blue {
  color: #1d82f5 !important;
}

.text-red {
  color: #D30000;
}

.text-yellow {
  color: #FCBD01;
}

.text-light-green {
  color: #39e500;
}

.text-dark-green {
  color: #2CB100;
}

.text-white-shade {
  color: #f7faff;
}

.text-pink {
  color: #ea4c89;
}

/*************************** BORDERS **************************/
.border-grey {
  border: 1px solid #e8eef3;
}

.border-left-grey {
  border-left: 1px solid #e8eef3;
}

.border-right-grey {
  border-right: 1px solid #e8eef3;
}

.border-top-grey {
  border-top: 1px solid #e8eef3;
}

.border-bottom-grey {
  border-bottom: 1px solid #e8eef3;
}

.border-additional-grey {
  border: 1px solid #F2F4F7;
}

@media (max-width: 767.98px) {
  .border-left-grey-sm-0 {
    border-left: 0px !important;
  }
  .border-right-grey-sm-0 {
    border-right: 0px !important;
  }
}
.f-w-500 {
  font-weight: 500 !important;
}

.f-8 {
  font-size: 8px;
}

.f-10 {
  font-size: 10px;
}

.f-11 {
  font-size: 11px;
}

.f-12 {
  font-size: 12px;
}

.f-13 {
  font-size: 13px;
}

.f-14 {
  font-size: 14px !important;
}

.f-15 {
  font-size: 15px !important;
}

.f-16 {
  font-size: 16px;
}

.f-18 {
  font-size: 18px;
}

.f-19 {
  font-size: 19px;
}

.f-20 {
  font-size: 20px;
}

.f-21 {
  font-size: 21px;
}

.f-27 {
  font-size: 27px;
}

.f-57 {
  font-size: 57px;
}

.heading-h1 {
  font-size: 21px;
  font-weight: bold;
  line-height: 1.14;
}

.heading-h2 {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.17;
}

.heading-h3 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.17;
}

.heading-h4 {
  font-size: 15px;
  font-weight: 500;
  line-height: 1.13;
}

.heading-h5 {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.14;
  letter-spacing: 0.1px;
}

.heading-h6 {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.17;
}

.simple-text {
  font-size: 14px;
  line-height: 1.14;
}

.font-weight-semibold {
  font-weight: 500 !important;
}

.avatar-img {
  width: 25px;
  height: 25px;
  border: solid 1px #707070;
  overflow: hidden;
}
.avatar-img img {
  -o-object-fit: cover;
     object-fit: cover;
  min-width: 25px;
  height: 25px;
}

.pagination .page-item.active .page-link {
  background-color: #1d82f5;
}

.pagination li {
  margin-right: 8px;
}
.pagination li a {
  border-radius: 4px;
  min-width: 35px;
  height: 35px;
  line-height: 1.54;
  font-size: 13px;
  color: #4d4f5c;
  border: 1px solid #e8eef3;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pagination li a:focus {
  box-shadow: none;
}

.page-item.active .page-link {
  background-color: #1d82f5;
  border-color: #1d82f5;
}
.page-item.active .page-link:hover, .page-item.active .page-link:focus {
  color: #fff !important;
  box-shadow: none;
}

div.dataTables_wrapper div.dataTables_info {
  padding-top: 1.3rem;
  padding-left: 1rem;
}

.page-item.active .page-link {
  z-index: 0;
}

.labels p {
  font-size: 14px;
  line-height: 1.14;
  color: #28313c;
  width: 50%;
}

.card-horizontal {
  display: flex;
  flex: 1 1 auto;
}

.card-img {
  margin: 1.25rem;
  width: 60px;
  height: 60px;
  -o-object-fit: contain;
     object-fit: contain;
  border-radius: 4px;
  overflow: hidden;
}
.card-img img {
  width: 60px;
  height: 60px;
  -o-object-fit: cover;
     object-fit: cover;
}

.card-img-small {
  flex-shrink: 0;
  margin: 1.25rem;
  width: 30px;
  height: 30px;
  -o-object-fit: contain;
     object-fit: contain;
  border-radius: 4px;
  overflow: hidden;
  border: solid 1px #616e80;
}

.card-title {
  line-height: 21px;
  margin-bottom: 7px;
}

.card-text {
  line-height: 1.5;
}

.w-tables {
  box-shadow: 0 0 4px 0 #d2d9e4;
}
@media (max-width: 767.98px) {
  .w-tables {
    overflow-x: scroll;
  }
}

.dataTables_wrapper {
  width: 100%;
}

.dataTables_filter {
  display: none;
}

.table thead th, .table th, .table td {
  border: 0px;
  padding: 6px;
}

.table thead th {
  font-size: 13px;
  font-weight: 500;
  color: #99A5B5;
  box-shadow: 0 1px 0 0 #f1f1f3;
}

.table thead th:first-child, .table tbody td:first-child {
  padding-left: 20px;
}

.table tr td {
  font-size: 13px;
  color: #28313c;
  box-shadow: 0 1px 0 0 #f1f1f3;
  vertical-align: middle;
}

.taskEmployeeImg {
  width: 25px;
  height: 25px;
  display: inline-block;
  overflow: hidden;
  -o-object-fit: cover;
     object-fit: cover;
}

.task_view {
  border: 1px solid #99A5B5;
  border-radius: 4px;
  display: inline-flex;
}
.task_view .taskView {
  padding: 0px 7px;
  line-height: 2;
  color: #99A5B5;
  border-right: 1px solid #99A5B5;
}
.task_view .taskView:hover {
  background-color: #F2F4F7;
}
.task_view .task_view_more {
  color: #99A5B5;
  padding: 6.3px 5px;
  border-radius: 4px;
}
.task_view .task_view_more:hover {
  background-color: #F2F4F7;
}
.task_view .task_view_more:after {
  content: none;
}
.task_view .dropdown-menu {
  border: 0;
  box-shadow: 1px 4px 6px 4px rgba(104, 104, 104, 0.0784313725);
}

table.dataTable {
  margin-bottom: 0px !important;
}
@media (max-width: 767.98px) {
  table.dataTable {
    width: 862px;
  }
}

.dataTables_paginate {
  margin: 12px !important;
}

.dataTables_length {
  padding: 15px 20px;
}

.dataTables_length select {
  height: 35px;
  min-width: 53px;
}

.dataTables_wrapper .table thead td, .dataTables_wrapper .table thead th {
  text-wrap: nowrap;
  border-bottom-width: 2px;
}

.message_wrapper {
  margin: 0px 9px;
  overflow: hidden;
  border: 1px solid #e8eef3;
}
.message_wrapper .msg-header {
  height: 62px;
  border-bottom: 1px solid #e8eef3;
}
.message_wrapper .msg-header .msg-header-left {
  padding: 0px 1.25rem;
}
@media (max-width: 991.98px) {
  .message_wrapper .msg-header .msg-header-left {
    max-width: 100%;
    width: 100%;
  }
}
.message_wrapper .msg-header .msg-header-right {
  max-width: 100%;
  padding: 0px 1.25rem;
}
.message_wrapper .input-group {
  border: solid 1px #e8eef3;
  border-radius: 4px;
}
.message_wrapper .input-group .input-group-text {
  background-color: transparent;
  padding: 0px 6px;
}
.message_wrapper .input-group input {
  line-height: 1.8;
}
.message_wrapper .msg-content-left {
  border: 1px solid #e8eef3;
  max-width: 392px;
  float: left;
  width: 40%;
}
@media (max-width: 991.98px) {
  .message_wrapper .msg-content-left {
    max-width: 100%;
    width: 100%;
    height: 100%;
  }
}
.message_wrapper .msg-content-left .card-img {
  border: 1px solid #e8eef3;
}
.message_wrapper .msg-content-left .card {
  border: 1px solid #e8eef3;
}
.message_wrapper .msg-content-left .card-title {
  line-height: 1;
}
.message_wrapper .msg-content-left .card-date {
  line-height: 1;
}
.message_wrapper .msg-content-left .card-body .card-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.62;
}
.message_wrapper .msg-content-left .tablinks.active, .message_wrapper .msg-content-left .card:hover {
  border-radius: 0px;
  background-color: #e8eef3;
  cursor: pointer;
}
.message_wrapper .msg-content-left .tablinks.active .card-title, .message_wrapper .msg-content-left .card:hover .card-title {
  color: #28313c;
}
.message_wrapper .msg-content-left .tablinks.active .card-date, .message_wrapper .msg-content-left .card:hover .card-date {
  color: #28313c;
}
.message_wrapper .msg-content-left .tablinks.active .card-text, .message_wrapper .msg-content-left .card:hover .card-text {
  color: #616e80;
}
.message_wrapper .msg-content-right {
  max-width: 100%;
  width: 100%;
  height: auto;
}
@media (max-width: 991.98px) {
  .message_wrapper .msg-content-right {
    margin-left: 0;
    display: none;
    position: fixed;
    top: 62px;
    left: 0;
    right: 0;
    height: 100%;
    background-color: #fff;
    margin: 0 9px;
  }
}
.message_wrapper .msg-content-right .mbl-sender-name {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #e8eef3;
}
.message_wrapper .msg-content-right .chat-box {
  height: calc(100vh - 330px);
}
@media (max-width: 991.98px) {
  .message_wrapper .msg-content-right .chat-box {
    height: calc(100vh - 273px);
    margin-bottom: 157px;
  }
}
.message_wrapper .msg-content-right .chat-box .card-img {
  border: 1px solid #e8eef3;
}
.message_wrapper .msg-content-right .chat-box .card:hover {
  background-color: #e8eef3;
}
.message_wrapper .msg-content-right .chat-box .card:hover p {
  color: #28313c;
}
.message_wrapper .msg-content-right .chat-box .card-text, .message_wrapper .msg-content-right .card-date {
  line-height: 1.6;
}
.message_wrapper .msg-content-right .custom-file-input:lang(en) ~ .custom-file-label::after {
  content: none;
  cursor: pointer;
}
.message_wrapper .msg-content-right .custom-file {
  width: 75%;
}
.message_wrapper .msg-content-right .custom-file label {
  padding-left: 0px;
}
.message_wrapper .msg-content-right .attach-send {
  padding: 11px 16px;
}

.scroll {
  position: relative;
  overflow: hidden;
}

.scroll.ps > .ps__rail-y {
  height: 0px !important;
  display: none !important;
}

@media (max-width: 991.98px) {
  .msg-content-right form {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0px 9px;
    background-color: #fff;
  }
}
.task-search input {
  text-overflow: ellipsis;
}

.select-box, .task-search {
  width: 26%;
}
@media (max-width: 991.98px) {
  .select-box, .task-search {
    width: auto;
  }
}

.w-task-board-box {
  overflow-x: hidden;
  position: relative;
}
.w-task-board-box:after {
  content: "";
  height: 1px;
  width: 100%;
  background-color: #e8eef3;
  position: absolute;
  bottom: 26px;
  left: 0;
  right: 0;
}
.w-task-board-box .w-task-board-panel {
  overflow-x: scroll;
}
.w-task-board-box .w-task-board-panel .board-panel {
  width: 340px;
  margin-bottom: 20px;
  flex: 0 0 340px;
  white-space: nowrap;
}
@media (max-width: 767.98px) {
  .w-task-board-box .w-task-board-panel .board-panel {
    width: 255px;
    flex: 0 0 255px;
  }
}
.w-task-board-box .w-task-board-panel .board-panel .b-p-header .bg-dark:hover {
  color: #fff;
}
.w-task-board-box .w-task-board-panel .board-panel .b-p-header .b-p-badge {
  line-height: 1.9;
  vertical-align: middle;
}
.w-task-board-box .w-task-board-panel .board-panel .b-p-body {
  height: calc(100vh - 213px);
  overflow-y: auto;
}
@media (max-width: 767.98px) {
  .w-task-board-box .w-task-board-panel .board-panel .b-p-body {
    height: calc(100vh - 276px);
  }
}
.w-task-board-box .w-task-board-panel .minimized {
  margin-bottom: 20px;
}
.w-task-board-box .w-task-board-panel .minimized .b-p-header {
  writing-mode: vertical-rl;
  text-orientation: sideways;
}

.w-task-board-panel::-webkit-scrollbar {
  width: 5px;
  background: #fff;
  height: 10px;
}

.w-task-board-panel::-webkit-scrollbar-thumb {
  border-radius: 7px;
  background-color: #e8eef3;
}

.gu-transit {
  background-color: #848586 !important;
  opacity: 0.1 !important;
  border: 2px dashed black;
}
.gu-transit .card-body {
  visibility: hidden;
}

.gu-mirror {
  box-shadow: none;
  opacity: 1 !important;
  height: 125px !important;
  transform: rotate(5deg);
  border: 2px dashed rgba(0, 0, 0, 0.397);
}

.full .filter-box {
  position: relative;
  top: 0px;
}

.settings-sidebar {
  height: calc(100% - 63px);
  width: 100%;
  max-height: 100%;
  max-width: 270px;
  position: fixed;
  white-space: nowrap;
}
@media (max-width: 991.98px) {
  .settings-sidebar {
    max-width: 0px;
    transition: 0.3s;
    position: fixed;
    right: 0;
    z-index: 99;
    top: 0px;
    height: 100vh;
  }
}
.settings-sidebar .settings-menu {
  height: calc(100vh - 150px);
  overflow: auto;
}
@media (max-width: 991.98px) {
  .settings-sidebar .settings-menu {
    height: calc(100vh - 80px);
  }
}
.settings-sidebar .settings-menu li a {
  padding: 15px 24px;
  white-space: break-spaces;
}
.settings-sidebar .settings-menu li a:hover {
  color: #28313c !important;
  background-color: rgba(232, 238, 243, 0.5215686275);
  border-right: 2px solid rgba(40, 49, 60, 0.5215686275);
}
.settings-sidebar li.active {
  background-color: #e8eef3;
}
.settings-sidebar li.active a {
  color: #28313c;
  border-right: 2px solid #28313c;
}

@media (max-width: 991.98px) {
  .settings-sidebar.in {
    max-width: 14rem;
    transition: 0.3s;
  }
  .settings-sidebar.in form a {
    margin-top: 9px;
    margin-left: 8px;
    color: #99a5b5;
  }
  .settings-sidebar.in .close-it {
    opacity: 1;
    transition: width 0.7s;
    left: -43px;
  }
}
.settings-box {
  width: 100%;
  margin-left: 270px;
}
@media (max-width: 1199.98px) {
  .settings-box {
    padding: 30px;
  }
}
@media (max-width: 991.98px) {
  .settings-box {
    margin-left: 0px;
    padding: 0px 25px 25px;
  }
}
.settings-box .s-b-mob-sidebar {
  position: absolute;
  right: 24px;
  top: 28px;
}
.settings-box .s-b-inner {
  height: 100%;
}
@media (max-width: 1199.98px) {
  .settings-box .s-b-inner .s-b-n-header .nav {
    flex-wrap: nowrap;
    overflow: auto;
    overflow-y: hidden;
    overflow-x: scroll;
  }
}
@media (max-width: 767.98px) {
  .settings-box .s-b-inner .s-b-n-header .nav {
    flex-wrap: nowrap;
    overflow: auto;
    overflow-y: hidden;
    overflow-x: scroll;
  }
}
.settings-box .s-b-inner .s-b-n-content .ntfcn-tab-content-right {
  margin-top: -58px;
  height: auto;
}
@media (max-width: 991.98px) {
  .settings-box .s-b-inner .s-b-n-content .ntfcn-tab-content-right {
    margin-top: 0;
  }
}
@media (max-width: 767.98px) {
  .settings-box .s-b-inner .s-b-n-content .ntfcn-tab-content-right label {
    width: 100%;
  }
}
.settings-box .s-b-inner .s-b-n-content .ntfcn-tab-content-right h4 {
  height: 56px;
}
@media (max-width: 991.98px) {
  .settings-box .s-b-inner .s-b-n-content .ntfcn-tab-content-right h4 {
    height: 40px;
  }
}
@media (max-width: 767.98px) {
  .settings-box .s-b-inner .s-b-n-content .s-save {
    width: 100%;
  }
  .settings-box .s-b-inner .s-b-n-content .s-send-test-mail {
    width: 57%;
  }
  .settings-box .s-b-inner .s-b-n-content .s-cancel {
    width: 35%;
  }
}
.settings-box .s-b-inner .s-b-n-content .settings-btns button {
  padding: 9px 14px;
}

.s-b-n-header .nav::-webkit-scrollbar {
  display: none;
}

.mobile-close-overlay {
  height: 100%;
  position: fixed;
  top: 0;
  right: 0px;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 99;
  display: none;
  transition: width 0.3s;
}

@media (max-width: 991.98px) {
  .mobile-close-overlay.in {
    display: block !important;
    transition: width 0.3s;
  }
  .close-it {
    position: absolute;
    top: 17px;
    background-color: #1D82F5;
    padding: 9px 17px !important;
    color: #fff;
    border-radius: 22px 0px 0px 22px;
    font-size: 12px;
    transition: width 0.7s;
    opacity: 0;
  }
}
.settings-btns {
  display: flex;
  flex-flow: row;
  justify-content: flex-end;
}
@media (max-width: 767.98px) {
  .settings-btns .btn-primary, .settings-btns .inv-action {
    width: 50%;
  }
  .settings-btns .btn-cancel {
    margin-right: 0px !important;
  }
  .settings-btns .inv-action button {
    width: 100%;
  }
  .settings-btns .btn-cancel {
    order: 3;
  }
}

.gdpr-tabs {
  text-align: center;
}
.gdpr-tabs li a {
  white-space: nowrap;
}
.gdpr-tabs .-more .-secondary li {
  text-align: left;
}

.permisison-table .thead-light {
  position: sticky;
  top: 63px;
  z-index: 1;
  overflow: auto;
}
@media (max-width: 767.98px) {
  .permisison-table .thead-light {
    z-index: 1;
  }
}

.role-permission-select {
  width: 100px;
}

.set-btns {
  position: sticky;
  background: #fff;
  bottom: 0;
  z-index: 1;
}
@media (max-width: 991.98px) {
  .set-btns {
    position: relative;
  }
}

.invoice .inv-num-date td {
  padding: 6px;
  border: 1px solid #DBDBDB;
}
.invoice .inv-detail .i-d-heading, .invoice .inv-desc-mob .i-d-heading {
  border: 1px solid #DBDBDB;
}
.invoice .inv-detail .i-d-heading td, .invoice .inv-desc-mob .i-d-heading td {
  border: 1px solid #DBDBDB;
}
.invoice .inv-detail td, .invoice .inv-detail th, .invoice .inv-desc-mob td, .invoice .inv-desc-mob th {
  padding: 11px 10px;
  border: 1px solid #e7e9eb;
  word-break: break-word;
}
.invoice .card-footer button {
  padding: 0px 30px;
}
@media (max-width: 767.98px) {
  .invoice .card-footer button {
    padding: 9px 22px;
  }
}
.invoice .card-footer .inv-action .dropdown-toggle {
  font-size: 15px;
  color: #616e80;
  border: solid 1px #616e80;
  padding: 0px 9px;
  border-radius: 4px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 0;
}
.invoice .card-footer .inv-action .dropdown-toggle:hover {
  background-color: black;
  color: #fff !important;
}
.invoice .card-footer .inv-action .dropdown-toggle:hover span .fa-chevron-down {
  color: #fff !important;
}
.invoice .card-footer .inv-action .dropdown-toggle span {
  border-left: 1px solid #99a5b5;
  height: 34px;
  display: inline-flex;
  padding-left: 8px;
  align-items: center;
  margin-left: 8px;
}
@media (max-width: 767.98px) {
  .invoice .card-footer .inv-action .dropdown-toggle span {
    margin-left: 30px;
  }
}

.unpaid {
  text-transform: uppercase;
  background-color: #fff;
  color: #D30000;
  border: 1px solid #D30000;
  position: relative;
  padding: 11px 22px;
}

.inv-desc, .inv-note {
  width: 100%;
}

.inv-note td {
  width: 50%;
  word-break: break-word;
}

.inv-unpaid td:nth-child(2) {
  text-align: right;
}
@media (max-width: 767.98px) {
  .inv-unpaid td:nth-child(2) {
    text-align: left;
  }
}

@media (max-width: 767.98px) {
  .inv-logo-heading img {
    width: auto;
  }
  .inv-logo-heading td {
    width: 100%;
    display: block;
    margin: 0 auto;
    text-align: center;
  }
  .inv-num-date {
    width: 100%;
  }
  .inv-num-date td {
    display: table-cell !important;
    text-align: left !important;
  }
  .inv-num td {
    display: block;
    margin: 0 auto;
    text-align: center;
  }
  .blank-td {
    display: none;
  }
  .inv-note td, .inv-unpaid td {
    width: 100%;
    display: block;
  }
  .inv-detail {
    margin-bottom: 5px;
  }
  .inv-desc::-webkit-scrollbar {
    width: 5px;
    background: #fff;
    height: 10px;
  }
  .inv-desc::-webkit-scrollbar-thumb {
    border-radius: 7px;
    background-color: #e8eef3;
  }
}
.invoice .card-footer {
  display: flex;
  flex-flow: row;
  justify-content: flex-end;
}
@media (max-width: 767.98px) {
  .invoice .card-footer {
    flex-flow: column;
  }
  .invoice .card-footer .btn-primary, .invoice .card-footer .inv-action {
    width: 50%;
  }
  .invoice .card-footer .btn-primary {
    margin-right: 0px !important;
  }
  .invoice .card-footer .inv-action button {
    width: 100%;
  }
  .invoice .card-footer .btn-cancel {
    order: 3;
  }
}

.c-inv-desc table tr td {
  border: 1px solid #e7e9eb;
  padding: 11px 10px;
}

.c-inv-desc-table .item_name, .c-inv-desc-table .quantity, .c-inv-desc-table .cost_per_item, .hsn_sac_code {
  padding: 0.5rem !important;
  border: 1px solid #e7e9eb !important;
  border-radius: 0.25rem !important;
}

@media (max-width: 767.98px) {
  .c-inv-desc-table table tr {
    flex-direction: column;
    display: block;
    width: 50%;
    float: left;
  }
  .c-inv-desc-table table tr .inv-desc-mbl {
    height: 140px;
  }
  .c-inv-desc-table table td {
    display: block;
    flex: 1 1 auto;
    border: 1px solid #e7e9eb !important;
    width: 100%;
    height: 70px;
  }
  .c-inv-desc-table a {
    justify-content: flex-end !important;
    margin-top: 10px;
  }
  .c-inv-desc-table .select-others {
    line-height: 0;
    font-weight: 400;
  }
  .c-inv-desc-table input.quantity {
    margin-top: 0 !important;
  }
}
.c-inv-amt p {
  height: 70px;
  display: block;
  line-height: 2.7;
  text-align: right;
}

.c-inv-close {
  height: 96px;
}

.c-inv-total table tr td {
  border: 1px solid #e8eef3;
  padding: 11px 10px;
}
.c-inv-total table tr td .c-inv-sub-padding {
  padding: 5px 10px;
}
@media (max-width: 767.98px) {
  .c-inv-total table tr td .c-inv-sub-padding {
    padding: 5px 0px;
  }
}

.c-inv-note-terms textarea {
  width: 96%;
}
@media (max-width: 767.98px) {
  .c-inv-note-terms textarea {
    width: 100%;
  }
}

.c-inv-btns .dropdown-toggle {
  font-size: 15px;
  color: #fff;
  border: solid 1px #616e80;
  padding: 0px 9px;
  border-radius: 4px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 0;
}
.c-inv-btns .dropdown-toggle span {
  border-left: 1px solid #fff;
  height: 34px;
  display: inline-flex;
  padding-left: 8px;
  align-items: center;
  margin-left: 8px;
}
@media (max-width: 767.98px) {
  .c-inv-btns .dropdown-toggle span {
    margin-left: 28px;
  }
}

.dark_place input::-moz-placeholder, .c-inv-date-pickerinput::-moz-placeholder {
  color: #28313c !important;
  font-weight: normal important;
}

.dark_place input::placeholder, .c-inv-date-pickerinput::placeholder {
  color: #28313c !important;
  font-weight: normal important;
}

.dash-border-top {
  border-top: 1px dashed #c3c3c3 !important;
}

.c-inv-btns {
  display: flex;
  flex-flow: row;
  justify-content: flex-end;
}
@media (max-width: 767.98px) {
  .c-inv-btns .btn-secondary, .c-inv-btns .inv-action {
    width: auto;
  }
  .c-inv-btns .inv-action button {
    width: 100%;
  }
  .c-inv-btns .btn-cancel {
    order: 3;
  }
}

.btlr {
  border-top-left-radius: 4px;
}

.btrr-bbrr {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
@media (max-width: 767.98px) {
  .btrr-bbrr {
    border-top-right-radius: 0px;
  }
}

.bblr {
  border-bottom-left-radius: 4px;
}

@media (max-width: 767.98px) {
  .btrr-mbl {
    border-top-right-radius: 4px;
    border-top-left-radius: 0px;
  }
  .bblr-mbl {
    border-bottom-left-radius: 4px;
  }
}
table {
  border-collapse: separate !important;
  border-spacing: inherit;
}

.ticket-wrapper {
  margin: 0px 9px;
  overflow: hidden;
  border: 1px solid #e8eef3;
}
.ticket-wrapper .ticket-left .ticket-msg {
  height: calc(100vh - 260px);
  overflow-y: auto;
}
@media (max-width: 991.98px) {
  .ticket-wrapper .ticket-left .ticket-msg {
    height: calc(100vh - 328px);
  }
}
.ticket-wrapper .ticket-left .ticket-msg .ticket-card-img {
  margin: 1.3rem 1rem;
  width: 35px;
  height: 35px;
  -o-object-fit: contain;
  object-fit: contain;
  border-radius: 4px;
  overflow: hidden;
  border: solid 1px #616e80;
}
.ticket-wrapper .ticket-left .ticket-reply-back button {
  padding: 9px 11px;
}
.ticket-wrapper .ticket-right {
  max-width: 393px;
  width: 393px;
}
@media (max-width: 991.98px) {
  .ticket-wrapper .ticket-right {
    max-width: 0px;
    transition: 0.3s;
    position: fixed;
    right: 0;
    z-index: 99;
    top: 0px;
    height: 100vh;
    white-space: nowrap;
  }
}
.ticket-wrapper .ticket-right .ticket-filters {
  height: calc(100vh - 236px);
  overflow-y: auto;
}
@media (max-width: 991.98px) {
  .ticket-wrapper .ticket-right .ticket-filters {
    height: calc(100vh - 125px);
  }
}
.ticket-wrapper .ticket-right .nav-item {
  padding: 14px 28px;
}
.ticket-wrapper .ticket-right .ticket-update button {
  padding: 9px 11px;
}
.ticket-wrapper .ticket-right .ticket-contact-owner .card-img {
  width: 42px;
  height: 42px;
  margin: 0;
}
.ticket-wrapper .ticket-right .ticket-contact-owner .card-img img {
  width: 42px;
  height: 42px;
}
.ticket-wrapper .ticket-right .recent-ticket {
  height: calc(100vh - 451px);
  overflow-y: auto;
}
@media (max-width: 991.98px) {
  .ticket-wrapper .ticket-right .recent-ticket {
    height: calc(100vh - 333px);
  }
}
.ticket-wrapper .ticket-right .recent-ticket .recent-ticket-inner:before {
  position: absolute;
  left: 10.9px;
  content: "";
  width: 1px;
  height: 100%;
  background-color: #99A5B5;
  top: 4px;
}
.ticket-wrapper .ticket-right .recent-ticket .r-t-items {
  margin-bottom: 20px;
}
.ticket-wrapper .ticket-right .recent-ticket .r-t-items .r-t-items-right {
  padding-left: 10px;
}
.ticket-wrapper .ticket-right .recent-ticket .r-t-items:last-child {
  margin-bottom: 0px;
}
.ticket-wrapper .ticket-right .recent-ticket .r-t-items:last-child p {
  margin-bottom: 0px;
}
@media (max-width: 991.98px) {
  .ticket-wrapper .ticket-right.in {
    max-width: 14rem;
    transition: 0.3s;
    white-space: normal;
  }
  .ticket-wrapper .ticket-right.in .close-it {
    opacity: 1;
    transition: width 0.7s;
  }
  .ticket-wrapper .ticket-reply-back {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 97%;
    margin: 0 auto;
  }
  .ticket-wrapper .ticket-update {
    position: fixed;
    bottom: 0;
    width: 100%;
  }
}
@media (max-width: 767.98px) {
  .ticket-wrapper .ticket-reply-back {
    width: 95%;
  }
}

@media (max-width: 767.98px) {
  .ticket-wrapper .ticket-right.in ~ .main-container {
    z-index: auto;
  }
}
.project-header a {
  font-size: 14px;
}
@media (max-width: 1199.98px) {
  .project-header a {
    padding: 10px 8px;
    font-size: 11px;
  }
}
@media (max-width: 991.98px) {
  .project-header a {
    padding: 13px 15px;
    font-size: 14px;
  }
}
.project-header a:hover {
  color: #616E80 !important;
}
.project-header .project-menu .p-sub-menu {
  position: relative;
  font-size: 14px;
  display: block;
}
@media (max-width: 991.98px) {
  .project-header .project-menu .p-sub-menu {
    padding: 0px !important;
  }
}
.project-header .project-menu .p-sub-menu.active:after, .project-header .project-menu .p-sub-menu.active:hover:after {
  width: 100%;
  height: 3px;
  background: #1d82f5;
  transition: width 0.3s, height 0.3s;
  bottom: 0;
  position: absolute;
  content: "";
}
.project-header .project-menu .p-sub-menu span {
  padding: 13px 26px;
  display: block;
  white-space: nowrap;
  text-align: center;
}
@media (max-width: 991.98px) {
  .project-header .project-menu .p-sub-menu span {
    text-align: left;
  }
}
.project-header .project-menu .p-sub-menu::after {
  content: "";
  width: 0;
  height: 0;
  background: rgba(29, 130, 245, 0.5215686275);
  transition: width 0.3s, height 0.3s;
  bottom: 0;
  position: absolute;
}
.project-header .project-menu .p-sub-menu:hover::after {
  width: 100%;
  transition: width 0.3s, height 0.3s;
  height: 3px;
}
@media (max-width: 991.98px) {
  .project-header .project-menu {
    width: 100%;
    max-width: 0px;
    transition: 0.3s;
    position: fixed;
    right: 0;
    z-index: 99;
    top: 0px;
    height: 100vh;
    white-space: nowrap;
    background-color: #fff;
  }
}

@media (max-width: 991.98px) {
  .project-menu.in {
    max-width: 14rem;
    transition: 0.3s;
    white-space: nowrap;
  }
  .project-menu.in a {
    display: block !important;
  }
  .project-menu.in .close-it {
    opacity: 1;
    transition: width 0.7s;
    left: -43px;
  }
}
.project-left {
  margin-right: 270px;
}
.project-left .p-client-msg button:hover {
  background-color: #00E073 !important;
  border: 1px solid #00E073 !important;
}

.project-right {
  max-width: 270px;
  width: 270px;
  height: calc(100vh - 107px);
  position: fixed;
  right: 0;
}
@media (max-width: 991.98px) {
  .project-right {
    position: relative;
    width: 100%;
    max-width: 100%;
    height: auto;
  }
  .project-right .bg-white {
    border-radius: 4px;
  }
}
.project-right .p-activity-heading span {
  width: 35px;
  height: 35px;
  background-color: #F2F4F7;
}
.project-right .p-activity-heading span:hover {
  background-color: #e8eef3;
}
.project-right .p-activity-detail.cal-info {
  height: calc(100vh - 175px);
  overflow-y: auto !important;
}
@media (max-width: 991.98px) {
  .project-right .p-activity-detail.cal-info {
    height: auto !important;
  }
}
@media (max-width: 767.98px) {
  .project-right .p-activity-detail.cal-info {
    height: calc(100vh - 175px) !important;
  }
}

.cal-info .card-header {
  height: 45px;
  width: 37px;
  border: 1px solid #616E80 !important;
}
.cal-info .card-header span {
  display: block;
  text-align: center;
  line-height: 17px;
}
.cal-info .card-header span:nth-child(1) {
  border-bottom: 1px solid #616E80 !important;
}
@media (max-width: 991.98px) {
  .cal-info .card {
    width: 50%;
    float: left;
  }
}
@media (max-width: 767.98px) {
  .cal-info .card {
    width: 100%;
    float: none;
  }
}

@media (max-width: 991.98px) {
  .project-header {
    z-index: 0;
    position: initial;
  }
}
.more-projects {
  padding: 13px 15px;
  display: block;
}
.more-projects a {
  border-bottom: 1px solid #e8eef3;
}

button {
  cursor: pointer;
  border: 0;
  padding: 0;
}

.tabs {
  position: relative;
}
.tabs:not(.--jsfied) {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.tabs .--hidden {
  display: none;
}
.tabs .-primary {
  display: flex;
}
.tabs .-primary > li {
  flex-grow: 1;
}
.tabs .-primary .-more > button span {
  display: inline-block;
  transition: transform 0.2s;
}
.tabs.--show-secondary .-primary .-more > button span {
  transform: rotate(180deg);
}
.tabs .-secondary {
  max-width: 100%;
  min-width: 10em;
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  box-shadow: 0 0.1em 0.4em rgba(0, 0, 0, 0.12);
  animation: nav-secondary 0.2s;
  background-color: #fff;
}
.tabs .-secondary.in {
  display: block;
  width: 100%;
  height: 100%;
}
.tabs .-secondary.in li {
  border-bottom: 1px solid #e8eef3;
}
.tabs.--show-secondary .-secondary {
  display: block;
  z-index: 9;
}
.tabs.--show-secondary .-secondary li {
  border-bottom: 1px solid #e8eef3;
}
.tabs.--show-secondary .-secondary li span {
  text-align: left;
}

@keyframes nav-secondary {
  0% {
    opacity: 0;
    transform: translateY(-1em);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
/*********** CLIENT DETAIL **************/
.client-detail-chart {
  height: 159px;
}

/*********** CLIENT LIST **************/
.client-list-filter .select-box {
  width: auto;
}
.client-list-filter .select-box .input-group {
  width: auto;
}
@media (max-width: 767.98px) {
  .client-list-filter {
    position: initial;
  }
}

.client-list-filter.filter-box {
  padding: 0px 28px;
}

.emp-dashboard .clock-in-out p span {
  line-height: 1.5;
}
.emp-dashboard .e-d-info .card-img {
  width: 80px;
  height: 80px;
}
.emp-dashboard .e-d-info .card-img img {
  width: 80px;
  height: 80px;
  -o-object-fit: cover;
     object-fit: cover;
}
.emp-dashboard .cal-info .card-text {
  line-height: 1.2;
}
@media (max-width: 1199.98px) {
  .emp-dashboard .cal-info {
    height: calc(100vh - 850px) !important;
    overflow-y: scroll;
  }
}
@media (max-width: 767.98px) {
  .emp-dashboard .cal-info {
    height: calc(100vh - 350px) !important;
    overflow-y: scroll;
  }
}

.admin-dash-settings a:hover {
  border-bottom: none;
}

@media (max-width: 767.98px) {
  .admin-dash-table {
    width: 600px;
  }
}

.modal.show {
  background-color: rgba(0, 0, 0, 0.25);
  padding-right: 0px !important;
}

.modal-open {
  padding-right: 0px !important;
}

.modal-header .close {
  padding: 0;
  margin: 0;
}

.banner {
  background-image: url(https://recruit.froid.works/front/assets/img/header-bg.jpg);
  background-repeat: no-repeat;
  background-position: center;
  height: 200px;
}
.banner .banner-logo {
  position: absolute;
  background-color: white;
  width: 130px;
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: -49px;
}

.job-container {
  background-color: #fff;
  display: flex;
  flex-wrap: nowrap;
}
.job-container .job-left {
  border-right: 1px solid #e8eef3;
  width: 504px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  overflow-y: auto;
}
.job-container .job-right {
  width: 100%;
  height: calc(100vh - 150px);
  overflow-y: auto;
}
.list-style-disc {
  list-style-type: disc;
}

.front_header {
  box-shadow: 0 1px 0 0 #e8eef3;
}

.front_header img {
  max-height: 32px;
}

.front_header h3 {
  font-size: 21px;
  color: #28313c;
  font-weight: normal;
}

@media (max-width: 575.98px) {
  .header-banner-logo {
    width: 85px !important;
    height: 85px !important;
    bottom: -35px !important;
  }
  .banner-header {
    height: 110px;
  }
}
.rtl {
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
}
.rtl .main-sidebar {
  left: auto;
  right: 0;
}
.rtl .main-container {
  margin-right: 240px;
  margin-left: 0px;
  transition: width 0.3s ease-in-out, margin-right 0.3s ease-in-out;
}
.rtl .main-header {
  margin-right: 240px;
  margin-left: 0px;
  display: flex;
}
.rtl .pull-left {
  float: right !important;
}
.rtl .pull-right {
  float: left !important;
}
.rtl .page-header-right ul li {
  float: right;
}
.rtl .header-icon-box {
  margin-right: 9px;
  margin-left: 14px;
}
.rtl .form-check-inline, .rtl .form-check-inline .form-check-input {
  margin-right: 0;
  margin-left: 0.3125rem;
}
.rtl .accordionItem.closeIt .accordionItemHeading::after {
  right: 213px;
  content: "\e605";
}
.rtl .accordionItem.openIt .accordionItemHeading::after {
  right: 213px;
}
.rtl .sidebar-menu li .accordionItemContent a {
  margin-right: 22px;
}
.rtl #sidebarToggle::after {
  content: "\e606";
}
.rtl .sidebar-brand-box .sidebar-brand .sidebar-brand-name p span {
  left: 0;
  right: -20px;
}
.rtl .sidebar-brand-box .sidebar-brand .sidebar-brand-name p {
  margin-left: 0;
  margin-right: 20px;
}
.rtl .mr-5 {
  margin-left: 3rem !important;
  margin-right: 0 !important;
}
.rtl .mr-3 {
  margin-left: 1rem !important;
  margin-right: 0 !important;
}
.rtl .mr-2 {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}
.rtl .mr-1 {
  margin-left: 0.25rem !important;
  margin-right: 0 !important;
}
.rtl .pl-2 {
  padding-right: 0.5rem !important;
  padding-left: 0 !important;
}
.rtl .pl-3 {
  padding-right: 1rem !important;
  padding-left: 0 !important;
}
.rtl .ml-3 {
  margin-right: 1rem !important;
  margin-left: 0 !important;
}
.rtl #table-actions {
  display: flex;
}
.rtl .btn-group {
  flex-direction: row-reverse !important;
}
.rtl .bootstrap-select .dropdown-menu {
  left: auto !important;
}
.rtl .bootstrap-select .dropdown-toggle .filter-option-inner-inner {
  text-align: right !important;
}
.rtl .dropdown-menu-right {
  right: auto;
  left: 0;
}
.rtl .sidebar-brand-box .sidebar-brand-dropdown {
  right: 20px !important;
}
.rtl .sidebar-brand-box .sidebar-brand-dropdown .profile-box .ProfileData p {
  text-align: right;
}
.rtl .ml-auto {
  margin-right: auto !important;
  margin-left: 0px !important;
}
.rtl .border-right-grey {
  border-left: 1px solid #e8eef3;
  border-right: 0px;
}
.rtl .border-left-grey {
  border-right: 1px solid #e8eef3;
  border-left: 0px;
}
.rtl .pl-4 {
  padding-right: 1.5rem !important;
  padding-left: 0px;
}
.rtl .task_view .taskView {
  border-left: 1px solid #99A5B5;
  border-right: 0px;
}
.rtl .pr-3 {
  padding-right: 0px !important;
}
.rtl .select-box.pr-2 {
  padding-right: 0px !important;
  padding-left: 0.5rem;
}
.rtl .choices[data-type*=select-one]:after {
  left: 11.5px;
  right: auto;
}
.rtl .text-left {
  text-align: right !important;
}
.rtl .choices__item.choices__item--selectable {
  padding-left: 0px;
  padding-right: 6px;
}
.rtl .c-inv-btns .dropdown-toggle span, .rtl .invoice .card-footer .inv-action .dropdown-toggle span {
  border-left: 0px;
  padding-left: 0;
  padding-right: 8px;
  margin-left: auto;
  margin-right: 8px;
}
.rtl .c-inv-btns .dropdown-toggle span {
  border-right: 1px solid #fff;
}
.rtl .invoice .card-footer .inv-action .dropdown-toggle span {
  border-right: 1px solid #99a5b5;
}
.rtl .mr-lg-3 {
  margin-left: 1rem !important;
}
.rtl .emp-dashboard .mr-4 {
  margin-left: 1.5rem !important;
  margin-right: 0px !important;
}
.rtl .inv-unpaid td:nth-child(2) {
  text-align: left;
}
@media (max-width: 767.98px) {
  .rtl .inv-unpaid td:nth-child(2) {
    text-align: right;
  }
}
.rtl .form-group label sup {
  left: -4px;
  right: auto;
}
.rtl .c-inv-select .mr-2 {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}
.rtl .inv-num td:nth-child(2) {
  direction: ltr;
  text-align: right;
}
.rtl .text-right, .rtl .inv-logo-heading td:nth-child(2) {
  text-align: left;
}
.rtl .msg-header .ml-3 {
  margin-right: 1rem;
  margin-left: 0px !important;
}
.rtl .msg-content-left {
  float: right;
}
.rtl .msg-content-left .pl-0 {
  padding-left: 15px !important;
  padding-right: 0px !important;
}
.rtl .msg-content-right {
  margin-left: auto;
  margin-right: 392px;
}
@media (max-width: 991.98px) {
  .rtl .msg-content-right {
    margin-right: 0px;
  }
}
.rtl .msg-content-right .pl-0 {
  padding-left: 15px !important;
  padding-right: 0px !important;
}
.rtl .msg-content-right .mr-1 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}
.rtl .msg-content-right .mr-2 {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}
.rtl .msg-content-right .custom-file label {
  padding-right: 0px;
}
.rtl .msg-content-right .msg-sender-name svg {
  transform: rotate(180deg);
}
.rtl .project-left {
  margin-left: 270px;
  margin-right: 0px;
}
.rtl .project-left .ml-xl-4 {
  margin-left: 0px !important;
  margin-right: 1.5rem !important;
}
.rtl .project-left .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}
.rtl .project-right {
  left: 0;
  right: auto;
}
.rtl .project-right .ml-3 {
  margin-left: 0 !important;
  margin-right: 1rem !important;
}
.rtl .settings-box {
  margin-right: 270px;
  margin-left: 0px;
}
@media (max-width: 991.98px) {
  .rtl .settings-box {
    margin-right: 0px;
  }
}
.rtl .settings-box .mr-1 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}
.rtl .settings-box .s-b-mob-sidebar {
  left: 24px;
  right: auto;
}
.rtl .settings-box .checkmark {
  right: 0px;
  left: auto;
}
.rtl .settings-box .form_custom_label {
  padding-right: 25px;
  padding-left: 0px;
}
.rtl .settings-sidebar.in .close-it {
  left: auto;
  right: -43px;
  border-radius: 0px 22px 22px 0px;
}
.rtl .filter-box .mr-2 {
  margin-left: 0.5rem !important;
}
.rtl .filter-box .mr-lg-2 {
  margin-left: 0.5rem !important;
  margin-right: 0rem !important;
}
.rtl #fullscreen button:nth-child(3) {
  padding-right: 1.5rem !important;
}
@media (max-width: 991.98px) {
  .rtl #fullscreen button:nth-child(3) {
    padding-right: 0.5rem !important;
  }
}
.rtl .b-p-header .mr-2 {
  margin-right: auto !important;
  margin-left: 0.5rem !important;
}
.rtl .b-p-tasks .ml-1 {
  margin-left: 0 !important;
  margin-right: 0.25rem !important;
}
.rtl .b-p-tasks .ml-2 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}
.rtl .b-p-tasks .mr-1 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}
.rtl .table .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}
.rtl .table .mr-1 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}
.rtl .more-filters {
  padding-left: 0px !important;
  border-left: 0px;
  border-right: solid 1px #e8eef3;
  padding-right: 1rem;
}
.rtl .more-filters .pr-2 {
  padding-right: 0 !important;
  padding-left: 0.5rem !important;
}
.rtl .more-filters .more-filter-tab {
  left: 0px;
  right: auto;
  transform: translate3d(-273px, 0, 0);
}
.rtl .more-filters .more-filter-tab.in {
  transform: translate3d(0px, 0, 0);
}
.rtl .more-filters .close-more-filter {
  left: 28px;
  right: auto;
}
.rtl .ticket-wrapper .ticket-msg .card-body {
  padding-right: 0;
  padding-left: 15px !important;
}
.rtl .ticket-wrapper .ticket-left .mr-1 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}
.rtl .ticket-wrapper .ticket-right .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}
.rtl .ticket-wrapper .ticket-right .recent-ticket-inner:before {
  right: 10.9px;
  left: 0px;
}
.rtl .ticket-wrapper .ticket-right .r-t-items-right {
  padding-left: 0;
  padding-right: 10px;
}
.rtl .ticket-wrapper .ticket-right .r-t-items-right .mr-1 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}
.rtl .profileImg.mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}
.rtl .client-detail-wrapper .pl-xl-0 {
  padding-left: 15px !important;
}
.rtl .client-detail-wrapper .pl-0 {
  padding-right: 0px !important;
}
.rtl .client-detail-wrapper .pr-xl-0 {
  padding-right: 15px !important;
}
.rtl .client-list-wrapper .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}
.rtl .input-group > .input-group-prepend > .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.rtl .e-d-info-notices .ml-3 {
  margin-right: 1rem !important;
  margin-left: 0 !important;
}
.rtl .e-d-info-notices .pl-0 {
  padding-left: 15px !important;
  padding-right: 0 !important;
}
.rtl .b-p-header .mb-3 {
  margin-top: 1rem !important;
  margin-bottom: 0px !important;
}
.rtl .b-p-header .mb-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0 !important;
}
.rtl .task-list-wrap .mr-2 {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}
.rtl .task-detail-panel {
  right: auto;
  left: -320px;
}
.rtl .task-detail-panel.in {
  right: auto;
  left: 0px;
}
.rtl .task-overlay {
  left: 0px;
  right: auto;
}
.rtl .close-task-detail {
  right: -55px;
  left: auto;
  border-radius: 0px 22px 22px 0px;
}
@media (max-width: 767.98px) {
  .rtl .close-task-detail {
    left: 25px;
    right: auto;
    top: 28px;
    background-color: transparent;
    padding: 0px !important;
    color: #111;
    border-radius: 0;
  }
}
@media (max-width: 991.98px) {
  .rtl .mobile-close-sidebar-panel.toggled {
    left: auto;
    right: 0px;
  }
  .rtl .main-container {
    margin-right: 0;
  }
  .rtl .main-header {
    margin-right: 0px;
  }
  .rtl .menu-collapse {
    margin-right: 24px;
  }
  .rtl .main-sidebar.toggled {
    transform: translate3d(0px, 0, 0);
  }
  .rtl .main-sidebar {
    transform: translate3d(293px, 0, 0);
  }
  .rtl .sidebarTogglerBox {
    display: none !important;
  }
  .rtl .project-header .project-menu {
    right: auto;
    left: 0;
  }
  .rtl .project-menu.in .close-it {
    left: auto;
    right: -44px;
    border-radius: 0px 22px 22px 0px;
  }
  .rtl .settings-sidebar {
    left: 0;
    right: auto;
  }
  .rtl .filter-box a:nth-child(5) {
    padding-left: 0px !important;
  }
  .rtl .ticket-wrapper .ticket-right {
    left: 0;
    right: auto;
  }
}
@media (max-width: 767.98px) {
  .rtl .inv-logo-heading td:nth-child(2) {
    text-align: center;
  }
  .rtl .task-search .mr-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
  }
}

.rtl.sidebar-toggled #sidebarToggle::after {
  content: "\e605";
}

.rtl.sidebar-toggled .main-container {
  margin-right: 60px;
  transition: width 0.3s ease-in-out;
  margin-left: 0px;
}

.rtl.sidebar-toggled .main-header {
  margin-right: 60px;
  margin-left: 0px;
}

body.dark-theme {
  color: #D5D8DF !important;
  background-color: #181c34 !important;
}
body.dark-theme .border-0, body.dark-theme .settings-sidebar input, body.dark-theme .form-control.border-0, body.dark-theme .bootstrap-select > .dropdown-toggle {
  border: 0px !important;
}
body.dark-theme .bg-dark, body.dark-theme .qs-controls, body.dark-theme .dropdown-item:hover, body.dark-theme .dropdown-item:focus, body.dark-theme .task_view .task_view_more:hover, body.dark-theme .task_view .taskView:hover, body.dark-theme .project-menu.in, body.dark-theme .bg-amt-grey, body.dark-theme .e-d-tasks-projects-events .bg-grey, body.dark-theme .cal-info .bg-light-grey, body.dark-theme .inv-num-date .bg-light-grey, body.dark-theme .inv-detail .bg-light-grey, body.dark-theme .message_wrapper .msg-content-left .tablinks.active, body.dark-theme .message_wrapper .msg-content-left .card:hover, body.dark-theme .project-right .p-activity-heading span, body.dark-theme .settings-sidebar li.active, body.dark-theme .board-panel, body.dark-theme .w-task-board-panel .minimized, body.dark-theme .task-search .input-group-text, body.dark-theme .ticket-msg .card-horizontal.bg-white-shade, body.dark-theme .bg-white-shade.ticket-contact-owner, body.dark-theme .dark_place .input-group-text, body.dark-theme .login_section, body.dark-theme .daterangepicker .ranges li:hover, body.dark-theme .daterangepicker td.off.in-range, body.dark-theme .daterangepicker td.off.start-date, body.dark-theme .table .thead-light th, body.dark-theme .badge-light, body.dark-theme .datepicker .datepicker-switch:hover, body.dark-theme .datepicker .next:hover, body.dark-theme .datepicker .prev:hover, body.dark-theme .datepicker tfoot tr th:hover, body.dark-theme .datepicker table tr td.day:hover, body.dark-theme .datepicker table tr td.focused, body.dark-theme .tagify__tag > div, body.dark-theme .tagify__tag__removeBtn, body.dark-theme .fc-button.fc-button-primary.fc-button-active, body.dark-theme .fc-day-today, body.dark-theme .in-range.available, body.dark-theme .daterangepicker td.available:hover, body.dark-theme .daterangepicker th.available:hover, body.dark-theme .dropify-wrapper .dropify-preview, body.dark-theme .dropify-wrapper, body.dark-theme .bg-white-shade, body.dark-theme .input-group-text, body.dark-theme .progress, body.dark-theme .swal2-popup, body.dark-theme .main-sidebar, body.dark-theme .create-inv .bg-light-gre, body.dark-theme .unmatched .sample, body.dark-theme .fc .fc-cell-shaded, body.dark-theme .fc .fc-list-event:hover td, body.dark-theme .fc .fc-event:hover td {
  background-color: #29304C !important;
}
body.dark-theme .bg-white, body.dark-theme .card, body.dark-theme .form-control, body.dark-theme .qs-datepicker-container, body.dark-theme .qs-overlay, body.dark-theme .unpaid, body.dark-theme .custom-file-label, body.dark-theme .p-client-msg .btn-secondary, body.dark-theme .w-task-board-panel, body.dark-theme .b-p-header .b-p-badge, body.dark-theme .content-wrapper, body.dark-theme .admin-dashboard, body.dark-theme .page-title, body.dark-theme .main-container, body.dark-theme .settings-box, body.dark-theme .input-group .bootstrap-select.form-control .dropdown-toggle, body.dark-theme .bootstrap-select > .dropdown-toggle, body.dark-theme .bootstrap-timepicker-widget table td a:hover, body.dark-theme .btn-cancel, body.dark-theme .select-picker.role-permission-select, body.dark-theme .fc .fc-button-primary, body.dark-theme .daterangepicker td.off, body.dark-theme .dropzone, body.dark-theme textarea, body.dark-theme iframe .form-body, body.dark-theme .tabs .-secondary, body.dark-theme .set-btns, body.dark-theme .list-group-item, body.dark-theme .bg-light, body.dark-theme .bg-grey, body.dark-theme .dropzone .dz-preview.dz-image-preview, body.dark-theme .c-inv-desc select, body.dark-theme .bg-light-grey, body.dark-theme .matched .sample {
  background-color: #181C34 !important;
}
body.dark-theme .text-dark, body.dark-theme .text-dark-grey, body.dark-theme .text-darkest-grey, body.dark-theme .table tr td, body.dark-theme .sidebar-brand-box .sidebar-brand-dropdown .profile-box a, body.dark-theme .qs-datepicker-container, body.dark-theme .qs-day, body.dark-theme .table tr td a, body.dark-theme .tabs .nav .nav-item, body.dark-theme .login_box h3, body.dark-theme .login_header h3, body.dark-theme .form-control, body.dark-theme .forgot_pswd a, body.dark-theme .login_box .form-group label, body.dark-theme .page-item.disabled .page-link, body.dark-theme .pagination li a, body.dark-theme .dropdown-item, body.dark-theme a:hover, body.dark-theme .filter-option-inner-inner, body.dark-theme .badge-light, body.dark-theme .bootstrap-timepicker-widget table td a, body.dark-theme .ql-snow .ql-picker, body.dark-theme .ql-snow .ql-stroke, body.dark-theme .form_custom_label, body.dark-theme .dropdown-menu.datepicker-dropdown, body.dark-theme .select-picker.role-permission-select, body.dark-theme .tagify__tag > div > *, body.dark-theme .tagify__tag__removeBtn, body.dark-theme .dropdown-menu.dashboard-settings, body.dark-theme .daterangepicker td.in-range, body.dark-theme .text-muted, body.dark-theme .input-group-text, body.dark-theme .bootstrap-select .dropdown-toggle::after, body.dark-theme .btn-primary:hover .text-white, body.dark-theme .fc .fc-col-header-cell-cushion, body.dark-theme .swal2-title, body.dark-theme .swal2-content, body.dark-theme .login_box a, body.dark-theme .bg-grey, body.dark-theme .bg-grey span, body.dark-theme .desktop-description, body.dark-theme .item_name, body.dark-theme .quantity, body.dark-theme .cost_per_item, body.dark-theme .ticket-message:hover p {
  color: #D5D8DF !important;
}
body.dark-theme .chart-container .axis, body.dark-theme .chart-container .chart-label, body.dark-theme .data-point-value {
  fill: #D5D8DF !important;
}
body.dark-theme .ql-snow .ql-stroke {
  stroke: #D5D8DF;
}
body.dark-theme .filter-box, body.dark-theme .table tr td, body.dark-theme .login_header {
  box-shadow: 0 1px 0 0 #4B4E69;
}
body.dark-theme .border-right-grey, body.dark-theme .task_view .taskView, body.dark-theme .border-right {
  border-right: 1px solid #4B4E69 !important;
}
body.dark-theme .border-left-grey, body.dark-theme .more-filters, body.dark-theme .daterangepicker.show-ranges.ltr .drp-calendar.left, body.dark-theme .border-left, body.dark-theme .c-inv-btns .dropdown-toggle span {
  border-left: 1px solid #4B4E69 !important;
}
body.dark-theme .border-bottom-grey, body.dark-theme .main-header, body.dark-theme .modal-header, body.dark-theme .modal-body, body.dark-theme .message_wrapper .msg-header, body.dark-theme .border-bottom, body.dark-theme .nav-tabs, body.dark-theme .tabs.--show-secondary .-secondary li {
  border-bottom: 1px solid #4B4E69 !important;
}
body.dark-theme .border-top-grey, body.dark-theme .modal-footer, body.dark-theme .more-filters .more-filter-tab.in .clear-all, body.dark-theme .daterangepicker .drp-buttons, body.dark-theme .border-top {
  border-top: 1px solid #4B4E69 !important;
}
body.dark-theme .b-shadow-4, body.dark-theme .qs-datepicker-container, body.dark-theme .form-control, body.dark-theme .w-tables, body.dark-theme .c-inv-desc table tr td, body.dark-theme .border, body.dark-theme .table-bordered, body.dark-theme .c-inv-total .c-inv-total-right td {
  box-shadow: none;
  border: 1px solid #4B4E69 !important;
}
body.dark-theme .sidebar-brand-box .sidebar-brand-dropdown, body.dark-theme .dropdown-menu, body.dark-theme .add-client, body.dark-theme .modal-content, body.dark-theme input, body.dark-theme tbody, body.dark-theme .message_wrapper .msg-content-right .mbl-sender-name, body.dark-theme .more-filters .more-filter-tab, body.dark-theme .daterangepicker {
  background-color: #181C34;
  border: 1px solid #4B4E69 !important;
}
body.dark-theme .modal.show {
  background-color: rgba(0, 0, 0, 0.47);
}
body.dark-theme .menu-collapse:before {
  background: none;
}
body.dark-theme .recent-activity .res-activity-list:after {
  border: 1px solid #4B4E69 !important;
}
body.dark-theme .select-box .date-picker-field, body.dark-theme .select-box .date-picker-field1, body.dark-theme .msg-content-left input {
  border: none !important;
  color: #D5D8DF !important;
}
body.dark-theme .close {
  color: #fff;
}
body.dark-theme .table thead th {
  box-shadow: none;
  border-bottom: 1px solid #4B4E69 !important;
  color: #D5D8DF !important;
}
body.dark-theme .c-inv-disc td, body.dark-theme .inv-num-date tr td, body.dark-theme .inv-detail tr td, body.dark-theme .message_wrapper .msg-content-left, body.dark-theme .message_wrapper, body.dark-theme .message_wrapper .msg-content-left .card, body.dark-theme .message_wrapper .input-group, body.dark-theme .settings-sidebar, body.dark-theme .border-grey, body.dark-theme .ticket-wrapper, body.dark-theme .task-detail-panel, body.dark-theme ul.thumbnails.image_picker_selector li .thumbnail, body.dark-theme .task_view, body.dark-theme .fc-theme-standard .fc-scrollgrid, body.dark-theme .fc-theme-standard td, body.dark-theme .fc-theme-standard th, body.dark-theme .fc .fc-button-primary, body.dark-theme .dropify-wrapper, body.dark-theme .input-group-text, body.dark-theme .fc-theme-standard .fc-list, body.dark-theme .tagify, body.dark-theme .list-group-item {
  border: 1px solid #4B4E69 !important;
}
body.dark-theme .dash-border-top {
  border-top: 1px dashed #4B4E69 !important;
}
body.dark-theme .daterangepicker .ranges li.active, body.dark-theme .page-link:hover, body.dark-theme .permisison-table .bg-light, body.dark-theme .w-task-board-box:after {
  background-color: #4B4E69 !important;
}
body.dark-theme .ql-toolbar.ql-snow, body.dark-theme .ql-container.ql-snow, body.dark-theme .daterangepicker .calendar-table .next span, body.dark-theme .daterangepicker .calendar-table .prev span {
  border-color: #4B4E69;
}
body.dark-theme .daterangepicker .calendar-table {
  border: 1px solid #181c34;
  background-color: #181c34;
}
body.dark-theme .page-item.disabled .page-link, body.dark-theme .pagination li a {
  background-color: #29304c;
  border-color: #29304c;
}
body.dark-theme .filter-box .select-box .bootstrap-select .dropdown-toggle, body.dark-theme .filter-box .select-box .bootstrap-select .btn-light:not(:disabled):not(.disabled):active, body.dark-theme .filter-box .select-box .bootstrap-select .btn-light:not(:disabled):not(.disabled).active, body.dark-theme .btn:hover, body.dark-theme .message_wrapper .msg-content-right .chat-box .card:hover p {
  color: #9c9fa6 !important;
}
body.dark-theme .pagination .page-item.active .page-link, body.dark-theme .page-item.active .page-link, body.dark-theme .dropdown-item.active, body.dark-theme .dropdown-item:active:hover, body.dark-theme .btn-primary {
  background-color: #D5D8DF !important;
  border-color: #D5D8DF;
}
body.dark-theme .page-item.active .page-link, body.dark-theme .dropdown-item.active, body.dark-theme .dropdown-item.active:hover, body.dark-theme .text-white {
  color: #181c34 !important;
}
body.dark-theme .daterangepicker td.off.end-date, body.dark-theme .daterangepicker td.active.end-date {
  background-color: #616e80 !important;
}
body.dark-theme .bootstrap-timepicker-widget.dropdown-menu:after {
  border-bottom: 6px solid #4b4e69;
}
body.dark-theme .tagify__tag > div::before {
  box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em) #29304c inset;
}
body.dark-theme ::-moz-placeholder {
  color: #a7aaaf !important;
}
body.dark-theme ::placeholder, body.dark-theme .tagify__input:empty::before {
  color: #a7aaaf !important;
}
body.dark-theme ::-moz-placeholder :focus::-moz-placeholder { /* Firefox 19+ */
  opacity: 0.5;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
body.dark-theme ::placeholder :focus::-moz-placeholder, body.dark-theme .tagify__input:empty::before :focus::-moz-placeholder { /* Firefox 19+ */
  opacity: 0.5;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
body.dark-theme ::-moz-placeholder :focus:-moz-placeholder { /* Firefox 18- */
  opacity: 0.5;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
body.dark-theme ::placeholder :focus:-moz-placeholder, body.dark-theme .tagify__input:empty::before :focus:-moz-placeholder { /* Firefox 18- */
  opacity: 0.5;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
body.dark-theme .btn-active, body.dark-theme .btn-active.btn-secondary {
  background-color: #616e80 !important;
  border: 1px solid #616e80 !important;
}
body.dark-theme .btn-primary {
  background-color: #ffffff !important;
  color: #181c34 !important;
  border: 1px solid #ffffff !important;
}
body.dark-theme .btn-primary:hover {
  background-color: #000000 !important;
  border: 1px solid #000000 !important;
  color: #fff !important;
}
body.dark-theme .btn-secondary {
  border: solid 1px #616e80;
  background-color: #29304c !important;
  color: #bec1ca !important;
}
body.dark-theme .btn-secondary:hover {
  background-color: #000000 !important;
  border: 1px solid #000000 !important;
}
body.dark-theme .daterangepicker:before {
  border-bottom: 7px solid #4b4e69;
}
body.dark-theme .daterangepicker:after {
  border-bottom: 6px solid #4b4e69;
}
body.dark-theme .alert-secondary {
  color: #e2e3e8;
  background-color: #29304c;
  border-color: #4b4e69;
}
body.dark-theme input:-webkit-autofill,
body.dark-theme input:-webkit-autofill:hover,
body.dark-theme input:-webkit-autofill:focus,
body.dark-theme input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px #29304c inset !important;
}
body.dark-theme input:-webkit-autofill {
  -webkit-text-fill-color: white !important;
}
body.dark-theme .msg-content-left .py-1, body.dark-theme .settings-sidebar .py-1 {
  padding: 0px !important;
}
body.dark-theme .msg-content-left .py-1 {
  flex-wrap: nowrap;
}
body.dark-theme .fc-theme-standard .fc-listWeek-view table td {
  border: none !important;
}
body.dark-theme tr.alert-primary {
  background-color: #212851;
  border-color: #212851;
}
body.dark-theme .alert .text-dark-grey {
  color: #616e80 !important;
}
body.dark-theme .dropdown-item.active .text-muted {
  color: #6c757d !important;
}
body.dark-theme .dropdown-item.active .text-darkest-grey {
  color: #4d4f5c !important;
}
body.dark-theme .sidebar-menu::-webkit-scrollbar {
  width: 10px;
  background: #262f3a;
}
body.dark-theme .sidebar-menu::-webkit-scrollbar-thumb {
  background-color: #585858;
  border: 3px solid #262f3a;
}
body.dark-theme .spinner-border {
  border: 0.25em solid #d5d8df;
}

body.dark-theme::-webkit-scrollbar,
body.dark-theme .t-d-inner-panel::-webkit-scrollbar,
body.dark-theme .show::-webkit-scrollbar,
body.dark-theme .b-p-body::-webkit-scrollbar,
body.dark-theme .w-task-board-panel::-webkit-scrollbar,
body.dark-theme .settings-menu::-webkit-scrollbar,
body.dark-theme .card-body::-webkit-scrollbar {
  width: 10px;
  background: #262f3a;
}

body.dark-theme::-webkit-scrollbar-thumb,
body.dark-theme .t-d-inner-panel::-webkit-scrollbar-thumb,
body.dark-theme .show::-webkit-scrollbar-thumb,
body.dark-theme .b-p-body::-webkit-scrollbar-thumb,
body.dark-theme .w-task-board-panel::-webkit-scrollbar-thumb,
body.dark-theme .settings-menu::-webkit-scrollbar-thumb,
body.dark-theme .card-body::-webkit-scrollbar-thumb {
  background-color: #585858;
  border: 3px solid #262f3a;
}

/*# sourceMappingURL=main.css.map*/