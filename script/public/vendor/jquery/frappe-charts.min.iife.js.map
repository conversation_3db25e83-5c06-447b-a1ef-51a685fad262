{"version": 3, "file": "frappe-charts.min.iife.js", "sources": ["../src/js/utils/dom.js", "../src/js/utils/constants.js", "../src/js/utils/helpers.js", "../src/js/utils/draw-utils.js", "../src/js/utils/colors.js", "../src/js/utils/draw.js", "../src/js/utils/animate.js", "../src/js/utils/animation.js", "../src/js/utils/export.js", "../src/js/utils/date-utils.js", "../src/js/objects/ChartComponents.js", "../src/js/utils/intervals.js", "../src/js/utils/axis-chart-utils.js", "../src/js/chart.js", "../node_modules/style-inject/dist/style-inject.es.js", "../src/js/objects/SvgTip.js", "../src/css/chartsCss.js", "../src/js/charts/BaseChart.js", "../src/js/charts/AggregationChart.js", "../src/js/charts/PercentageChart.js", "../src/js/charts/PieChart.js", "../src/js/charts/Heatmap.js", "../src/js/charts/AxisChart.js", "../src/js/charts/DonutChart.js", "../src/js/index.js"], "sourcesContent": ["export function $(expr, con) {\n\treturn typeof expr === \"string\"? (con || document).querySelector(expr) : expr || null;\n}\n\nexport function findNodeIndex(node)\n{\n\tvar i = 0;\n\twhile (node.previousSibling) {\n\t\tnode = node.previousSibling;\n\t\ti++;\n\t}\n\treturn i;\n}\n\n$.create = (tag, o) => {\n\tvar element = document.createElement(tag);\n\n\tfor (var i in o) {\n\t\tvar val = o[i];\n\n\t\tif (i === \"inside\") {\n\t\t\t$(val).appendChild(element);\n\t\t}\n\t\telse if (i === \"around\") {\n\t\t\tvar ref = $(val);\n\t\t\tref.parentNode.insertBefore(element, ref);\n\t\t\telement.appendChild(ref);\n\n\t\t} else if (i === \"styles\") {\n\t\t\tif(typeof val === \"object\") {\n\t\t\t\tObject.keys(val).map(prop => {\n\t\t\t\t\telement.style[prop] = val[prop];\n\t\t\t\t});\n\t\t\t}\n\t\t} else if (i in element ) {\n\t\t\telement[i] = val;\n\t\t}\n\t\telse {\n\t\t\telement.setAttribute(i, val);\n\t\t}\n\t}\n\n\treturn element;\n};\n\nexport function getOffset(element) {\n\tlet rect = element.getBoundingClientRect();\n\treturn {\n\t\t// https://stackoverflow.com/a/7436602/6495043\n\t\t// rect.top varies with scroll, so we add whatever has been\n\t\t// scrolled to it to get absolute distance from actual page top\n\t\ttop: rect.top + (document.documentElement.scrollTop || document.body.scrollTop),\n\t\tleft: rect.left + (document.documentElement.scrollLeft || document.body.scrollLeft)\n\t};\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent\n// an element's offsetParent property will return null whenever it, or any of its parents,\n// is hidden via the display style property.\nexport function isHidden(el) {\n\treturn (el.offsetParent === null);\n}\n\nexport function isElementInViewport(el) {\n\t// Although straightforward: https://stackoverflow.com/a/7557433/6495043\n\tvar rect = el.getBoundingClientRect();\n\n\treturn (\n\t\trect.top >= 0 &&\n        rect.left >= 0 &&\n        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && /*or $(window).height() */\n        rect.right <= (window.innerWidth || document.documentElement.clientWidth) /*or $(window).width() */\n\t);\n}\n\nexport function getElementContentWidth(element) {\n\tvar styles = window.getComputedStyle(element);\n\tvar padding = parseFloat(styles.paddingLeft) +\n\t\tparseFloat(styles.paddingRight);\n\n\treturn element.clientWidth - padding;\n}\n\nexport function bind(element, o){\n\tif (element) {\n\t\tfor (var event in o) {\n\t\t\tvar callback = o[event];\n\n\t\t\tevent.split(/\\s+/).forEach(function (event) {\n\t\t\t\telement.addEventListener(event, callback);\n\t\t\t});\n\t\t}\n\t}\n}\n\nexport function unbind(element, o){\n\tif (element) {\n\t\tfor (var event in o) {\n\t\t\tvar callback = o[event];\n\n\t\t\tevent.split(/\\s+/).forEach(function(event) {\n\t\t\t\telement.removeEventListener(event, callback);\n\t\t\t});\n\t\t}\n\t}\n}\n\nexport function fire(target, type, properties) {\n\tvar evt = document.createEvent(\"HTMLEvents\");\n\n\tevt.initEvent(type, true, true );\n\n\tfor (var j in properties) {\n\t\tevt[j] = properties[j];\n\t}\n\n\treturn target.dispatchEvent(evt);\n}\n\n// https://css-tricks.com/snippets/javascript/loop-queryselectorall-matches/\nexport function forEachNode(nodeList, callback, scope) {\n\tif(!nodeList) return;\n\tfor (var i = 0; i < nodeList.length; i++) {\n\t\tcallback.call(scope, nodeList[i], i);\n\t}\n}\n\nexport function activate($parent, $child, commonClass, activeClass='active', index = -1) {\n\tlet $children = $parent.querySelectorAll(`.${commonClass}.${activeClass}`);\n\n\tforEachNode($children, (node, i) => {\n\t\tif(index >= 0 && i <= index) return;\n\t\tnode.classList.remove(activeClass);\n\t});\n\n\t$child.classList.add(activeClass);\n}\n", "export const ALL_CHART_TYPES = ['line', 'scatter', 'bar', 'percentage', 'heatmap', 'pie'];\n\nexport const COMPATIBLE_CHARTS = {\n\tbar: ['line', 'scatter', 'percentage', 'pie'],\n\tline: ['scatter', 'bar', 'percentage', 'pie'],\n\tpie: ['line', 'scatter', 'percentage', 'bar'],\n\tpercentage: ['bar', 'line', 'scatter', 'pie'],\n\theatmap: []\n};\n\nexport const DATA_COLOR_DIVISIONS = {\n\tbar: 'datasets',\n\tline: 'datasets',\n\tpie: 'labels',\n\tpercentage: 'labels',\n\theatmap: HEATMAP_DISTRIBUTION_SIZE\n};\n\nexport const BASE_MEASURES = {\n\tmargins: {\n\t\ttop: 10,\n\t\tbottom: 10,\n\t\tleft: 20,\n\t\tright: 20\n\t},\n\tpaddings: {\n\t\ttop: 20,\n\t\tbottom: 40,\n\t\tleft: 30,\n\t\tright: 10\n\t},\n\n\tbaseHeight: 240,\n\ttitleHeight: 20,\n\tlegendHeight: 30,\n\n\ttitleFontSize: 12,\n};\n\nexport function getTopOffset(m) {\n\treturn m.titleHeight + m.margins.top + m.paddings.top;\n}\n\nexport function getLeftOffset(m) {\n\treturn m.margins.left + m.paddings.left;\n}\n\nexport function getExtraHeight(m) {\n\tlet totalExtraHeight = m.margins.top + m.margins.bottom\n\t\t+ m.paddings.top + m.paddings.bottom\n\t\t+ m.titleHeight + m.legendHeight;\n\treturn totalExtraHeight;\n}\n\nexport function getExtraWidth(m) {\n\tlet totalExtraWidth = m.margins.left + m.margins.right\n\t\t+ m.paddings.left + m.paddings.right;\n\n\treturn totalExtraWidth;\n}\n\nexport const INIT_CHART_UPDATE_TIMEOUT = 700;\nexport const CHART_POST_ANIMATE_TIMEOUT = 400;\n\nexport const DEFAULT_AXIS_CHART_TYPE = 'line';\nexport const AXIS_DATASET_CHART_TYPES = ['line', 'bar'];\n\nexport const AXIS_LEGEND_BAR_SIZE = 100;\n\nexport const BAR_CHART_SPACE_RATIO = 0.5;\nexport const MIN_BAR_PERCENT_HEIGHT = 0.00;\n\nexport const LINE_CHART_DOT_SIZE = 4;\nexport const DOT_OVERLAY_SIZE_INCR = 4;\n\nexport const PERCENTAGE_BAR_DEFAULT_HEIGHT = 20;\nexport const PERCENTAGE_BAR_DEFAULT_DEPTH = 2;\n\n// Fixed 5-color theme,\n// More colors are difficult to parse visually\nexport const HEATMAP_DISTRIBUTION_SIZE = 5;\n\nexport const HEATMAP_SQUARE_SIZE = 10;\nexport const HEATMAP_GUTTER_SIZE = 2;\n\nexport const DEFAULT_CHAR_WIDTH = 7;\n\nexport const TOOLTIP_POINTER_TRIANGLE_HEIGHT = 5;\n\nconst DEFAULT_CHART_COLORS = ['light-blue', 'blue', 'violet', 'red', 'orange',\n\t'yellow', 'green', 'light-green', 'purple', 'magenta', 'light-grey', 'dark-grey'];\nconst HEATMAP_COLORS_GREEN = ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127'];\nexport const HEATMAP_COLORS_BLUE = ['#ebedf0', '#c0ddf9', '#73b3f3', '#3886e1', '#17459e'];\nexport const HEATMAP_COLORS_YELLOW = ['#ebedf0', '#fdf436', '#ffc700', '#ff9100', '#06001c'];\n\nexport const DEFAULT_COLORS = {\n\tbar: DEFAULT_CHART_COLORS,\n\tline: DEFAULT_CHART_COLORS,\n\tpie: DEFAULT_CHART_COLORS,\n\tpercentage: DEFAULT_CHART_COLORS,\n\theatmap: HEATMAP_COLORS_GREEN,\n\tdonut: DEFAULT_CHART_COLORS\n};\n\n// Universal constants\nexport const ANGLE_RATIO = Math.PI / 180;\nexport const FULL_ANGLE = 360;\n", "import { ANGLE_RATIO } from './constants';\n\n/**\n * Returns the value of a number upto 2 decimal places.\n * @param {Number} d Any number\n */\nexport function floatTwo(d) {\n\treturn parseFloat(d.toFixed(2));\n}\n\n/**\n * Returns whether or not two given arrays are equal.\n * @param {Array} arr1 First array\n * @param {Array} arr2 Second array\n */\nexport function arraysEqual(arr1, arr2) {\n\tif(arr1.length !== arr2.length) return false;\n\tlet areEqual = true;\n\tarr1.map((d, i) => {\n\t\tif(arr2[i] !== d) areEqual = false;\n\t});\n\treturn areEqual;\n}\n\n/**\n * Shuffles array in place. ES6 version\n * @param {Array} array An array containing the items.\n */\nexport function shuffle(array) {\n\t// Awesomeness: https://bost.ocks.org/mike/shuffle/\n\t// https://stackoverflow.com/a/2450976/6495043\n\t// https://stackoverflow.com/questions/6274339/how-can-i-shuffle-an-array?noredirect=1&lq=1\n\n\tfor (let i = array.length - 1; i > 0; i--) {\n\t\tlet j = Math.floor(Math.random() * (i + 1));\n\t\t[array[i], array[j]] = [array[j], array[i]];\n\t}\n\n\treturn array;\n}\n\n/**\n * Fill an array with extra points\n * @param {Array} array Array\n * @param {Number} count number of filler elements\n * @param {Object} element element to fill with\n * @param {Boolean} start fill at start?\n */\nexport function fillArray(array, count, element, start=false) {\n\tif(!element) {\n\t\telement = start ? array[0] : array[array.length - 1];\n\t}\n\tlet fillerArray = new Array(Math.abs(count)).fill(element);\n\tarray = start ? fillerArray.concat(array) : array.concat(fillerArray);\n\treturn array;\n}\n\n/**\n * Returns pixel width of string.\n * @param {String} string\n * @param {Number} charWidth Width of single char in pixels\n */\nexport function getStringWidth(string, charWidth) {\n\treturn (string+\"\").length * charWidth;\n}\n\nexport function bindChange(obj, getFn, setFn) {\n\treturn new Proxy(obj, {\n\t\tset: function(target, prop, value) {\n\t\t\tsetFn();\n\t\t\treturn Reflect.set(target, prop, value);\n\t\t},\n\t\tget: function(target, prop) {\n\t\t\tgetFn();\n\t\t\treturn Reflect.get(target, prop);\n\t\t}\n\t});\n}\n\n// https://stackoverflow.com/a/29325222\nexport function getRandomBias(min, max, bias, influence) {\n\tconst range = max - min;\n\tconst biasValue = range * bias + min;\n\tvar rnd = Math.random() * range + min,\t\t// random in range\n\t\tmix = Math.random() * influence;\t\t// random mixer\n\treturn rnd * (1 - mix) + biasValue * mix;\t// mix full range and bias\n}\n\nexport function getPositionByAngle(angle, radius) {\n\treturn {\n\t\tx: Math.sin(angle * ANGLE_RATIO) * radius,\n\t\ty: Math.cos(angle * ANGLE_RATIO) * radius,\n\t};\n}\n\n/**\n * Check if a number is valid for svg attributes\n * @param {object} candidate Candidate to test\n * @param {Boolean} nonNegative flag to treat negative number as invalid\n */\nexport function isValidNumber(candidate, nonNegative=false) {\n\tif (Number.isNaN(candidate)) return false;\n\telse if (candidate === undefined) return false;\n\telse if (!Number.isFinite(candidate)) return false;\n\telse if (nonNegative && candidate < 0) return false;\n\telse return true;\n}\n\n/**\n * Round a number to the closes precision, max max precision 4\n * @param {Number} d Any Number\n */\nexport function round(d) {\n\t// https://floating-point-gui.de/\n\t// https://www.jacklmoore.com/notes/rounding-in-javascript/\n\treturn Number(Math.round(d + 'e4') + 'e-4');\n}\n", "import { fillArray } from './helpers';\n\nexport function getBarHeightAndYAttr(yTop, zeroLine) {\n\tlet height, y;\n\tif (yTop <= zeroLine) {\n\t\theight = zeroLine - yTop;\n\t\ty = yTop;\n\t} else {\n\t\theight = yTop - zeroLine;\n\t\ty = zeroLine;\n\t}\n\n\treturn [height, y];\n}\n\nexport function equilizeNoOfElements(array1, array2,\n\textraCount = array2.length - array1.length) {\n\n\t// Doesn't work if either has zero elements.\n\tif(extraCount > 0) {\n\t\tarray1 = fillArray(array1, extraCount);\n\t} else {\n\t\tarray2 = fillArray(array2, extraCount);\n\t}\n\treturn [array1, array2];\n}\n\nexport function truncateString(txt, len) {\n\tif (!txt) {\n\t\treturn;\n\t}\n\tif (txt.length > len) {\n\t\treturn txt.slice(0, len-3) + '...';\n\t} else {\n\t\treturn txt;\n\t}\n}\n\nexport function shortenLargeNumber(label) {\n\tlet number;\n\tif (typeof label === 'number') number = label;\n\telse if (typeof label === 'string') {\n\t\tnumber = Number(label);\n\t\tif (Number.isNaN(number)) return label;\n\t}\n\n\t// Using absolute since log wont work for negative numbers\n\tlet p = Math.floor(Math.log10(Math.abs(number)));\n\tif (p <= 2) return number; // Return as is for a 3 digit number of less\n\tlet\tl = Math.floor(p / 3);\n\tlet shortened = (Math.pow(10, p - l * 3) * +(number / Math.pow(10, p)).toFixed(1));\n\n\t// Correct for floating point error upto 2 decimal places\n\treturn Math.round(shortened*100)/100 + ' ' + ['', 'K', 'M', 'B', 'T'][l];\n}\n\n// cubic bezier curve calculation (from example by François Romain)\nexport function getSplineCurvePointsStr(xList, yList) {\n\n\tlet points=[];\n\tfor(let i=0;i<xList.length;i++){\n\t\tpoints.push([xList[i], yList[i]]);\n\t}\n\n\tlet smoothing = 0.2;\n\tlet line = (pointA, pointB) => {\n\t\tlet lengthX = pointB[0] - pointA[0];\n\t\tlet lengthY = pointB[1] - pointA[1];\n\t\treturn {\n\t\t\tlength: Math.sqrt(Math.pow(lengthX, 2) + Math.pow(lengthY, 2)),\n\t\t\tangle: Math.atan2(lengthY, lengthX)\n\t\t};\n\t};\n    \n\tlet controlPoint = (current, previous, next, reverse) => {\n\t\tlet p = previous || current;\n\t\tlet n = next || current;\n\t\tlet o = line(p, n);\n\t\tlet angle = o.angle + (reverse ? Math.PI : 0);\n\t\tlet length = o.length * smoothing;\n\t\tlet x = current[0] + Math.cos(angle) * length;\n\t\tlet y = current[1] + Math.sin(angle) * length;\n\t\treturn [x, y];\n\t};\n    \n\tlet bezierCommand = (point, i, a) => {\n\t\tlet cps = controlPoint(a[i - 1], a[i - 2], point);\n\t\tlet cpe = controlPoint(point, a[i - 1], a[i + 1], true);\n\t\treturn `C ${cps[0]},${cps[1]} ${cpe[0]},${cpe[1]} ${point[0]},${point[1]}`;\n\t};\n    \n\tlet pointStr = (points, command) => {\n\t\treturn points.reduce((acc, point, i, a) => i === 0\n\t\t\t? `${point[0]},${point[1]}`\n\t\t\t: `${acc} ${command(point, i, a)}`, '');\n\t};\n    \n\treturn pointStr(points, bezierCommand);\n}\n", "const PRESET_COLOR_MAP = {\n\t'light-blue': '#7cd6fd',\n\t'blue': '#5e64ff',\n\t'violet': '#743ee2',\n\t'red': '#ff5858',\n\t'orange': '#ffa00a',\n\t'yellow': '#feef72',\n\t'green': '#28a745',\n\t'light-green': '#98d85b',\n\t'purple': '#b554ff',\n\t'magenta': '#ffa3ef',\n\t'black': '#36114C',\n\t'grey': '#bdd3e6',\n\t'light-grey': '#f0f4f7',\n\t'dark-grey': '#b8c2cc'\n};\n\nfunction limitColor(r){\n\tif (r > 255) return 255;\n\telse if (r < 0) return 0;\n\treturn r;\n}\n\nexport function lightenDarkenColor(color, amt) {\n\tlet col = getColor(color);\n\tlet usePound = false;\n\tif (col[0] == \"#\") {\n\t\tcol = col.slice(1);\n\t\tusePound = true;\n\t}\n\tlet num = parseInt(col,16);\n\tlet r = limitColor((num >> 16) + amt);\n\tlet b = limitColor(((num >> 8) & 0x00FF) + amt);\n\tlet g = limitColor((num & 0x0000FF) + amt);\n\treturn (usePound?\"#\":\"\") + (g | (b << 8) | (r << 16)).toString(16);\n}\n\nexport function isValidColor(string) {\n\t// https://stackoverflow.com/a/32685393\n\tlet HEX_RE = /(^\\s*)(#)((?:[A-Fa-f0-9]{3}){1,2})$/i;\n\tlet RGB_RE = /(^\\s*)(rgb|hsl)(a?)[(]\\s*([\\d.]+\\s*%?)\\s*,\\s*([\\d.]+\\s*%?)\\s*,\\s*([\\d.]+\\s*%?)\\s*(?:,\\s*([\\d.]+)\\s*)?[)]$/i;\n\treturn HEX_RE.test(string) || RGB_RE.test(string);\n}\n\nexport const getColor = (color) => {\n\t// When RGB color, convert to hexadecimal (alpha value is omitted)\n\tif((/rgb[a]{0,1}\\([\\d, ]+\\)/gim).test(color)) {\n\t\treturn (/\\D+(\\d*)\\D+(\\d*)\\D+(\\d*)/gim).exec(color)\n\t\t\t.map((x, i) => (i !== 0 ? Number(x).toString(16) : '#'))\n\t\t\t.reduce((c, ch) => `${c}${ch}`);\n\t}\n\treturn PRESET_COLOR_MAP[color] || color;\n};\n", "import { getBarHeightAndYAttr, truncateString, shortenLargeNumber, getSplineCurvePointsStr } from './draw-utils';\nimport { getStringWidth, isValidNumber } from './helpers';\nimport { DOT_OVERLAY_SIZE_INCR, PERCENTAGE_BAR_DEFAULT_DEPTH } from './constants';\nimport { lightenDarkenColor } from './colors';\n\nexport const AXIS_TICK_LENGTH = 6;\nconst LABEL_MARGIN = 4;\nconst LABEL_MAX_CHARS = 15;\nexport const FONT_SIZE = 10;\nconst BASE_LINE_COLOR = '#dadada';\nconst FONT_FILL = '#555b51';\n\nfunction $(expr, con) {\n\treturn typeof expr === \"string\"? (con || document).querySelector(expr) : expr || null;\n}\n\nexport function createSVG(tag, o) {\n\tvar element = document.createElementNS(\"http://www.w3.org/2000/svg\", tag);\n\n\tfor (var i in o) {\n\t\tvar val = o[i];\n\n\t\tif (i === \"inside\") {\n\t\t\t$(val).appendChild(element);\n\t\t}\n\t\telse if (i === \"around\") {\n\t\t\tvar ref = $(val);\n\t\t\tref.parentNode.insertBefore(element, ref);\n\t\t\telement.appendChild(ref);\n\n\t\t} else if (i === \"styles\") {\n\t\t\tif(typeof val === \"object\") {\n\t\t\t\tObject.keys(val).map(prop => {\n\t\t\t\t\telement.style[prop] = val[prop];\n\t\t\t\t});\n\t\t\t}\n\t\t} else {\n\t\t\tif(i === \"className\") { i = \"class\"; }\n\t\t\tif(i === \"innerHTML\") {\n\t\t\t\telement['textContent'] = val;\n\t\t\t} else {\n\t\t\t\telement.setAttribute(i, val);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn element;\n}\n\nfunction renderVerticalGradient(svgDefElem, gradientId) {\n\treturn createSVG('linearGradient', {\n\t\tinside: svgDefElem,\n\t\tid: gradientId,\n\t\tx1: 0,\n\t\tx2: 0,\n\t\ty1: 0,\n\t\ty2: 1\n\t});\n}\n\nfunction setGradientStop(gradElem, offset, color, opacity) {\n\treturn createSVG('stop', {\n\t\t'inside': gradElem,\n\t\t'style': `stop-color: ${color}`,\n\t\t'offset': offset,\n\t\t'stop-opacity': opacity\n\t});\n}\n\nexport function makeSVGContainer(parent, className, width, height) {\n\treturn createSVG('svg', {\n\t\tclassName: className,\n\t\tinside: parent,\n\t\twidth: width,\n\t\theight: height\n\t});\n}\n\nexport function makeSVGDefs(svgContainer) {\n\treturn createSVG('defs', {\n\t\tinside: svgContainer,\n\t});\n}\n\nexport function makeSVGGroup(className, transform='', parent=undefined) {\n\tlet args = {\n\t\tclassName: className,\n\t\ttransform: transform\n\t};\n\tif(parent) args.inside = parent;\n\treturn createSVG('g', args);\n}\n\nexport function wrapInSVGGroup(elements, className='') {\n\tlet g = createSVG('g', {\n\t\tclassName: className\n\t});\n\telements.forEach(e => g.appendChild(e));\n\treturn g;\n}\n\nexport function makePath(pathStr, className='', stroke='none', fill='none', strokeWidth=2) {\n\treturn createSVG('path', {\n\t\tclassName: className,\n\t\td: pathStr,\n\t\tstyles: {\n\t\t\tstroke: stroke,\n\t\t\tfill: fill,\n\t\t\t'stroke-width': strokeWidth\n\t\t}\n\t});\n}\n\nexport function makeArcPathStr(startPosition, endPosition, center, radius, clockWise=1, largeArc=0){\n\tlet [arcStartX, arcStartY] = [center.x + startPosition.x, center.y + startPosition.y];\n\tlet [arcEndX, arcEndY] = [center.x + endPosition.x, center.y + endPosition.y];\n\treturn `M${center.x} ${center.y}\n\t\tL${arcStartX} ${arcStartY}\n\t\tA ${radius} ${radius} 0 ${largeArc} ${clockWise ? 1 : 0}\n\t\t${arcEndX} ${arcEndY} z`;\n}\n\nexport function makeCircleStr(startPosition, endPosition, center, radius, clockWise=1, largeArc=0){\n\tlet [arcStartX, arcStartY] = [center.x + startPosition.x, center.y + startPosition.y];\n\tlet [arcEndX, midArc, arcEndY] = [center.x + endPosition.x, center.y * 2, center.y + endPosition.y];\n\treturn `M${center.x} ${center.y}\n\t\tL${arcStartX} ${arcStartY}\n\t\tA ${radius} ${radius} 0 ${largeArc} ${clockWise ? 1 : 0}\n\t\t${arcEndX} ${midArc} z\n\t\tL${arcStartX} ${midArc}\n\t\tA ${radius} ${radius} 0 ${largeArc} ${clockWise ? 1 : 0}\n\t\t${arcEndX} ${arcEndY} z`;\n}\n\nexport function makeArcStrokePathStr(startPosition, endPosition, center, radius, clockWise=1, largeArc=0){\n\tlet [arcStartX, arcStartY] = [center.x + startPosition.x, center.y + startPosition.y];\n\tlet [arcEndX, arcEndY] = [center.x + endPosition.x, center.y + endPosition.y];\n\n\treturn `M${arcStartX} ${arcStartY}\n\t\tA ${radius} ${radius} 0 ${largeArc} ${clockWise ? 1 : 0}\n\t\t${arcEndX} ${arcEndY}`;\n}\n\nexport function makeStrokeCircleStr(startPosition, endPosition, center, radius, clockWise=1, largeArc=0){\n\tlet [arcStartX, arcStartY] = [center.x + startPosition.x, center.y + startPosition.y];\n\tlet [arcEndX, midArc, arcEndY] = [center.x + endPosition.x, radius * 2 + arcStartY, center.y + startPosition.y];\n\n\treturn `M${arcStartX} ${arcStartY}\n\t\tA ${radius} ${radius} 0 ${largeArc} ${clockWise ? 1 : 0}\n\t\t${arcEndX} ${midArc}\n\t\tM${arcStartX} ${midArc}\n\t\tA ${radius} ${radius} 0 ${largeArc} ${clockWise ? 1 : 0}\n\t\t${arcEndX} ${arcEndY}`;\n}\n\nexport function makeGradient(svgDefElem, color, lighter = false) {\n\tlet gradientId ='path-fill-gradient' + '-' + color + '-' +(lighter ? 'lighter' : 'default');\n\tlet gradientDef = renderVerticalGradient(svgDefElem, gradientId);\n\tlet opacities = [1, 0.6, 0.2];\n\tif(lighter) {\n\t\topacities = [0.4, 0.2, 0];\n\t}\n\n\tsetGradientStop(gradientDef, \"0%\", color, opacities[0]);\n\tsetGradientStop(gradientDef, \"50%\", color, opacities[1]);\n\tsetGradientStop(gradientDef, \"100%\", color, opacities[2]);\n\n\treturn gradientId;\n}\n\nexport function percentageBar(x, y, width, height,\n\tdepth=PERCENTAGE_BAR_DEFAULT_DEPTH, fill='none') {\n\n\tlet args = {\n\t\tclassName: 'percentage-bar',\n\t\tx: x,\n\t\ty: y,\n\t\twidth: width,\n\t\theight: height,\n\t\tfill: fill,\n\t\tstyles: {\n\t\t\t'stroke': lightenDarkenColor(fill, -25),\n\t\t\t// Diabolically good: https://stackoverflow.com/a/9000859\n\t\t\t// https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/stroke-dasharray\n\t\t\t'stroke-dasharray': `0, ${height + width}, ${width}, ${height}`,\n\t\t\t'stroke-width': depth\n\t\t},\n\t};\n\n\treturn createSVG(\"rect\", args);\n}\n\nexport function heatSquare(className, x, y, size, radius, fill='none', data={}) {\n\tlet args = {\n\t\tclassName: className,\n\t\tx: x,\n\t\ty: y,\n\t\twidth: size,\n\t\theight: size,\n\t\trx: radius,\n\t\tfill: fill\n\t};\n\n\tObject.keys(data).map(key => {\n\t\targs[key] = data[key];\n\t});\n\n\treturn createSVG(\"rect\", args);\n}\n\nexport function legendBar(x, y, size, fill='none', label, truncate=false) {\n\tlabel = truncate ? truncateString(label, LABEL_MAX_CHARS) : label;\n\n\tlet args = {\n\t\tclassName: 'legend-bar',\n\t\tx: 0,\n\t\ty: 0,\n\t\twidth: size,\n\t\theight: '2px',\n\t\tfill: fill\n\t};\n\tlet text = createSVG('text', {\n\t\tclassName: 'legend-dataset-text',\n\t\tx: 0,\n\t\ty: 0,\n\t\tdy: (FONT_SIZE * 2) + 'px',\n\t\t'font-size': (FONT_SIZE * 1.2) + 'px',\n\t\t'text-anchor': 'start',\n\t\tfill: FONT_FILL,\n\t\tinnerHTML: label\n\t});\n\n\tlet group = createSVG('g', {\n\t\ttransform: `translate(${x}, ${y})`\n\t});\n\tgroup.appendChild(createSVG(\"rect\", args));\n\tgroup.appendChild(text);\n\n\treturn group;\n}\n\nexport function legendDot(x, y, size, fill='none', label, truncate=false) {\n\tlabel = truncate ? truncateString(label, LABEL_MAX_CHARS) : label;\n\n\tlet args = {\n\t\tclassName: 'legend-dot',\n\t\tcx: 0,\n\t\tcy: 0,\n\t\tr: size,\n\t\tfill: fill\n\t};\n\tlet text = createSVG('text', {\n\t\tclassName: 'legend-dataset-text',\n\t\tx: 0,\n\t\ty: 0,\n\t\tdx: (FONT_SIZE) + 'px',\n\t\tdy: (FONT_SIZE/3) + 'px',\n\t\t'font-size': (FONT_SIZE * 1.2) + 'px',\n\t\t'text-anchor': 'start',\n\t\tfill: FONT_FILL,\n\t\tinnerHTML: label\n\t});\n\n\tlet group = createSVG('g', {\n\t\ttransform: `translate(${x}, ${y})`\n\t});\n\tgroup.appendChild(createSVG(\"circle\", args));\n\tgroup.appendChild(text);\n\n\treturn group;\n}\n\nexport function makeText(className, x, y, content, options = {}) {\n\tlet fontSize = options.fontSize || FONT_SIZE;\n\tlet dy = options.dy !== undefined ? options.dy : (fontSize / 2);\n\tlet fill = options.fill || FONT_FILL;\n\tlet textAnchor = options.textAnchor || 'start';\n\treturn createSVG('text', {\n\t\tclassName: className,\n\t\tx: x,\n\t\ty: y,\n\t\tdy: dy + 'px',\n\t\t'font-size': fontSize + 'px',\n\t\tfill: fill,\n\t\t'text-anchor': textAnchor,\n\t\tinnerHTML: content\n\t});\n}\n\nfunction makeVertLine(x, label, y1, y2, options={}) {\n\tif(!options.stroke) options.stroke = BASE_LINE_COLOR;\n\tlet l = createSVG('line', {\n\t\tclassName: 'line-vertical ' + options.className,\n\t\tx1: 0,\n\t\tx2: 0,\n\t\ty1: y1,\n\t\ty2: y2,\n\t\tstyles: {\n\t\t\tstroke: options.stroke\n\t\t}\n\t});\n\n\tlet text = createSVG('text', {\n\t\tx: 0,\n\t\ty: y1 > y2 ? y1 + LABEL_MARGIN : y1 - LABEL_MARGIN - FONT_SIZE,\n\t\tdy: FONT_SIZE + 'px',\n\t\t'font-size': FONT_SIZE + 'px',\n\t\t'text-anchor': 'middle',\n\t\tinnerHTML: label + \"\"\n\t});\n\n\tlet line = createSVG('g', {\n\t\ttransform: `translate(${ x }, 0)`\n\t});\n\n\tline.appendChild(l);\n\tline.appendChild(text);\n\n\treturn line;\n}\n\nfunction makeHoriLine(y, label, x1, x2, options={}) {\n\tif(!options.stroke) options.stroke = BASE_LINE_COLOR;\n\tif(!options.lineType) options.lineType = '';\n\tif (options.shortenNumbers) label = shortenLargeNumber(label);\n\n\tlet className = 'line-horizontal ' + options.className +\n\t\t(options.lineType === \"dashed\" ? \"dashed\": \"\");\n\n\tlet l = createSVG('line', {\n\t\tclassName: className,\n\t\tx1: x1,\n\t\tx2: x2,\n\t\ty1: 0,\n\t\ty2: 0,\n\t\tstyles: {\n\t\t\tstroke: options.stroke\n\t\t}\n\t});\n\n\tlet text = createSVG('text', {\n\t\tx: x1 < x2 ? x1 - LABEL_MARGIN : x1 + LABEL_MARGIN,\n\t\ty: 0,\n\t\tdy: (FONT_SIZE / 2 - 2) + 'px',\n\t\t'font-size': FONT_SIZE + 'px',\n\t\t'text-anchor': x1 < x2 ? 'end' : 'start',\n\t\tinnerHTML: label+\"\"\n\t});\n\n\tlet line = createSVG('g', {\n\t\ttransform: `translate(0, ${y})`,\n\t\t'stroke-opacity': 1\n\t});\n\n\tif(text === 0 || text === '0') {\n\t\tline.style.stroke = \"rgba(27, 31, 35, 0.6)\";\n\t}\n\n\tline.appendChild(l);\n\tline.appendChild(text);\n\n\treturn line;\n}\n\nexport function yLine(y, label, width, options={}) {\n\tif (!isValidNumber(y)) y = 0;\n\n\tif(!options.pos) options.pos = 'left';\n\tif(!options.offset) options.offset = 0;\n\tif(!options.mode) options.mode = 'span';\n\tif(!options.stroke) options.stroke = BASE_LINE_COLOR;\n\tif(!options.className) options.className = '';\n\n\tlet x1 = -1 * AXIS_TICK_LENGTH;\n\tlet x2 = options.mode === 'span' ? width + AXIS_TICK_LENGTH : 0;\n\n\tif(options.mode === 'tick' && options.pos === 'right') {\n\t\tx1 = width + AXIS_TICK_LENGTH;\n\t\tx2 = width;\n\t}\n\n\t// let offset = options.pos === 'left' ? -1 * options.offset : options.offset;\n\n\tx1 += options.offset;\n\tx2 += options.offset;\n\n\treturn makeHoriLine(y, label, x1, x2, {\n\t\tstroke: options.stroke,\n\t\tclassName: options.className,\n\t\tlineType: options.lineType,\n\t\tshortenNumbers: options.shortenNumbers\n\t});\n}\n\nexport function xLine(x, label, height, options={}) {\n\tif (!isValidNumber(x)) x = 0;\n\n\tif(!options.pos) options.pos = 'bottom';\n\tif(!options.offset) options.offset = 0;\n\tif(!options.mode) options.mode = 'span';\n\tif(!options.stroke) options.stroke = BASE_LINE_COLOR;\n\tif(!options.className) options.className = '';\n\n\t// Draw X axis line in span/tick mode with optional label\n\t//                        \ty2(span)\n\t// \t\t\t\t\t\t|\n\t// \t\t\t\t\t\t|\n\t//\t\t\t\tx line\t|\n\t//\t\t\t\t\t\t|\n\t// \t\t\t\t\t   \t|\n\t// ---------------------+-- y2(tick)\n\t//\t\t\t\t\t\t|\n\t//\t\t\t\t\t\t\ty1\n\n\tlet y1 = height + AXIS_TICK_LENGTH;\n\tlet y2 = options.mode === 'span' ? -1 * AXIS_TICK_LENGTH : height;\n\n\tif(options.mode === 'tick' && options.pos === 'top') {\n\t\t// top axis ticks\n\t\ty1 = -1 * AXIS_TICK_LENGTH;\n\t\ty2 = 0;\n\t}\n\n\treturn makeVertLine(x, label, y1, y2, {\n\t\tstroke: options.stroke,\n\t\tclassName: options.className,\n\t\tlineType: options.lineType\n\t});\n}\n\nexport function yMarker(y, label, width, options={}) {\n\tif(!options.labelPos) options.labelPos = 'right';\n\tlet x = options.labelPos === 'left' ? LABEL_MARGIN\n\t\t: width - getStringWidth(label, 5) - LABEL_MARGIN;\n\n\tlet labelSvg = createSVG('text', {\n\t\tclassName: 'chart-label',\n\t\tx: x,\n\t\ty: 0,\n\t\tdy: (FONT_SIZE / -2) + 'px',\n\t\t'font-size': FONT_SIZE + 'px',\n\t\t'text-anchor': 'start',\n\t\tinnerHTML: label+\"\"\n\t});\n\n\tlet line = makeHoriLine(y, '', 0, width, {\n\t\tstroke: options.stroke || BASE_LINE_COLOR,\n\t\tclassName: options.className || '',\n\t\tlineType: options.lineType\n\t});\n\n\tline.appendChild(labelSvg);\n\n\treturn line;\n}\n\nexport function yRegion(y1, y2, width, label, options={}) {\n\t// return a group\n\tlet height = y1 - y2;\n\n\tlet rect = createSVG('rect', {\n\t\tclassName: `bar mini`, // remove class\n\t\tstyles: {\n\t\t\tfill: `rgba(228, 234, 239, 0.49)`,\n\t\t\tstroke: BASE_LINE_COLOR,\n\t\t\t'stroke-dasharray': `${width}, ${height}`\n\t\t},\n\t\t// 'data-point-index': index,\n\t\tx: 0,\n\t\ty: 0,\n\t\twidth: width,\n\t\theight: height\n\t});\n\n\tif(!options.labelPos) options.labelPos = 'right';\n\tlet x = options.labelPos === 'left' ? LABEL_MARGIN\n\t\t: width - getStringWidth(label+\"\", 4.5) - LABEL_MARGIN;\n\n\tlet labelSvg = createSVG('text', {\n\t\tclassName: 'chart-label',\n\t\tx: x,\n\t\ty: 0,\n\t\tdy: (FONT_SIZE / -2) + 'px',\n\t\t'font-size': FONT_SIZE + 'px',\n\t\t'text-anchor': 'start',\n\t\tinnerHTML: label+\"\"\n\t});\n\n\tlet region = createSVG('g', {\n\t\ttransform: `translate(0, ${y2})`\n\t});\n\n\tregion.appendChild(rect);\n\tregion.appendChild(labelSvg);\n\n\treturn region;\n}\n\nexport function datasetBar(x, yTop, width, color, label='', index=0, offset=0, meta={}) {\n\tlet [height, y] = getBarHeightAndYAttr(yTop, meta.zeroLine);\n\ty -= offset;\n\n\tif(height === 0) {\n\t\theight = meta.minHeight;\n\t\ty -= meta.minHeight;\n\t}\n\n\t// Preprocess numbers to avoid svg building errors\n\tif (!isValidNumber(x)) x = 0;\n\tif (!isValidNumber(y)) y = 0;\n\tif (!isValidNumber(height, true)) height = 0;\n\tif (!isValidNumber(width, true)) width = 0;\n\n\tlet rect = createSVG('rect', {\n\t\tclassName: `bar mini`,\n\t\tstyle: `fill: ${color}`,\n\t\t'data-point-index': index,\n\t\tx: x,\n\t\ty: y,\n\t\twidth: width,\n\t\theight: height\n\t});\n\n\tlabel += \"\";\n\n\tif(!label && !label.length) {\n\t\treturn rect;\n\t} else {\n\t\trect.setAttribute('y', 0);\n\t\trect.setAttribute('x', 0);\n\t\tlet text = createSVG('text', {\n\t\t\tclassName: 'data-point-value',\n\t\t\tx: width/2,\n\t\t\ty: 0,\n\t\t\tdy: (FONT_SIZE / 2 * -1) + 'px',\n\t\t\t'font-size': FONT_SIZE + 'px',\n\t\t\t'text-anchor': 'middle',\n\t\t\tinnerHTML: label\n\t\t});\n\n\t\tlet group = createSVG('g', {\n\t\t\t'data-point-index': index,\n\t\t\ttransform: `translate(${x}, ${y})`\n\t\t});\n\t\tgroup.appendChild(rect);\n\t\tgroup.appendChild(text);\n\n\t\treturn group;\n\t}\n}\n\nexport function datasetDot(x, y, radius, color, label='', index=0) {\n\tlet dot = createSVG('circle', {\n\t\tstyle: `fill: ${color}`,\n\t\t'data-point-index': index,\n\t\tcx: x,\n\t\tcy: y,\n\t\tr: radius\n\t});\n\n\tlabel += \"\";\n\n\tif(!label && !label.length) {\n\t\treturn dot;\n\t} else {\n\t\tdot.setAttribute('cy', 0);\n\t\tdot.setAttribute('cx', 0);\n\n\t\tlet text = createSVG('text', {\n\t\t\tclassName: 'data-point-value',\n\t\t\tx: 0,\n\t\t\ty: 0,\n\t\t\tdy: (FONT_SIZE / 2 * -1 - radius) + 'px',\n\t\t\t'font-size': FONT_SIZE + 'px',\n\t\t\t'text-anchor': 'middle',\n\t\t\tinnerHTML: label\n\t\t});\n\n\t\tlet group = createSVG('g', {\n\t\t\t'data-point-index': index,\n\t\t\ttransform: `translate(${x}, ${y})`\n\t\t});\n\t\tgroup.appendChild(dot);\n\t\tgroup.appendChild(text);\n\n\t\treturn group;\n\t}\n}\n\nexport function getPaths(xList, yList, color, options={}, meta={}) {\n\tlet pointsList = yList.map((y, i) => (xList[i] + ',' + y));\n\tlet pointsStr = pointsList.join(\"L\");\n\n\t// Spline\n\tif (options.spline)\n\t\tpointsStr = getSplineCurvePointsStr(xList, yList);\n\n\tlet path = makePath(\"M\"+pointsStr, 'line-graph-path', color);\n\n\t// HeatLine\n\tif(options.heatline) {\n\t\tlet gradient_id = makeGradient(meta.svgDefs, color);\n\t\tpath.style.stroke = `url(#${gradient_id})`;\n\t}\n\n\tlet paths = {\n\t\tpath: path\n\t};\n\n\t// Region\n\tif(options.regionFill) {\n\t\tlet gradient_id_region = makeGradient(meta.svgDefs, color, true);\n\n\t\tlet pathStr = \"M\" + `${xList[0]},${meta.zeroLine}L` + pointsStr + `L${xList.slice(-1)[0]},${meta.zeroLine}`;\n\t\tpaths.region = makePath(pathStr, `region-fill`, 'none', `url(#${gradient_id_region})`);\n\t}\n\n\treturn paths;\n}\n\nexport let makeOverlay = {\n\t'bar': (unit) => {\n\t\tlet transformValue;\n\t\tif(unit.nodeName !== 'rect') {\n\t\t\ttransformValue = unit.getAttribute('transform');\n\t\t\tunit = unit.childNodes[0];\n\t\t}\n\t\tlet overlay = unit.cloneNode();\n\t\toverlay.style.fill = '#000000';\n\t\toverlay.style.opacity = '0.4';\n\n\t\tif(transformValue) {\n\t\t\toverlay.setAttribute('transform', transformValue);\n\t\t}\n\t\treturn overlay;\n\t},\n\n\t'dot': (unit) => {\n\t\tlet transformValue;\n\t\tif(unit.nodeName !== 'circle') {\n\t\t\ttransformValue = unit.getAttribute('transform');\n\t\t\tunit = unit.childNodes[0];\n\t\t}\n\t\tlet overlay = unit.cloneNode();\n\t\tlet radius = unit.getAttribute('r');\n\t\tlet fill = unit.getAttribute('fill');\n\t\toverlay.setAttribute('r', parseInt(radius) + DOT_OVERLAY_SIZE_INCR);\n\t\toverlay.setAttribute('fill', fill);\n\t\toverlay.style.opacity = '0.6';\n\n\t\tif(transformValue) {\n\t\t\toverlay.setAttribute('transform', transformValue);\n\t\t}\n\t\treturn overlay;\n\t},\n\n\t'heat_square': (unit) => {\n\t\tlet transformValue;\n\t\tif(unit.nodeName !== 'circle') {\n\t\t\ttransformValue = unit.getAttribute('transform');\n\t\t\tunit = unit.childNodes[0];\n\t\t}\n\t\tlet overlay = unit.cloneNode();\n\t\tlet radius = unit.getAttribute('r');\n\t\tlet fill = unit.getAttribute('fill');\n\t\toverlay.setAttribute('r', parseInt(radius) + DOT_OVERLAY_SIZE_INCR);\n\t\toverlay.setAttribute('fill', fill);\n\t\toverlay.style.opacity = '0.6';\n\n\t\tif(transformValue) {\n\t\t\toverlay.setAttribute('transform', transformValue);\n\t\t}\n\t\treturn overlay;\n\t}\n};\n\nexport let updateOverlay = {\n\t'bar': (unit, overlay) => {\n\t\tlet transformValue;\n\t\tif(unit.nodeName !== 'rect') {\n\t\t\ttransformValue = unit.getAttribute('transform');\n\t\t\tunit = unit.childNodes[0];\n\t\t}\n\t\tlet attributes = ['x', 'y', 'width', 'height'];\n\t\tObject.values(unit.attributes)\n\t\t\t.filter(attr => attributes.includes(attr.name) && attr.specified)\n\t\t\t.map(attr => {\n\t\t\t\toverlay.setAttribute(attr.name, attr.nodeValue);\n\t\t\t});\n\n\t\tif(transformValue) {\n\t\t\toverlay.setAttribute('transform', transformValue);\n\t\t}\n\t},\n\n\t'dot': (unit, overlay) => {\n\t\tlet transformValue;\n\t\tif(unit.nodeName !== 'circle') {\n\t\t\ttransformValue = unit.getAttribute('transform');\n\t\t\tunit = unit.childNodes[0];\n\t\t}\n\t\tlet attributes = ['cx', 'cy'];\n\t\tObject.values(unit.attributes)\n\t\t\t.filter(attr => attributes.includes(attr.name) && attr.specified)\n\t\t\t.map(attr => {\n\t\t\t\toverlay.setAttribute(attr.name, attr.nodeValue);\n\t\t\t});\n\n\t\tif(transformValue) {\n\t\t\toverlay.setAttribute('transform', transformValue);\n\t\t}\n\t},\n\n\t'heat_square': (unit, overlay) => {\n\t\tlet transformValue;\n\t\tif(unit.nodeName !== 'circle') {\n\t\t\ttransformValue = unit.getAttribute('transform');\n\t\t\tunit = unit.childNodes[0];\n\t\t}\n\t\tlet attributes = ['cx', 'cy'];\n\t\tObject.values(unit.attributes)\n\t\t\t.filter(attr => attributes.includes(attr.name) && attr.specified)\n\t\t\t.map(attr => {\n\t\t\t\toverlay.setAttribute(attr.name, attr.nodeValue);\n\t\t\t});\n\n\t\tif(transformValue) {\n\t\t\toverlay.setAttribute('transform', transformValue);\n\t\t}\n\t},\n};\n", "import { getBarHeightAndYAttr, getSplineCurvePointsStr } from './draw-utils';\n\nexport const UNIT_ANIM_DUR = 350;\nexport const PATH_ANIM_DUR = 350;\nexport const MARKER_LINE_ANIM_DUR = UNIT_ANIM_DUR;\nexport const REPLACE_ALL_NEW_DUR = 250;\n\nexport const STD_EASING = 'easein';\n\nexport function translate(unit, oldCoord, newCoord, duration) {\n\tlet old = typeof oldCoord === 'string' ? oldCoord : oldCoord.join(', ');\n\treturn [\n\t\tunit,\n\t\t{transform: newCoord.join(', ')},\n\t\tduration,\n\t\tSTD_EASING,\n\t\t\"translate\",\n\t\t{transform: old}\n\t];\n}\n\nexport function translateVertLine(xLine, newX, oldX) {\n\treturn translate(xLine, [oldX, 0], [newX, 0], MARKER_LINE_ANIM_DUR);\n}\n\nexport function translateHoriLine(yLine, newY, oldY) {\n\treturn translate(yLine, [0, oldY], [0, newY], MARKER_LINE_ANIM_DUR);\n}\n\nexport function animateRegion(rectGroup, newY1, newY2, oldY2) {\n\tlet newHeight = newY1 - newY2;\n\tlet rect = rectGroup.childNodes[0];\n\tlet width = rect.getAttribute(\"width\");\n\tlet rectAnim = [\n\t\trect,\n\t\t{ height: newHeight, 'stroke-dasharray': `${width}, ${newHeight}` },\n\t\tMARKER_LINE_ANIM_DUR,\n\t\tSTD_EASING\n\t];\n\n\tlet groupAnim = translate(rectGroup, [0, oldY2], [0, newY2], MARKER_LINE_ANIM_DUR);\n\treturn [rectAnim, groupAnim];\n}\n\nexport function animateBar(bar, x, yTop, width, offset=0, meta={}) {\n\tlet [height, y] = getBarHeightAndYAttr(yTop, meta.zeroLine);\n\ty -= offset;\n\tif(bar.nodeName !== 'rect') {\n\t\tlet rect = bar.childNodes[0];\n\t\tlet rectAnim = [\n\t\t\trect,\n\t\t\t{width: width, height: height},\n\t\t\tUNIT_ANIM_DUR,\n\t\t\tSTD_EASING\n\t\t];\n\n\t\tlet oldCoordStr = bar.getAttribute(\"transform\").split(\"(\")[1].slice(0, -1);\n\t\tlet groupAnim = translate(bar, oldCoordStr, [x, y], MARKER_LINE_ANIM_DUR);\n\t\treturn [rectAnim, groupAnim];\n\t} else {\n\t\treturn [[bar, {width: width, height: height, x: x, y: y}, UNIT_ANIM_DUR, STD_EASING]];\n\t}\n\t// bar.animate({height: args.newHeight, y: yTop}, UNIT_ANIM_DUR, mina.easein);\n}\n\nexport function animateDot(dot, x, y) {\n\tif(dot.nodeName !== 'circle') {\n\t\tlet oldCoordStr = dot.getAttribute(\"transform\").split(\"(\")[1].slice(0, -1);\n\t\tlet groupAnim = translate(dot, oldCoordStr, [x, y], MARKER_LINE_ANIM_DUR);\n\t\treturn [groupAnim];\n\t} else {\n\t\treturn [[dot, {cx: x, cy: y}, UNIT_ANIM_DUR, STD_EASING]];\n\t}\n\t// dot.animate({cy: yTop}, UNIT_ANIM_DUR, mina.easein);\n}\n\nexport function animatePath(paths, newXList, newYList, zeroLine, spline) {\n\tlet pathComponents = [];\n\tlet pointsStr = newYList.map((y, i) => (newXList[i] + ',' + y)).join(\"L\");\n\n\tif (spline)\n\t\tpointsStr = getSplineCurvePointsStr(newXList, newYList);\n\n\tconst animPath = [paths.path, {d:\"M\" + pointsStr}, PATH_ANIM_DUR, STD_EASING];\n\tpathComponents.push(animPath);\n\n\tif(paths.region) {\n\t\tlet regStartPt = `${newXList[0]},${zeroLine}L`;\n\t\tlet regEndPt = `L${newXList.slice(-1)[0]}, ${zeroLine}`;\n\n\t\tconst animRegion = [\n\t\t\tpaths.region,\n\t\t\t{d:\"M\" + regStartPt + pointsStr + regEndPt},\n\t\t\tPATH_ANIM_DUR,\n\t\t\tSTD_EASING\n\t\t];\n\t\tpathComponents.push(animRegion);\n\t}\n\n\treturn pathComponents;\n}\n\nexport function animatePathStr(oldPath, pathStr) {\n\treturn [oldPath, {d: pathStr}, UNIT_ANIM_DUR, STD_EASING];\n}\n", "// Leveraging SMIL Animations\n\nimport { REPLACE_ALL_NEW_DUR } from './animate';\n\nconst EASING = {\n\tease: \"0.25 0.1 0.25 1\",\n\tlinear: \"0 0 1 1\",\n\t// easein: \"0.42 0 1 1\",\n\teasein: \"0.1 0.8 0.2 1\",\n\teaseout: \"0 0 0.58 1\",\n\teaseinout: \"0.42 0 0.58 1\"\n};\n\nfunction animateSVGElement(element, props, dur, easingType=\"linear\", type=undefined, oldValues={}) {\n\n\tlet animElement = element.cloneNode(true);\n\tlet newElement = element.cloneNode(true);\n\n\tfor(var attributeName in props) {\n\t\tlet animateElement;\n\t\tif(attributeName === 'transform') {\n\t\t\tanimateElement = document.createElementNS(\"http://www.w3.org/2000/svg\", \"animateTransform\");\n\t\t} else {\n\t\t\tanimateElement = document.createElementNS(\"http://www.w3.org/2000/svg\", \"animate\");\n\t\t}\n\t\tlet currentValue = oldValues[attributeName] || element.getAttribute(attributeName);\n\t\tlet value = props[attributeName];\n\n\t\tlet animAttr = {\n\t\t\tattributeName: attributeName,\n\t\t\tfrom: currentValue,\n\t\t\tto: value,\n\t\t\tbegin: \"0s\",\n\t\t\tdur: dur/1000 + \"s\",\n\t\t\tvalues: currentValue + \";\" + value,\n\t\t\tkeySplines: EASING[easingType],\n\t\t\tkeyTimes: \"0;1\",\n\t\t\tcalcMode: \"spline\",\n\t\t\tfill: 'freeze'\n\t\t};\n\n\t\tif(type) {\n\t\t\tanimAttr[\"type\"] = type;\n\t\t}\n\n\t\tfor (var i in animAttr) {\n\t\t\tanimateElement.setAttribute(i, animAttr[i]);\n\t\t}\n\n\t\tanimElement.appendChild(animateElement);\n\n\t\tif(type) {\n\t\t\tnewElement.setAttribute(attributeName, `translate(${value})`);\n\t\t} else {\n\t\t\tnewElement.setAttribute(attributeName, value);\n\t\t}\n\t}\n\n\treturn [animElement, newElement];\n}\n\nexport function transform(element, style) { // eslint-disable-line no-unused-vars\n\telement.style.transform = style;\n\telement.style.webkitTransform = style;\n\telement.style.msTransform = style;\n\telement.style.mozTransform = style;\n\telement.style.oTransform = style;\n}\n\nfunction animateSVG(svgContainer, elements) {\n\tlet newElements = [];\n\tlet animElements = [];\n\n\telements.map(element => {\n\t\tlet unit = element[0];\n\t\tlet parent = unit.parentNode;\n\n\t\tlet animElement, newElement;\n\n\t\telement[0] = unit;\n\t\t[animElement, newElement] = animateSVGElement(...element);\n\n\t\tnewElements.push(newElement);\n\t\tanimElements.push([animElement, parent]);\n\n\t\tparent.replaceChild(animElement, unit);\n\t});\n\n\tlet animSvg = svgContainer.cloneNode(true);\n\n\tanimElements.map((animElement, i) => {\n\t\tanimElement[1].replaceChild(newElements[i], animElement[0]);\n\t\telements[i][0] = newElements[i];\n\t});\n\n\treturn animSvg;\n}\n\nexport function runSMILAnimation(parent, svgElement, elementsToAnimate) {\n\tif(elementsToAnimate.length === 0) return;\n\n\tlet animSvgElement = animateSVG(svgElement, elementsToAnimate);\n\tif(svgElement.parentNode == parent) {\n\t\tparent.removeChild(svgElement);\n\t\tparent.appendChild(animSvgElement);\n\n\t}\n\n\t// Replace the new svgElement (data has already been replaced)\n\tsetTimeout(() => {\n\t\tif(animSvgElement.parentNode == parent) {\n\t\t\tparent.removeChild(animSvgElement);\n\t\t\tparent.appendChild(svgElement);\n\t\t}\n\t}, REPLACE_ALL_NEW_DUR);\n}\n", "import { $ } from '../utils/dom';\nimport { CSSTEXT } from '../../css/chartsCss';\n\nexport function downloadFile(filename, data) {\n\tvar a = document.createElement('a');\n\ta.style = \"display: none\";\n\tvar blob = new Blob(data, {type: \"image/svg+xml; charset=utf-8\"});\n\tvar url = window.URL.createObjectURL(blob);\n\ta.href = url;\n\ta.download = filename;\n\tdocument.body.appendChild(a);\n\ta.click();\n\tsetTimeout(function(){\n\t\tdocument.body.removeChild(a);\n\t\twindow.URL.revokeObjectURL(url);\n\t}, 300);\n}\n\nexport function prepareForExport(svg) {\n\tlet clone = svg.cloneNode(true);\n\tclone.classList.add('chart-container');\n\tclone.setAttribute('xmlns', \"http://www.w3.org/2000/svg\");\n\tclone.setAttribute('xmlns:xlink', \"http://www.w3.org/1999/xlink\");\n\tlet styleEl = $.create('style', {\n\t\t'innerHTML': CSSTEXT\n\t});\n\tclone.insertBefore(styleEl, clone.firstChild);\n\n\tlet container = $.create('div');\n\tcontainer.appendChild(clone);\n\n\treturn container.innerHTML;\n}\n", "// Playing around with dates\n\nexport const NO_OF_YEAR_MONTHS = 12;\nexport const NO_OF_DAYS_IN_WEEK = 7;\nexport const DAYS_IN_YEAR = 375;\nexport const NO_OF_MILLIS = 1000;\nexport const SEC_IN_DAY = 86400;\n\nexport const MONTH_NAMES = [\"January\", \"February\", \"March\", \"April\", \"May\",\n\t\"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\nexport const MONTH_NAMES_SHORT = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\",\n\t\"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n\nexport const DAY_NAMES_SHORT = [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"];\nexport const DAY_NAMES = [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\",\n\t\"Thursday\", \"Friday\", \"Saturday\"];\n\n// https://stackoverflow.com/a/11252167/6495043\nfunction treatAsUtc(date) {\n\tlet result = new Date(date);\n\tresult.setMinutes(result.getMinutes() - result.getTimezoneOffset());\n\treturn result;\n}\n\nexport function getYyyyMmDd(date) {\n\tlet dd = date.getDate();\n\tlet mm = date.getMonth() + 1; // getMonth() is zero-based\n\treturn [\n\t\tdate.getFullYear(),\n\t\t(mm>9 ? '' : '0') + mm,\n\t\t(dd>9 ? '' : '0') + dd\n\t].join('-');\n}\n\nexport function clone(date) {\n\treturn new Date(date.getTime());\n}\n\nexport function timestampSec(date) {\n\treturn date.getTime()/NO_OF_MILLIS;\n}\n\nexport function timestampToMidnight(timestamp, roundAhead = false) {\n\tlet midnightTs = Math.floor(timestamp - (timestamp % SEC_IN_DAY));\n\tif(roundAhead) {\n\t\treturn midnightTs + SEC_IN_DAY;\n\t}\n\treturn midnightTs;\n}\n\n// export function getMonthsBetween(startDate, endDate) {}\n\nexport function getWeeksBetween(startDate, endDate) {\n\tlet weekStartDate = setDayToSunday(startDate);\n\treturn Math.ceil(getDaysBetween(weekStartDate, endDate) / NO_OF_DAYS_IN_WEEK);\n}\n\nexport function getDaysBetween(startDate, endDate) {\n\tlet millisecondsPerDay = SEC_IN_DAY * NO_OF_MILLIS;\n\treturn (treatAsUtc(endDate) - treatAsUtc(startDate)) / millisecondsPerDay;\n}\n\nexport function areInSameMonth(startDate, endDate) {\n\treturn startDate.getMonth() === endDate.getMonth()\n\t\t&& startDate.getFullYear() === endDate.getFullYear();\n}\n\nexport function getMonthName(i, short=false) {\n\tlet monthName = MONTH_NAMES[i];\n\treturn short ? monthName.slice(0, 3) : monthName;\n}\n\nexport function getLastDateInMonth (month, year) {\n\treturn new Date(year, month + 1, 0); // 0: last day in previous month\n}\n\n// mutates\nexport function setDayToSunday(date) {\n\tlet newDate = clone(date);\n\tconst day = newDate.getDay();\n\tif(day !== 0) {\n\t\taddDays(newDate, (-1) * day);\n\t}\n\treturn newDate;\n}\n\n// mutates\nexport function addDays(date, numberOfDays) {\n\tdate.setDate(date.getDate() + numberOfDays);\n}\n", "import { makeSVGGroup } from '../utils/draw';\nimport { makeText, makePath, xLine, yLine, yMarker, yRegion, datasetBar, datasetDot, percentageBar, getPaths, heatSquare } from '../utils/draw';\nimport { equilizeNoOfElements } from '../utils/draw-utils';\nimport { translateHoriLine, translateVertLine, animateRegion, animateBar,\n\tanimateDot, animatePath, animatePathStr } from '../utils/animate';\nimport { getMonthName } from '../utils/date-utils';\n\nclass ChartComponent {\n\tconstructor({\n\t\tlayerClass = '',\n\t\tlayerTransform = '',\n\t\tconstants,\n\n\t\tgetData,\n\t\tmakeElements,\n\t\tanimateElements\n\t}) {\n\t\tthis.layerTransform = layerTransform;\n\t\tthis.constants = constants;\n\n\t\tthis.makeElements = makeElements;\n\t\tthis.getData = getData;\n\n\t\tthis.animateElements = animateElements;\n\n\t\tthis.store = [];\n\t\tthis.labels = [];\n\n\t\tthis.layerClass = layerClass;\n\t\tthis.layerClass = typeof(this.layerClass) === 'function'\n\t\t\t? this.layerClass() : this.layerClass;\n\n\t\tthis.refresh();\n\t}\n\n\trefresh(data) {\n\t\tthis.data = data || this.getData();\n\t}\n\n\tsetup(parent) {\n\t\tthis.layer = makeSVGGroup(this.layerClass, this.layerTransform, parent);\n\t}\n\n\tmake() {\n\t\tthis.render(this.data);\n\t\tthis.oldData = this.data;\n\t}\n\n\trender(data) {\n\t\tthis.store = this.makeElements(data);\n\n\t\tthis.layer.textContent = '';\n\t\tthis.store.forEach(element => {\n\t\t\tthis.layer.appendChild(element);\n\t\t});\n\t\tthis.labels.forEach(element => {\n\t\t\tthis.layer.appendChild(element);\n\t\t});\n\t}\n\n\tupdate(animate = true) {\n\t\tthis.refresh();\n\t\tlet animateElements = [];\n\t\tif(animate) {\n\t\t\tanimateElements = this.animateElements(this.data) || [];\n\t\t}\n\t\treturn animateElements;\n\t}\n}\n\nlet componentConfigs = {\n\tdonutSlices: {\n\t\tlayerClass: 'donut-slices',\n\t\tmakeElements(data) {\n\t\t\treturn data.sliceStrings.map((s, i) => {\n\t\t\t\tlet slice = makePath(s, 'donut-path', data.colors[i], 'none', data.strokeWidth);\n\t\t\t\tslice.style.transition = 'transform .3s;';\n\t\t\t\treturn slice;\n\t\t\t});\n\t\t},\n\n\t\tanimateElements(newData) {\n\t\t\treturn this.store.map((slice, i) => animatePathStr(slice, newData.sliceStrings[i]));\n\t\t},\n\t},\n\tpieSlices: {\n\t\tlayerClass: 'pie-slices',\n\t\tmakeElements(data) {\n\t\t\treturn data.sliceStrings.map((s, i) =>{\n\t\t\t\tlet slice = makePath(s, 'pie-path', 'none', data.colors[i]);\n\t\t\t\tslice.style.transition = 'transform .3s;';\n\t\t\t\treturn slice;\n\t\t\t});\n\t\t},\n\n\t\tanimateElements(newData) {\n\t\t\treturn this.store.map((slice, i) =>\n\t\t\t\tanimatePathStr(slice, newData.sliceStrings[i])\n\t\t\t);\n\t\t}\n\t},\n\tpercentageBars: {\n\t\tlayerClass: 'percentage-bars',\n\t\tmakeElements(data) {\n\t\t\treturn data.xPositions.map((x, i) =>{\n\t\t\t\tlet y = 0;\n\t\t\t\tlet bar = percentageBar(x, y, data.widths[i],\n\t\t\t\t\tthis.constants.barHeight, this.constants.barDepth, data.colors[i]);\n\t\t\t\treturn bar;\n\t\t\t});\n\t\t},\n\n\t\tanimateElements(newData) {\n\t\t\tif(newData) return [];\n\t\t}\n\t},\n\tyAxis: {\n\t\tlayerClass: 'y axis',\n\t\tmakeElements(data) {\n\t\t\treturn data.positions.map((position, i) =>\n\t\t\t\tyLine(position, data.labels[i], this.constants.width,\n\t\t\t\t\t{mode: this.constants.mode, pos: this.constants.pos, shortenNumbers: this.constants.shortenNumbers})\n\t\t\t);\n\t\t},\n\n\t\tanimateElements(newData) {\n\t\t\tlet newPos = newData.positions;\n\t\t\tlet newLabels = newData.labels;\n\t\t\tlet oldPos = this.oldData.positions;\n\t\t\tlet oldLabels = this.oldData.labels;\n\n\t\t\t[oldPos, newPos] = equilizeNoOfElements(oldPos, newPos);\n\t\t\t[oldLabels, newLabels] = equilizeNoOfElements(oldLabels, newLabels);\n\n\t\t\tthis.render({\n\t\t\t\tpositions: oldPos,\n\t\t\t\tlabels: newLabels\n\t\t\t});\n\n\t\t\treturn this.store.map((line, i) => {\n\t\t\t\treturn translateHoriLine(\n\t\t\t\t\tline, newPos[i], oldPos[i]\n\t\t\t\t);\n\t\t\t});\n\t\t}\n\t},\n\n\txAxis: {\n\t\tlayerClass: 'x axis',\n\t\tmakeElements(data) {\n\t\t\treturn data.positions.map((position, i) =>\n\t\t\t\txLine(position, data.calcLabels[i], this.constants.height,\n\t\t\t\t\t{mode: this.constants.mode, pos: this.constants.pos})\n\t\t\t);\n\t\t},\n\n\t\tanimateElements(newData) {\n\t\t\tlet newPos = newData.positions;\n\t\t\tlet newLabels = newData.calcLabels;\n\t\t\tlet oldPos = this.oldData.positions;\n\t\t\tlet oldLabels = this.oldData.calcLabels;\n\n\t\t\t[oldPos, newPos] = equilizeNoOfElements(oldPos, newPos);\n\t\t\t[oldLabels, newLabels] = equilizeNoOfElements(oldLabels, newLabels);\n\n\t\t\tthis.render({\n\t\t\t\tpositions: oldPos,\n\t\t\t\tcalcLabels: newLabels\n\t\t\t});\n\n\t\t\treturn this.store.map((line, i) => {\n\t\t\t\treturn translateVertLine(\n\t\t\t\t\tline, newPos[i], oldPos[i]\n\t\t\t\t);\n\t\t\t});\n\t\t}\n\t},\n\n\tyMarkers: {\n\t\tlayerClass: 'y-markers',\n\t\tmakeElements(data) {\n\t\t\treturn data.map(m =>\n\t\t\t\tyMarker(m.position, m.label, this.constants.width,\n\t\t\t\t\t{labelPos: m.options.labelPos, mode: 'span', lineType: 'dashed'})\n\t\t\t);\n\t\t},\n\t\tanimateElements(newData) {\n\t\t\t[this.oldData, newData] = equilizeNoOfElements(this.oldData, newData);\n\n\t\t\tlet newPos = newData.map(d => d.position);\n\t\t\tlet newLabels = newData.map(d => d.label);\n\t\t\tlet newOptions = newData.map(d => d.options);\n\n\t\t\tlet oldPos = this.oldData.map(d => d.position);\n\n\t\t\tthis.render(oldPos.map((pos, i) => {\n\t\t\t\treturn {\n\t\t\t\t\tposition: oldPos[i],\n\t\t\t\t\tlabel: newLabels[i],\n\t\t\t\t\toptions: newOptions[i]\n\t\t\t\t};\n\t\t\t}));\n\n\t\t\treturn this.store.map((line, i) => {\n\t\t\t\treturn translateHoriLine(\n\t\t\t\t\tline, newPos[i], oldPos[i]\n\t\t\t\t);\n\t\t\t});\n\t\t}\n\t},\n\n\tyRegions: {\n\t\tlayerClass: 'y-regions',\n\t\tmakeElements(data) {\n\t\t\treturn data.map(r =>\n\t\t\t\tyRegion(r.startPos, r.endPos, this.constants.width,\n\t\t\t\t\tr.label, {labelPos: r.options.labelPos})\n\t\t\t);\n\t\t},\n\t\tanimateElements(newData) {\n\t\t\t[this.oldData, newData] = equilizeNoOfElements(this.oldData, newData);\n\n\t\t\tlet newPos = newData.map(d => d.endPos);\n\t\t\tlet newLabels = newData.map(d => d.label);\n\t\t\tlet newStarts = newData.map(d => d.startPos);\n\t\t\tlet newOptions = newData.map(d => d.options);\n\n\t\t\tlet oldPos = this.oldData.map(d => d.endPos);\n\t\t\tlet oldStarts = this.oldData.map(d => d.startPos);\n\n\t\t\tthis.render(oldPos.map((pos, i) => {\n\t\t\t\treturn {\n\t\t\t\t\tstartPos: oldStarts[i],\n\t\t\t\t\tendPos: oldPos[i],\n\t\t\t\t\tlabel: newLabels[i],\n\t\t\t\t\toptions: newOptions[i]\n\t\t\t\t};\n\t\t\t}));\n\n\t\t\tlet animateElements = [];\n\n\t\t\tthis.store.map((rectGroup, i) => {\n\t\t\t\tanimateElements = animateElements.concat(animateRegion(\n\t\t\t\t\trectGroup, newStarts[i], newPos[i], oldPos[i]\n\t\t\t\t));\n\t\t\t});\n\n\t\t\treturn animateElements;\n\t\t}\n\t},\n\n\theatDomain: {\n\t\tlayerClass: function() { return 'heat-domain domain-' + this.constants.index; },\n\t\tmakeElements(data) {\n\t\t\tlet {index, colWidth, rowHeight, squareSize, radius, xTranslate} = this.constants;\n\t\t\tlet monthNameHeight = -12;\n\t\t\tlet x = xTranslate, y = 0;\n\n\t\t\tthis.serializedSubDomains = [];\n\n\t\t\tdata.cols.map((week, weekNo) => {\n\t\t\t\tif(weekNo === 1) {\n\t\t\t\t\tthis.labels.push(\n\t\t\t\t\t\tmakeText('domain-name', x, monthNameHeight, getMonthName(index, true).toUpperCase(),\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tfontSize: 9\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tweek.map((day, i) => {\n\t\t\t\t\tif(day.fill) {\n\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t'data-date': day.yyyyMmDd,\n\t\t\t\t\t\t\t'data-value': day.dataValue,\n\t\t\t\t\t\t\t'data-day': i\n\t\t\t\t\t\t};\n\t\t\t\t\t\tlet square = heatSquare('day', x, y, squareSize, radius, day.fill, data);\n\t\t\t\t\t\tthis.serializedSubDomains.push(square);\n\t\t\t\t\t}\n\t\t\t\t\ty += rowHeight;\n\t\t\t\t});\n\t\t\t\ty = 0;\n\t\t\t\tx += colWidth;\n\t\t\t});\n\n\t\t\treturn this.serializedSubDomains;\n\t\t},\n\n\t\tanimateElements(newData) {\n\t\t\tif(newData) return [];\n\t\t}\n\t},\n\n\tbarGraph: {\n\t\tlayerClass: function() { return 'dataset-units dataset-bars dataset-' + this.constants.index; },\n\t\tmakeElements(data) {\n\t\t\tlet c = this.constants;\n\t\t\tthis.unitType = 'bar';\n\t\t\tthis.units = data.yPositions.map((y, j) => {\n\t\t\t\treturn datasetBar(\n\t\t\t\t\tdata.xPositions[j],\n\t\t\t\t\ty,\n\t\t\t\t\tdata.barWidth,\n\t\t\t\t\tc.color,\n\t\t\t\t\tdata.labels[j],\n\t\t\t\t\tj,\n\t\t\t\t\tdata.offsets[j],\n\t\t\t\t\t{\n\t\t\t\t\t\tzeroLine: data.zeroLine,\n\t\t\t\t\t\tbarsWidth: data.barsWidth,\n\t\t\t\t\t\tminHeight: c.minHeight\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t});\n\t\t\treturn this.units;\n\t\t},\n\t\tanimateElements(newData) {\n\t\t\tlet newXPos = newData.xPositions;\n\t\t\tlet newYPos = newData.yPositions;\n\t\t\tlet newOffsets = newData.offsets;\n\t\t\tlet newLabels = newData.labels;\n\n\t\t\tlet oldXPos = this.oldData.xPositions;\n\t\t\tlet oldYPos = this.oldData.yPositions;\n\t\t\tlet oldOffsets = this.oldData.offsets;\n\t\t\tlet oldLabels = this.oldData.labels;\n\n\t\t\t[oldXPos, newXPos] = equilizeNoOfElements(oldXPos, newXPos);\n\t\t\t[oldYPos, newYPos] = equilizeNoOfElements(oldYPos, newYPos);\n\t\t\t[oldOffsets, newOffsets] = equilizeNoOfElements(oldOffsets, newOffsets);\n\t\t\t[oldLabels, newLabels] = equilizeNoOfElements(oldLabels, newLabels);\n\n\t\t\tthis.render({\n\t\t\t\txPositions: oldXPos,\n\t\t\t\tyPositions: oldYPos,\n\t\t\t\toffsets: oldOffsets,\n\t\t\t\tlabels: newLabels,\n\n\t\t\t\tzeroLine: this.oldData.zeroLine,\n\t\t\t\tbarsWidth: this.oldData.barsWidth,\n\t\t\t\tbarWidth: this.oldData.barWidth,\n\t\t\t});\n\n\t\t\tlet animateElements = [];\n\n\t\t\tthis.store.map((bar, i) => {\n\t\t\t\tanimateElements = animateElements.concat(animateBar(\n\t\t\t\t\tbar, newXPos[i], newYPos[i], newData.barWidth, newOffsets[i],\n\t\t\t\t\t{zeroLine: newData.zeroLine}\n\t\t\t\t));\n\t\t\t});\n\n\t\t\treturn animateElements;\n\t\t}\n\t},\n\n\tlineGraph: {\n\t\tlayerClass: function() { return 'dataset-units dataset-line dataset-' + this.constants.index; },\n\t\tmakeElements(data) {\n\t\t\tlet c = this.constants;\n\t\t\tthis.unitType = 'dot';\n\t\t\tthis.paths = {};\n\t\t\tif(!c.hideLine) {\n\t\t\t\tthis.paths = getPaths(\n\t\t\t\t\tdata.xPositions,\n\t\t\t\t\tdata.yPositions,\n\t\t\t\t\tc.color,\n\t\t\t\t\t{\n\t\t\t\t\t\theatline: c.heatline,\n\t\t\t\t\t\tregionFill: c.regionFill,\n\t\t\t\t\t\tspline: c.spline\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tsvgDefs: c.svgDefs,\n\t\t\t\t\t\tzeroLine: data.zeroLine\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.units = [];\n\t\t\tif(!c.hideDots) {\n\t\t\t\tthis.units = data.yPositions.map((y, j) => {\n\t\t\t\t\treturn datasetDot(\n\t\t\t\t\t\tdata.xPositions[j],\n\t\t\t\t\t\ty,\n\t\t\t\t\t\tdata.radius,\n\t\t\t\t\t\tc.color,\n\t\t\t\t\t\t(c.valuesOverPoints ? data.values[j] : ''),\n\t\t\t\t\t\tj\n\t\t\t\t\t);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn Object.values(this.paths).concat(this.units);\n\t\t},\n\t\tanimateElements(newData) {\n\t\t\tlet newXPos = newData.xPositions;\n\t\t\tlet newYPos = newData.yPositions;\n\t\t\tlet newValues = newData.values;\n\n\t\t\tlet oldXPos = this.oldData.xPositions;\n\t\t\tlet oldYPos = this.oldData.yPositions;\n\t\t\tlet oldValues = this.oldData.values;\n\n\t\t\t[oldXPos, newXPos] = equilizeNoOfElements(oldXPos, newXPos);\n\t\t\t[oldYPos, newYPos] = equilizeNoOfElements(oldYPos, newYPos);\n\t\t\t[oldValues, newValues] = equilizeNoOfElements(oldValues, newValues);\n\n\t\t\tthis.render({\n\t\t\t\txPositions: oldXPos,\n\t\t\t\tyPositions: oldYPos,\n\t\t\t\tvalues: newValues,\n\n\t\t\t\tzeroLine: this.oldData.zeroLine,\n\t\t\t\tradius: this.oldData.radius,\n\t\t\t});\n\n\t\t\tlet animateElements = [];\n\n\t\t\tif(Object.keys(this.paths).length) {\n\t\t\t\tanimateElements = animateElements.concat(animatePath(\n\t\t\t\t\tthis.paths, newXPos, newYPos, newData.zeroLine, this.constants.spline));\n\t\t\t}\n\n\t\t\tif(this.units.length) {\n\t\t\t\tthis.units.map((dot, i) => {\n\t\t\t\t\tanimateElements = animateElements.concat(animateDot(\n\t\t\t\t\t\tdot, newXPos[i], newYPos[i]));\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn animateElements;\n\t\t}\n\t}\n};\n\nexport function getComponent(name, constants, getData) {\n\tlet keys = Object.keys(componentConfigs).filter(k => name.includes(k));\n\tlet config = componentConfigs[keys[0]];\n\tObject.assign(config, {\n\t\tconstants: constants,\n\t\tgetData: getData\n\t});\n\treturn new ChartComponent(config);\n}\n", "import { floatTwo } from './helpers';\n\nfunction normalize(x) {\n\t// Calculates mantissa and exponent of a number\n\t// Returns normalized number and exponent\n\t// https://stackoverflow.com/q/9383593/6495043\n\n\tif(x===0) {\n\t\treturn [0, 0];\n\t}\n\tif(isNaN(x)) {\n\t\treturn {mantissa: -6755399441055744, exponent: 972};\n\t}\n\tvar sig = x > 0 ? 1 : -1;\n\tif(!isFinite(x)) {\n\t\treturn {mantissa: sig * 4503599627370496, exponent: 972};\n\t}\n\n\tx = Math.abs(x);\n\tvar exp = Math.floor(Math.log10(x));\n\tvar man = x/Math.pow(10, exp);\n\n\treturn [sig * man, exp];\n}\n\nfunction getChartRangeIntervals(max, min=0) {\n\tlet upperBound = Math.ceil(max);\n\tlet lowerBound = Math.floor(min);\n\tlet range = upperBound - lowerBound;\n\n\tlet noOfParts = range;\n\tlet partSize = 1;\n\n\t// To avoid too many partitions\n\tif(range > 5) {\n\t\tif(range % 2 !== 0) {\n\t\t\tupperBound++;\n\t\t\t// Recalc range\n\t\t\trange = upperBound - lowerBound;\n\t\t}\n\t\tnoOfParts = range/2;\n\t\tpartSize = 2;\n\t}\n\n\t// Special case: 1 and 2\n\tif(range <= 2) {\n\t\tnoOfParts = 4;\n\t\tpartSize = range/noOfParts;\n\t}\n\n\t// Special case: 0\n\tif(range === 0) {\n\t\tnoOfParts = 5;\n\t\tpartSize = 1;\n\t}\n\n\tlet intervals = [];\n\tfor(var i = 0; i <= noOfParts; i++){\n\t\tintervals.push(lowerBound + partSize * i);\n\t}\n\treturn intervals;\n}\n\nfunction getChartIntervals(maxValue, minValue=0) {\n\tlet [normalMaxValue, exponent] = normalize(maxValue);\n\tlet normalMinValue = minValue ? minValue/Math.pow(10, exponent): 0;\n\n\t// Allow only 7 significant digits\n\tnormalMaxValue = normalMaxValue.toFixed(6);\n\n\tlet intervals = getChartRangeIntervals(normalMaxValue, normalMinValue);\n\tintervals = intervals.map(value => value * Math.pow(10, exponent));\n\treturn intervals;\n}\n\nexport function calcChartIntervals(values, withMinimum=false) {\n\t//*** Where the magic happens ***\n\n\t// Calculates best-fit y intervals from given values\n\t// and returns the interval array\n\n\tlet maxValue = Math.max(...values);\n\tlet minValue = Math.min(...values);\n\n\t// Exponent to be used for pretty print\n\tlet exponent = 0, intervals = []; // eslint-disable-line no-unused-vars\n\n\tfunction getPositiveFirstIntervals(maxValue, absMinValue) {\n\t\tlet intervals = getChartIntervals(maxValue);\n\n\t\tlet intervalSize = intervals[1] - intervals[0];\n\n\t\t// Then unshift the negative values\n\t\tlet value = 0;\n\t\tfor(var i = 1; value < absMinValue; i++) {\n\t\t\tvalue += intervalSize;\n\t\t\tintervals.unshift((-1) * value);\n\t\t}\n\t\treturn intervals;\n\t}\n\n\t// CASE I: Both non-negative\n\n\tif(maxValue >= 0 && minValue >= 0) {\n\t\texponent = normalize(maxValue)[1];\n\t\tif(!withMinimum) {\n\t\t\tintervals = getChartIntervals(maxValue);\n\t\t} else {\n\t\t\tintervals = getChartIntervals(maxValue, minValue);\n\t\t}\n\t}\n\n\t// CASE II: Only minValue negative\n\n\telse if(maxValue > 0 && minValue < 0) {\n\t\t// `withMinimum` irrelevant in this case,\n\t\t// We'll be handling both sides of zero separately\n\t\t// (both starting from zero)\n\t\t// Because ceil() and floor() behave differently\n\t\t// in those two regions\n\n\t\tlet absMinValue = Math.abs(minValue);\n\n\t\tif(maxValue >= absMinValue) {\n\t\t\texponent = normalize(maxValue)[1];\n\t\t\tintervals = getPositiveFirstIntervals(maxValue, absMinValue);\n\t\t} else {\n\t\t\t// Mirror: maxValue => absMinValue, then change sign\n\t\t\texponent = normalize(absMinValue)[1];\n\t\t\tlet posIntervals = getPositiveFirstIntervals(absMinValue, maxValue);\n\t\t\tintervals = posIntervals.reverse().map(d => d * (-1));\n\t\t}\n\n\t}\n\n\t// CASE III: Both non-positive\n\n\telse if(maxValue <= 0 && minValue <= 0) {\n\t\t// Mirrored Case I:\n\t\t// Work with positives, then reverse the sign and array\n\n\t\tlet pseudoMaxValue = Math.abs(minValue);\n\t\tlet pseudoMinValue = Math.abs(maxValue);\n\n\t\texponent = normalize(pseudoMaxValue)[1];\n\t\tif(!withMinimum) {\n\t\t\tintervals = getChartIntervals(pseudoMaxValue);\n\t\t} else {\n\t\t\tintervals = getChartIntervals(pseudoMaxValue, pseudoMinValue);\n\t\t}\n\n\t\tintervals = intervals.reverse().map(d => d * (-1));\n\t}\n\n\treturn intervals;\n}\n\nexport function getZeroIndex(yPts) {\n\tlet zeroIndex;\n\tlet interval = getIntervalSize(yPts);\n\tif(yPts.indexOf(0) >= 0) {\n\t\t// the range has a given zero\n\t\t// zero-line on the chart\n\t\tzeroIndex = yPts.indexOf(0);\n\t} else if(yPts[0] > 0) {\n\t\t// Minimum value is positive\n\t\t// zero-line is off the chart: below\n\t\tlet min = yPts[0];\n\t\tzeroIndex = (-1) * min / interval;\n\t} else {\n\t\t// Maximum value is negative\n\t\t// zero-line is off the chart: above\n\t\tlet max = yPts[yPts.length - 1];\n\t\tzeroIndex = (-1) * max / interval + (yPts.length - 1);\n\t}\n\treturn zeroIndex;\n}\n\nexport function getRealIntervals(max, noOfIntervals, min = 0, asc = 1) {\n\tlet range = max - min;\n\tlet part = range * 1.0 / noOfIntervals;\n\tlet intervals = [];\n\n\tfor(var i = 0; i <= noOfIntervals; i++) {\n\t\tintervals.push(min + part * i);\n\t}\n\n\treturn asc ? intervals : intervals.reverse();\n}\n\nexport function getIntervalSize(orderedArray) {\n\treturn orderedArray[1] - orderedArray[0];\n}\n\nexport function getValueRange(orderedArray) {\n\treturn orderedArray[orderedArray.length-1] - orderedArray[0];\n}\n\nexport function scale(val, yAxis) {\n\treturn floatTwo(yAxis.zeroLine - val * yAxis.scaleMultiplier);\n}\n\nexport function isInRange(val, min, max) {\n\treturn val > min && val < max;\n}\n\nexport function isInRange2D(coord, minCoord, maxCoord) {\n\treturn isInRange(coord[0], minCoord[0], maxCoord[0])\n\t\t&& isInRange(coord[1], minCoord[1], maxCoord[1]);\n}\n\nexport function getClosestInArray(goal, arr, index = false) {\n\tlet closest = arr.reduce(function(prev, curr) {\n\t\treturn (Math.abs(curr - goal) < Math.abs(prev - goal) ? curr : prev);\n\t}, []);\n\n\treturn index ? arr.indexOf(closest) : closest;\n}\n\nexport function calcDistribution(values, distributionSize) {\n\t// Assume non-negative values,\n\t// implying distribution minimum at zero\n\n\tlet dataMaxValue = Math.max(...values);\n\n\tlet distributionStep = 1 / (distributionSize - 1);\n\tlet distribution = [];\n\n\tfor(var i = 0; i < distributionSize; i++) {\n\t\tlet checkpoint = dataMaxValue * (distributionStep * i);\n\t\tdistribution.push(checkpoint);\n\t}\n\n\treturn distribution;\n}\n\nexport function getMaxCheckpoint(value, distribution) {\n\treturn distribution.filter(d => d < value).length;\n}\n", "import { fillArray } from '../utils/helpers';\nimport { DEFAULT_AXIS_CHART_TYPE, AXIS_DATASET_CHART_TYPES, DEFAULT_CHAR_WIDTH } from '../utils/constants';\n\nexport function dataPrep(data, type) {\n\tdata.labels = data.labels || [];\n\n\tlet datasetLength = data.labels.length;\n\n\t// Datasets\n\tlet datasets = data.datasets;\n\tlet zeroArray = new Array(datasetLength).fill(0);\n\tif(!datasets) {\n\t\t// default\n\t\tdatasets = [{\n\t\t\tvalues: zeroArray\n\t\t}];\n\t}\n\n\tdatasets.map(d=> {\n\t\t// Set values\n\t\tif(!d.values) {\n\t\t\td.values = zeroArray;\n\t\t} else {\n\t\t\t// Check for non values\n\t\t\tlet vals = d.values;\n\t\t\tvals = vals.map(val => (!isNaN(val) ? val : 0));\n\n\t\t\t// Trim or extend\n\t\t\tif(vals.length > datasetLength) {\n\t\t\t\tvals = vals.slice(0, datasetLength);\n\t\t\t} else {\n\t\t\t\tvals = fillArray(vals, datasetLength - vals.length, 0);\n\t\t\t}\n\t\t}\n\n\t\t// Set labels\n\t\t//\n\n\t\t// Set type\n\t\tif(!d.chartType ) {\n\t\t\tif(!AXIS_DATASET_CHART_TYPES.includes(type)) type === DEFAULT_AXIS_CHART_TYPE;\n\t\t\td.chartType = type;\n\t\t}\n\n\t});\n\n\t// Markers\n\n\t// Regions\n\t// data.yRegions = data.yRegions || [];\n\tif(data.yRegions) {\n\t\tdata.yRegions.map(d => {\n\t\t\tif(d.end < d.start) {\n\t\t\t\t[d.start, d.end] = [d.end, d.start];\n\t\t\t}\n\t\t});\n\t}\n\n\treturn data;\n}\n\nexport function zeroDataPrep(realData) {\n\tlet datasetLength = realData.labels.length;\n\tlet zeroArray = new Array(datasetLength).fill(0);\n\n\tlet zeroData = {\n\t\tlabels: realData.labels.slice(0, -1),\n\t\tdatasets: realData.datasets.map(d => {\n\t\t\treturn {\n\t\t\t\tname: '',\n\t\t\t\tvalues: zeroArray.slice(0, -1),\n\t\t\t\tchartType: d.chartType\n\t\t\t};\n\t\t}),\n\t};\n\n\tif(realData.yMarkers) {\n\t\tzeroData.yMarkers = [\n\t\t\t{\n\t\t\t\tvalue: 0,\n\t\t\t\tlabel: ''\n\t\t\t}\n\t\t];\n\t}\n\n\tif(realData.yRegions) {\n\t\tzeroData.yRegions = [\n\t\t\t{\n\t\t\t\tstart: 0,\n\t\t\t\tend: 0,\n\t\t\t\tlabel: ''\n\t\t\t}\n\t\t];\n\t}\n\n\treturn zeroData;\n}\n\nexport function getShortenedLabels(chartWidth, labels=[], isSeries=true) {\n\tlet allowedSpace = chartWidth / labels.length;\n\tif(allowedSpace <= 0) allowedSpace = 1;\n\tlet allowedLetters = allowedSpace / DEFAULT_CHAR_WIDTH;\n\n\tlet seriesMultiple;\n\tif(isSeries) {\n\t\t// Find the maximum label length for spacing calculations\n\t\tlet maxLabelLength = Math.max(...labels.map(label => label.length));\n\t\tseriesMultiple = Math.ceil(maxLabelLength/allowedLetters);\n\t}\n\n\tlet calcLabels = labels.map((label, i) => {\n\t\tlabel += \"\";\n\t\tif(label.length > allowedLetters) {\n\n\t\t\tif(!isSeries) {\n\t\t\t\tif(allowedLetters-3 > 0) {\n\t\t\t\t\tlabel = label.slice(0, allowedLetters-3) + \" ...\";\n\t\t\t\t} else {\n\t\t\t\t\tlabel = label.slice(0, allowedLetters) + '..';\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif(i % seriesMultiple !== 0) {\n\t\t\t\t\tlabel = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn label;\n\t});\n\n\treturn calcLabels;\n}\n", "import '../css/charts.scss';\n\n// import MultiAxis<PERSON>hart from './charts/MultiAxisChart';\nimport Percentage<PERSON><PERSON> from './charts/PercentageChart';\nimport <PERSON><PERSON><PERSON> from './charts/PieChart';\nimport Heatmap from './charts/Heatmap';\nimport Axis<PERSON>hart from './charts/AxisChart';\nimport Donut<PERSON>hart from './charts/DonutChart';\n\nconst chartTypes = {\n\tbar: <PERSON><PERSON><PERSON>,\n\tline: Axis<PERSON>hart,\n\t// multiaxis: MultiAxisChart,\n\tpercentage: Percentage<PERSON><PERSON>,\n\theatmap: Heatmap,\n\tpie: PieChart,\n\tdonut: Donut<PERSON>hart,\n};\n\nfunction getChartByType(chartType = 'line', parent, options) {\n\tif (chartType === 'axis-mixed') {\n\t\toptions.type = 'line';\n\t\treturn new AxisChart(parent, options);\n\t}\n\n\tif (!chartTypes[chartType]) {\n\t\tconsole.error(\"Undefined chart type: \" + chartType);\n\t\treturn;\n\t}\n\n\treturn new chartTypes[chartType](parent, options);\n}\n\nclass Chart {\n\tconstructor(parent, options) {\n\t\treturn getChartByType(options.type, parent, options);\n\t}\n}\n\nexport { Chart, Percent<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> };", "function styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nexport default styleInject;\n", "import { $ } from '../utils/dom';\nimport { TOOLTIP_POINTER_TRIANGLE_HEIGHT } from '../utils/constants';\n\nexport default class SvgTip {\n\tconstructor({\n\t\tparent = null,\n\t\tcolors = []\n\t}) {\n\t\tthis.parent = parent;\n\t\tthis.colors = colors;\n\t\tthis.titleName = '';\n\t\tthis.titleValue = '';\n\t\tthis.listValues = [];\n\t\tthis.titleValueFirst = 0;\n\n\t\tthis.x = 0;\n\t\tthis.y = 0;\n\n\t\tthis.top = 0;\n\t\tthis.left = 0;\n\n\t\tthis.setup();\n\t}\n\n\tsetup() {\n\t\tthis.makeTooltip();\n\t}\n\n\trefresh() {\n\t\tthis.fill();\n\t\tthis.calcPosition();\n\t}\n\n\tmakeTooltip() {\n\t\tthis.container = $.create('div', {\n\t\t\tinside: this.parent,\n\t\t\tclassName: 'graph-svg-tip comparison',\n\t\t\tinnerHTML: `<span class=\"title\"></span>\n\t\t\t\t<ul class=\"data-point-list\"></ul>\n\t\t\t\t<div class=\"svg-pointer\"></div>`\n\t\t});\n\t\tthis.hideTip();\n\n\t\tthis.title = this.container.querySelector('.title');\n\t\tthis.dataPointList = this.container.querySelector('.data-point-list');\n\n\t\tthis.parent.addEventListener('mouseleave', () => {\n\t\t\tthis.hideTip();\n\t\t});\n\t}\n\n\tfill() {\n\t\tlet title;\n\t\tif(this.index) {\n\t\t\tthis.container.setAttribute('data-point-index', this.index);\n\t\t}\n\t\tif(this.titleValueFirst) {\n\t\t\ttitle = `<strong>${this.titleValue}</strong>${this.titleName}`;\n\t\t} else {\n\t\t\ttitle = `${this.titleName}<strong>${this.titleValue}</strong>`;\n\t\t}\n\t\tthis.title.innerHTML = title;\n\t\tthis.dataPointList.innerHTML = '';\n\n\t\tthis.listValues.map((set, i) => {\n\t\t\tconst color = this.colors[i] || 'black';\n\t\t\tlet value = set.formatted === 0 || set.formatted ? set.formatted : set.value;\n\n\t\t\tlet li = $.create('li', {\n\t\t\t\tstyles: {\n\t\t\t\t\t'border-top': `3px solid ${color}`\n\t\t\t\t},\n\t\t\t\tinnerHTML: `<strong style=\"display: block;\">${ value === 0 || value ? value : '' }</strong>\n\t\t\t\t\t${set.title ? set.title : '' }`\n\t\t\t});\n\n\t\t\tthis.dataPointList.appendChild(li);\n\t\t});\n\t}\n\n\tcalcPosition() {\n\t\tlet width = this.container.offsetWidth;\n\n\t\tthis.top = this.y - this.container.offsetHeight\n\t\t\t- TOOLTIP_POINTER_TRIANGLE_HEIGHT;\n\t\tthis.left = this.x - width/2;\n\t\tlet maxLeft = this.parent.offsetWidth - width;\n\n\t\tlet pointer = this.container.querySelector('.svg-pointer');\n\n\t\tif(this.left < 0) {\n\t\t\tpointer.style.left = `calc(50% - ${-1 * this.left}px)`;\n\t\t\tthis.left = 0;\n\t\t} else if(this.left > maxLeft) {\n\t\t\tlet delta = this.left - maxLeft;\n\t\t\tlet pointerOffset = `calc(50% + ${delta}px)`;\n\t\t\tpointer.style.left = pointerOffset;\n\n\t\t\tthis.left = maxLeft;\n\t\t} else {\n\t\t\tpointer.style.left = `50%`;\n\t\t}\n\t}\n\n\tsetValues(x, y, title = {}, listValues = [], index = -1) {\n\t\tthis.titleName = title.name;\n\t\tthis.titleValue = title.value;\n\t\tthis.listValues = listValues;\n\t\tthis.x = x;\n\t\tthis.y = y;\n\t\tthis.titleValueFirst = title.valueFirst || 0;\n\t\tthis.index = index;\n\t\tthis.refresh();\n\t}\n\n\thideTip() {\n\t\tthis.container.style.top = '0px';\n\t\tthis.container.style.left = '0px';\n\t\tthis.container.style.opacity = '0';\n\t}\n\n\tshowTip() {\n\t\tthis.container.style.top = this.top + 'px';\n\t\tthis.container.style.left = this.left + 'px';\n\t\tthis.container.style.opacity = '1';\n\t}\n}\n", "export const CSSTEXT = \".chart-container{position:relative;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI','Robot<PERSON>','Oxygen','Ubunt<PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON> Sans','Droid Sans','Helvetica Neue',sans-serif}.chart-container .axis,.chart-container .chart-label{fill:#555b51}.chart-container .axis line,.chart-container .chart-label line{stroke:#dadada}.chart-container .dataset-units circle{stroke:#fff;stroke-width:2}.chart-container .dataset-units path{fill:none;stroke-opacity:1;stroke-width:2px}.chart-container .dataset-path{stroke-width:2px}.chart-container .path-group path{fill:none;stroke-opacity:1;stroke-width:2px}.chart-container line.dashed{stroke-dasharray:5,3}.chart-container .axis-line .specific-value{text-anchor:start}.chart-container .axis-line .y-line{text-anchor:end}.chart-container .axis-line .x-line{text-anchor:middle}.chart-container .legend-dataset-text{fill:#6c7680;font-weight:600}.graph-svg-tip{position:absolute;z-index:99999;padding:10px;font-size:12px;color:#959da5;text-align:center;background:rgba(0,0,0,.8);border-radius:3px}.graph-svg-tip ul{padding-left:0;display:flex}.graph-svg-tip ol{padding-left:0;display:flex}.graph-svg-tip ul.data-point-list li{min-width:90px;flex:1;font-weight:600}.graph-svg-tip strong{color:#dfe2e5;font-weight:600}.graph-svg-tip .svg-pointer{position:absolute;height:5px;margin:0 0 0 -5px;content:' ';border:5px solid transparent;border-top-color:rgba(0,0,0,.8)}.graph-svg-tip.comparison{padding:0;text-align:left;pointer-events:none}.graph-svg-tip.comparison .title{display:block;padding:10px;margin:0;font-weight:600;line-height:1;pointer-events:none}.graph-svg-tip.comparison ul{margin:0;white-space:nowrap;list-style:none}.graph-svg-tip.comparison li{display:inline-block;padding:5px 10px}\";", "import SvgTip from '../objects/SvgTip';\nimport { $, isElementInViewport, getElementContentWidth, isHidden } from '../utils/dom';\nimport { makeSVGContainer, makeSVGDefs, makeSVGGroup, makeText } from '../utils/draw';\nimport { BASE_MEASURES, getExtraHeight, getExtraWidth, getTopOffset, getLeftOffset,\n\tINIT_CHART_UPDATE_TIMEOUT, CHART_POST_ANIMATE_TIMEOUT, DEFAULT_COLORS} from '../utils/constants';\nimport { getColor, isValidColor } from '../utils/colors';\nimport { runSMILAnimation } from '../utils/animation';\nimport { downloadFile, prepareForExport } from '../utils/export';\n\nexport default class BaseChart {\n\tconstructor(parent, options) {\n\n\t\tthis.parent = typeof parent === 'string'\n\t\t\t? document.querySelector(parent)\n\t\t\t: parent;\n\n\t\tif (!(this.parent instanceof HTMLElement)) {\n\t\t\tthrow new Error('No `parent` element to render on was provided.');\n\t\t}\n\n\t\tthis.rawChartArgs = options;\n\n\t\tthis.title = options.title || '';\n\t\tthis.type = options.type || '';\n\n\t\tthis.realData = this.prepareData(options.data);\n\t\tthis.data = this.prepareFirstData(this.realData);\n\n\t\tthis.colors = this.validateColors(options.colors, this.type);\n\n\t\tthis.config = {\n\t\t\tshowTooltip: 1, // calculate\n\t\t\tshowLegend: 1, // calculate\n\t\t\tisNavigable: options.isNavigable || 0,\n\t\t\tanimate: (typeof options.animate !== 'undefined') ? options.animate : 1,\n\t\t\ttruncateLegends: options.truncateLegends || 1\n\t\t};\n\n\t\tthis.measures = JSON.parse(JSON.stringify(BASE_MEASURES));\n\t\tlet m = this.measures;\n\t\tthis.setMeasures(options);\n\t\tif(!this.title.length) { m.titleHeight = 0; }\n\t\tif(!this.config.showLegend) m.legendHeight = 0;\n\t\tthis.argHeight = options.height || m.baseHeight;\n\n\t\tthis.state = {};\n\t\tthis.options = {};\n\n\t\tthis.initTimeout = INIT_CHART_UPDATE_TIMEOUT;\n\n\t\tif(this.config.isNavigable) {\n\t\t\tthis.overlays = [];\n\t\t}\n\n\t\tthis.configure(options);\n\t}\n\n\tprepareData(data) {\n\t\treturn data;\n\t}\n\n\tprepareFirstData(data) {\n\t\treturn data;\n\t}\n\n\tvalidateColors(colors, type) {\n\t\tconst validColors = [];\n\t\tcolors = (colors || []).concat(DEFAULT_COLORS[type]);\n\t\tcolors.forEach((string) => {\n\t\t\tconst color = getColor(string);\n\t\t\tif(!isValidColor(color)) {\n\t\t\t\tconsole.warn('\"' + string + '\" is not a valid color.');\n\t\t\t} else {\n\t\t\t\tvalidColors.push(color);\n\t\t\t}\n\t\t});\n\t\treturn validColors;\n\t}\n\n\tsetMeasures() {\n\t\t// Override measures, including those for title and legend\n\t\t// set config for legend and title\n\t}\n\n\tconfigure() {\n\t\tlet height = this.argHeight;\n\t\tthis.baseHeight = height;\n\t\tthis.height = height - getExtraHeight(this.measures);\n\n\t\t// Bind window events\n\t\tthis.boundDrawFn = () => this.draw(true);\n\t\twindow.addEventListener('resize', this.boundDrawFn);\n\t\twindow.addEventListener('orientationchange', this.boundDrawFn);\n\t}\n\n\tdestroy() {\n\t\twindow.removeEventListener('resize', this.boundDrawFn);\n\t\twindow.removeEventListener('orientationchange', this.boundDrawFn);\n\t}\n\n\t// Has to be called manually\n\tsetup() {\n\t\tthis.makeContainer();\n\t\tthis.updateWidth();\n\t\tthis.makeTooltip();\n\n\t\tthis.draw(false, true);\n\t}\n\n\tmakeContainer() {\n\t\t// Chart needs a dedicated parent element\n\t\tthis.parent.innerHTML = '';\n\n\t\tlet args = {\n\t\t\tinside: this.parent,\n\t\t\tclassName: 'chart-container'\n\t\t};\n\n\t\tif(this.independentWidth) {\n\t\t\targs.styles = { width: this.independentWidth + 'px' };\n\t\t}\n\n\t\tthis.container = $.create('div', args);\n\t}\n\n\tmakeTooltip() {\n\t\tthis.tip = new SvgTip({\n\t\t\tparent: this.container,\n\t\t\tcolors: this.colors\n\t\t});\n\t\tthis.bindTooltip();\n\t}\n\n\tbindTooltip() {}\n\n\tdraw(onlyWidthChange=false, init=false) {\n\t\tif (onlyWidthChange && isHidden(this.parent)) {\n\t\t\t// Don't update anything if the chart is hidden\n\t\t\treturn;\n\t\t}\n\t\tthis.updateWidth();\n\n\t\tthis.calc(onlyWidthChange);\n\t\tthis.makeChartArea();\n\t\tthis.setupComponents();\n\n\t\tthis.components.forEach(c => c.setup(this.drawArea));\n\t\t// this.components.forEach(c => c.make());\n\t\tthis.render(this.components, false);\n\n\t\tif(init) {\n\t\t\tthis.data = this.realData;\n\t\t\tsetTimeout(() => {this.update(this.data);}, this.initTimeout);\n\t\t}\n\n\t\tthis.renderLegend();\n\n\t\tthis.setupNavigation(init);\n\t}\n\n\tcalc() {} // builds state\n\n\tupdateWidth() {\n\t\tthis.baseWidth = getElementContentWidth(this.parent);\n\t\tthis.width = this.baseWidth - getExtraWidth(this.measures);\n\t}\n\n\tmakeChartArea() {\n\t\tif(this.svg) {\n\t\t\tthis.container.removeChild(this.svg);\n\t\t}\n\t\tlet m = this.measures;\n\n\t\tthis.svg = makeSVGContainer(\n\t\t\tthis.container,\n\t\t\t'frappe-chart chart',\n\t\t\tthis.baseWidth,\n\t\t\tthis.baseHeight\n\t\t);\n\t\tthis.svgDefs = makeSVGDefs(this.svg);\n\n\t\tif(this.title.length) {\n\t\t\tthis.titleEL = makeText(\n\t\t\t\t'title',\n\t\t\t\tm.margins.left,\n\t\t\t\tm.margins.top,\n\t\t\t\tthis.title,\n\t\t\t\t{\n\t\t\t\t\tfontSize: m.titleFontSize,\n\t\t\t\t\tfill: '#666666',\n\t\t\t\t\tdy: m.titleFontSize\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tlet top = getTopOffset(m);\n\t\tthis.drawArea = makeSVGGroup(\n\t\t\tthis.type + '-chart chart-draw-area',\n\t\t\t`translate(${getLeftOffset(m)}, ${top})`\n\t\t);\n\n\t\tif(this.config.showLegend) {\n\t\t\ttop += this.height + m.paddings.bottom;\n\t\t\tthis.legendArea = makeSVGGroup(\n\t\t\t\t'chart-legend',\n\t\t\t\t`translate(${getLeftOffset(m)}, ${top})`\n\t\t\t);\n\t\t}\n\n\t\tif(this.title.length) { this.svg.appendChild(this.titleEL); }\n\t\tthis.svg.appendChild(this.drawArea);\n\t\tif(this.config.showLegend) { this.svg.appendChild(this.legendArea); }\n\n\t\tthis.updateTipOffset(getLeftOffset(m), getTopOffset(m));\n\t}\n\n\tupdateTipOffset(x, y) {\n\t\tthis.tip.offset = {\n\t\t\tx: x,\n\t\t\ty: y\n\t\t};\n\t}\n\n\tsetupComponents() { this.components = new Map(); }\n\n\tupdate(data) {\n\t\tif(!data) {\n\t\t\tconsole.error('No data to update.');\n\t\t}\n\t\tthis.data = this.prepareData(data);\n\t\tthis.calc(); // builds state\n\t\tthis.render(this.components, this.config.animate);\n\t}\n\n\trender(components=this.components, animate=true) {\n\t\tif(this.config.isNavigable) {\n\t\t\t// Remove all existing overlays\n\t\t\tthis.overlays.map(o => o.parentNode.removeChild(o));\n\t\t\t// ref.parentNode.insertBefore(element, ref);\n\t\t}\n\t\tlet elementsToAnimate = [];\n\t\t// Can decouple to this.refreshComponents() first to save animation timeout\n\t\tcomponents.forEach(c => {\n\t\t\telementsToAnimate = elementsToAnimate.concat(c.update(animate));\n\t\t});\n\t\tif(elementsToAnimate.length > 0) {\n\t\t\trunSMILAnimation(this.container, this.svg, elementsToAnimate);\n\t\t\tsetTimeout(() => {\n\t\t\t\tcomponents.forEach(c => c.make());\n\t\t\t\tthis.updateNav();\n\t\t\t}, CHART_POST_ANIMATE_TIMEOUT);\n\t\t} else {\n\t\t\tcomponents.forEach(c => c.make());\n\t\t\tthis.updateNav();\n\t\t}\n\t}\n\n\tupdateNav() {\n\t\tif(this.config.isNavigable) {\n\t\t\tthis.makeOverlay();\n\t\t\tthis.bindUnits();\n\t\t}\n\t}\n\n\trenderLegend() {}\n\n\tsetupNavigation(init=false) {\n\t\tif(!this.config.isNavigable) return;\n\n\t\tif(init) {\n\t\t\tthis.bindOverlay();\n\n\t\t\tthis.keyActions = {\n\t\t\t\t'13': this.onEnterKey.bind(this),\n\t\t\t\t'37': this.onLeftArrow.bind(this),\n\t\t\t\t'38': this.onUpArrow.bind(this),\n\t\t\t\t'39': this.onRightArrow.bind(this),\n\t\t\t\t'40': this.onDownArrow.bind(this),\n\t\t\t};\n\n\t\t\tdocument.addEventListener('keydown', (e) => {\n\t\t\t\tif(isElementInViewport(this.container)) {\n\t\t\t\t\te = e || window.event;\n\t\t\t\t\tif(this.keyActions[e.keyCode]) {\n\t\t\t\t\t\tthis.keyActions[e.keyCode]();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n\n\tmakeOverlay() {}\n\tupdateOverlay() {}\n\tbindOverlay() {}\n\tbindUnits() {}\n\n\tonLeftArrow() {}\n\tonRightArrow() {}\n\tonUpArrow() {}\n\tonDownArrow() {}\n\tonEnterKey() {}\n\n\taddDataPoint() {}\n\tremoveDataPoint() {}\n\n\tgetDataPoint() {}\n\tsetCurrentDataPoint() {}\n\n\tupdateDataset() {}\n\n\texport() {\n\t\tlet chartSvg = prepareForExport(this.svg);\n\t\tdownloadFile(this.title || 'Chart', [chartSvg]);\n\t}\n}\n", "import BaseChart from './BaseChart';\nimport { truncateString } from '../utils/draw-utils';\nimport { legendDot } from '../utils/draw';\nimport { round } from '../utils/helpers';\nimport { getExtraWidth } from '../utils/constants';\n\nexport default class AggregationChart extends BaseChart {\n\tconstructor(parent, args) {\n\t\tsuper(parent, args);\n\t}\n\n\tconfigure(args) {\n\t\tsuper.configure(args);\n\n\t\tthis.config.formatTooltipY = (args.tooltipOptions || {}).formatTooltipY;\n\t\tthis.config.maxSlices = args.maxSlices || 20;\n\t\tthis.config.maxLegendPoints = args.maxLegendPoints || 20;\n\t}\n\n\tcalc() {\n\t\tlet s = this.state;\n\t\tlet maxSlices = this.config.maxSlices;\n\t\ts.sliceTotals = [];\n\n\t\tlet allTotals = this.data.labels.map((label, i) => {\n\t\t\tlet total = 0;\n\t\t\tthis.data.datasets.map(e => {\n\t\t\t\ttotal += e.values[i];\n\t\t\t});\n\t\t\treturn [total, label];\n\t\t}).filter(d => { return d[0] >= 0; }); // keep only positive results\n\n\t\tlet totals = allTotals;\n\t\tif(allTotals.length > maxSlices) {\n\t\t\t// Prune and keep a grey area for rest as per maxSlices\n\t\t\tallTotals.sort((a, b) => { return b[0] - a[0]; });\n\n\t\t\ttotals = allTotals.slice(0, maxSlices-1);\n\t\t\tlet remaining = allTotals.slice(maxSlices-1);\n\n\t\t\tlet sumOfRemaining = 0;\n\t\t\tremaining.map(d => {sumOfRemaining += d[0];});\n\t\t\ttotals.push([sumOfRemaining, 'Rest']);\n\t\t\tthis.colors[maxSlices-1] = 'grey';\n\t\t}\n\n\t\ts.labels = [];\n\t\ttotals.map(d => {\n\t\t\ts.sliceTotals.push(round(d[0]));\n\t\t\ts.labels.push(d[1]);\n\t\t});\n\n\t\ts.grandTotal = s.sliceTotals.reduce((a, b) => a + b, 0);\n\n\t\tthis.center = {\n\t\t\tx: this.width / 2,\n\t\t\ty: this.height / 2\n\t\t};\n\t}\n\n\trenderLegend() {\n\t\tlet s = this.state;\n\t\tthis.legendArea.textContent = '';\n\t\tthis.legendTotals = s.sliceTotals.slice(0, this.config.maxLegendPoints);\n\n\t\tlet count = 0;\n\t\tlet y = 0;\n\t\tthis.legendTotals.map((d, i) => {\n\t\t\tlet barWidth = 150;\n\t\t\tlet divisor = Math.floor(\n\t\t\t\t(this.width - getExtraWidth(this.measures))/barWidth\n\t\t\t);\n\t\t\tif (this.legendTotals.length < divisor) {\n\t\t\t\tbarWidth = this.width/this.legendTotals.length;\n\t\t\t}\n\t\t\tif(count > divisor) {\n\t\t\t\tcount = 0;\n\t\t\t\ty += 20;\n\t\t\t}\n\t\t\tlet x = barWidth * count + 5;\n\t\t\tlet label = this.config.truncateLegends ? truncateString(s.labels[i], barWidth/10) : s.labels[i];\n\t\t\tlet formatted = this.config.formatTooltipY ? this.config.formatTooltipY(d) : d;\n\t\t\tlet dot = legendDot(\n\t\t\t\tx,\n\t\t\t\ty,\n\t\t\t\t5,\n\t\t\t\tthis.colors[i],\n\t\t\t\t`${label}: ${formatted}`,\n\t\t\t\tfalse\n\t\t\t);\n\t\t\tthis.legendArea.appendChild(dot);\n\t\t\tcount++;\n\t\t});\n\t}\n}\n", "import AggregationChart from './AggregationChart';\nimport { getOffset } from '../utils/dom';\nimport { getComponent } from '../objects/ChartComponents';\nimport { PERCENTAGE_BAR_DEFAULT_HEIGHT, PERCENTAGE_BAR_DEFAULT_DEPTH } from '../utils/constants';\n\nexport default class PercentageChart extends AggregationChart {\n\tconstructor(parent, args) {\n\t\tsuper(parent, args);\n\t\tthis.type = 'percentage';\n\t\tthis.setup();\n\t}\n\n\tsetMeasures(options) {\n\t\tlet m = this.measures;\n\t\tthis.barOptions = options.barOptions || {};\n\n\t\tlet b = this.barOptions;\n\t\tb.height = b.height || PERCENTAGE_BAR_DEFAULT_HEIGHT;\n\t\tb.depth = b.depth || PERCENTAGE_BAR_DEFAULT_DEPTH;\n\n\t\tm.paddings.right = 30;\n\t\tm.legendHeight = 60;\n\t\tm.baseHeight = (b.height + b.depth * 0.5) * 8;\n\t}\n\n\tsetupComponents() {\n\t\tlet s = this.state;\n\n\t\tlet componentConfigs = [\n\t\t\t[\n\t\t\t\t'percentageBars',\n\t\t\t\t{\n\t\t\t\t\tbarHeight: this.barOptions.height,\n\t\t\t\t\tbarDepth: this.barOptions.depth,\n\t\t\t\t},\n\t\t\t\tfunction() {\n\t\t\t\t\treturn {\n\t\t\t\t\t\txPositions: s.xPositions,\n\t\t\t\t\t\twidths: s.widths,\n\t\t\t\t\t\tcolors: this.colors\n\t\t\t\t\t};\n\t\t\t\t}.bind(this)\n\t\t\t]\n\t\t];\n\n\t\tthis.components = new Map(componentConfigs\n\t\t\t.map(args => {\n\t\t\t\tlet component = getComponent(...args);\n\t\t\t\treturn [args[0], component];\n\t\t\t}));\n\t}\n\n\tcalc() {\n\t\tsuper.calc();\n\t\tlet s = this.state;\n\n\t\ts.xPositions = [];\n\t\ts.widths = [];\n\n\t\tlet xPos = 0;\n\t\ts.sliceTotals.map((value) => {\n\t\t\tlet width = this.width * value / s.grandTotal;\n\t\t\ts.widths.push(width);\n\t\t\ts.xPositions.push(xPos);\n\t\t\txPos += width;\n\t\t});\n\t}\n\n\tmakeDataByIndex() { }\n\n\tbindTooltip() {\n\t\tlet s = this.state;\n\t\tthis.container.addEventListener('mousemove', (e) => {\n\t\t\tlet bars = this.components.get('percentageBars').store;\n\t\t\tlet bar = e.target;\n\t\t\tif(bars.includes(bar)) {\n\n\t\t\t\tlet i = bars.indexOf(bar);\n\t\t\t\tlet gOff = getOffset(this.container), pOff = getOffset(bar);\n\n\t\t\t\tlet x = pOff.left - gOff.left + parseInt(bar.getAttribute('width'))/2;\n\t\t\t\tlet y = pOff.top - gOff.top;\n\t\t\t\tlet title = (this.formattedLabels && this.formattedLabels.length>0\n\t\t\t\t\t? this.formattedLabels[i] : this.state.labels[i]) + ': ';\n\t\t\t\tlet fraction = s.sliceTotals[i]/s.grandTotal;\n\n\t\t\t\tthis.tip.setValues(x, y, {name: title, value: (fraction*100).toFixed(1) + \"%\"});\n\t\t\t\tthis.tip.showTip();\n\t\t\t}\n\t\t});\n\t}\n}\n", "import AggregationChart from './AggregationChart';\nimport { getComponent } from '../objects/ChartComponents';\nimport { getOffset } from '../utils/dom';\nimport { getPositionByAngle } from '../utils/helpers';\nimport { makeArcPathStr, makeCircleStr } from '../utils/draw';\nimport { lightenDarkenColor } from '../utils/colors';\nimport { transform } from '../utils/animation';\nimport { FULL_ANGLE } from '../utils/constants';\n\nexport default class PieChart extends AggregationChart {\n\tconstructor(parent, args) {\n\t\tsuper(parent, args);\n\t\tthis.type = 'pie';\n\t\tthis.initTimeout = 0;\n\t\tthis.init = 1;\n\n\t\tthis.setup();\n\t}\n\n\tconfigure(args) {\n\t\tsuper.configure(args);\n\t\tthis.mouseMove = this.mouseMove.bind(this);\n\t\tthis.mouseLeave = this.mouseLeave.bind(this);\n\n\t\tthis.hoverRadio = args.hoverRadio || 0.1;\n\t\tthis.config.startAngle = args.startAngle || 0;\n\n\t\tthis.clockWise = args.clockWise || false;\n\t}\n\n\tcalc() {\n\t\tsuper.calc();\n\t\tlet s = this.state;\n\t\tthis.radius = (this.height > this.width ? this.center.x : this.center.y);\n\n\t\tconst { radius, clockWise } = this;\n\n\t\tconst prevSlicesProperties = s.slicesProperties || [];\n\t\ts.sliceStrings = [];\n\t\ts.slicesProperties = [];\n\t\tlet curAngle = 180 - this.config.startAngle;\n\t\ts.sliceTotals.map((total, i) => {\n\t\t\tconst startAngle = curAngle;\n\t\t\tconst originDiffAngle = (total / s.grandTotal) * FULL_ANGLE;\n\t\t\tconst largeArc = originDiffAngle > 180 ? 1: 0;\n\t\t\tconst diffAngle = clockWise ? -originDiffAngle : originDiffAngle;\n\t\t\tconst endAngle = curAngle = curAngle + diffAngle;\n\t\t\tconst startPosition = getPositionByAngle(startAngle, radius);\n\t\t\tconst endPosition = getPositionByAngle(endAngle, radius);\n\n\t\t\tconst prevProperty = this.init && prevSlicesProperties[i];\n\n\t\t\tlet curStart,curEnd;\n\t\t\tif(this.init) {\n\t\t\t\tcurStart = prevProperty ? prevProperty.startPosition : startPosition;\n\t\t\t\tcurEnd = prevProperty ? prevProperty.endPosition : startPosition;\n\t\t\t} else {\n\t\t\t\tcurStart = startPosition;\n\t\t\t\tcurEnd = endPosition;\n\t\t\t}\n\t\t\tconst curPath =\n\t\t\t\toriginDiffAngle === 360\n\t\t\t\t\t? makeCircleStr(curStart, curEnd, this.center, this.radius, clockWise, largeArc)\n\t\t\t\t\t: makeArcPathStr(curStart, curEnd, this.center, this.radius, clockWise, largeArc);\n\n\t\t\ts.sliceStrings.push(curPath);\n\t\t\ts.slicesProperties.push({\n\t\t\t\tstartPosition,\n\t\t\t\tendPosition,\n\t\t\t\tvalue: total,\n\t\t\t\ttotal: s.grandTotal,\n\t\t\t\tstartAngle,\n\t\t\t\tendAngle,\n\t\t\t\tangle: diffAngle\n\t\t\t});\n\n\t\t});\n\t\tthis.init = 0;\n\t}\n\n\tsetupComponents() {\n\t\tlet s = this.state;\n\n\t\tlet componentConfigs = [\n\t\t\t[\n\t\t\t\t'pieSlices',\n\t\t\t\t{ },\n\t\t\t\tfunction() {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tsliceStrings: s.sliceStrings,\n\t\t\t\t\t\tcolors: this.colors\n\t\t\t\t\t};\n\t\t\t\t}.bind(this)\n\t\t\t]\n\t\t];\n\n\t\tthis.components = new Map(componentConfigs\n\t\t\t.map(args => {\n\t\t\t\tlet component = getComponent(...args);\n\t\t\t\treturn [args[0], component];\n\t\t\t}));\n\t}\n\n\tcalTranslateByAngle(property){\n\t\tconst{radius,hoverRadio} = this;\n\t\tconst position = getPositionByAngle(property.startAngle+(property.angle / 2),radius);\n\t\treturn `translate3d(${(position.x) * hoverRadio}px,${(position.y) * hoverRadio}px,0)`;\n\t}\n\n\thoverSlice(path,i,flag,e){\n\t\tif(!path) return;\n\t\tconst color = this.colors[i];\n\t\tif(flag) {\n\t\t\ttransform(path, this.calTranslateByAngle(this.state.slicesProperties[i]));\n\t\t\tpath.style.fill = lightenDarkenColor(color, 50);\n\t\t\tlet g_off = getOffset(this.svg);\n\t\t\tlet x = e.pageX - g_off.left + 10;\n\t\t\tlet y = e.pageY - g_off.top - 10;\n\t\t\tlet title = (this.formatted_labels && this.formatted_labels.length > 0\n\t\t\t\t? this.formatted_labels[i] : this.state.labels[i]) + ': ';\n\t\t\tlet percent = (this.state.sliceTotals[i] * 100 / this.state.grandTotal).toFixed(1);\n\t\t\tthis.tip.setValues(x, y, {name: title, value: percent + \"%\"});\n\t\t\tthis.tip.showTip();\n\t\t} else {\n\t\t\ttransform(path,'translate3d(0,0,0)');\n\t\t\tthis.tip.hideTip();\n\t\t\tpath.style.fill = color;\n\t\t}\n\t}\n\n\tbindTooltip() {\n\t\tthis.container.addEventListener('mousemove', this.mouseMove);\n\t\tthis.container.addEventListener('mouseleave', this.mouseLeave);\n\t}\n\n\tmouseMove(e){\n\t\tconst target = e.target;\n\t\tlet slices = this.components.get('pieSlices').store;\n\t\tlet prevIndex = this.curActiveSliceIndex;\n\t\tlet prevAcitve = this.curActiveSlice;\n\t\tif(slices.includes(target)) {\n\t\t\tlet i = slices.indexOf(target);\n\t\t\tthis.hoverSlice(prevAcitve, prevIndex,false);\n\t\t\tthis.curActiveSlice = target;\n\t\t\tthis.curActiveSliceIndex = i;\n\t\t\tthis.hoverSlice(target, i, true, e);\n\t\t} else {\n\t\t\tthis.mouseLeave();\n\t\t}\n\t}\n\n\tmouseLeave(){\n\t\tthis.hoverSlice(this.curActiveSlice,this.curActiveSliceIndex,false);\n\t}\n}\n", "import BaseChart from './BaseChart';\nimport { getComponent } from '../objects/ChartComponents';\nimport { makeText, heatSquare } from '../utils/draw';\nimport { DAY_NAMES_SHORT, addDays, areInSameMonth, getLastDateInMonth, setDayToSunday, getYyyyMmDd, getWeeksBetween, getMonthName, clone,\n\tNO_OF_MILLIS, NO_OF_YEAR_MONTHS, NO_OF_DAYS_IN_WEEK } from '../utils/date-utils';\nimport { calcDistribution, getMaxCheckpoint } from '../utils/intervals';\nimport { getExtraHeight, getExtraWidth, HEATMAP_DISTRIBUTION_SIZE, HEATMAP_SQUARE_SIZE,\n\tHEATMAP_GUTTER_SIZE } from '../utils/constants';\n\nconst COL_WIDTH = HEATMAP_SQUARE_SIZE + HEATMAP_GUTTER_SIZE;\nconst ROW_HEIGHT = COL_WIDTH;\n// const DAY_INCR = 1;\n\nexport default class Heatmap extends BaseChart {\n\tconstructor(parent, options) {\n\t\tsuper(parent, options);\n\t\tthis.type = 'heatmap';\n\n\t\tthis.countLabel = options.countLabel || '';\n\n\t\tlet validStarts = ['Sunday', 'Monday'];\n\t\tlet startSubDomain = validStarts.includes(options.startSubDomain)\n\t\t\t? options.startSubDomain : 'Sunday';\n\t\tthis.startSubDomainIndex = validStarts.indexOf(startSubDomain);\n\n\t\tthis.setup();\n\t}\n\n\tsetMeasures(options) {\n\t\tlet m = this.measures;\n\t\tthis.discreteDomains = options.discreteDomains === 0 ? 0 : 1;\n\n\t\tm.paddings.top = ROW_HEIGHT * 3;\n\t\tm.paddings.bottom = 0;\n\t\tm.legendHeight = ROW_HEIGHT * 2;\n\t\tm.baseHeight = ROW_HEIGHT * NO_OF_DAYS_IN_WEEK\n\t\t\t+ getExtraHeight(m);\n\n\t\tlet d = this.data;\n\t\tlet spacing = this.discreteDomains ? NO_OF_YEAR_MONTHS : 0;\n\t\tthis.independentWidth = (getWeeksBetween(d.start, d.end)\n\t\t\t+ spacing) * COL_WIDTH + getExtraWidth(m);\n\t}\n\n\tupdateWidth() {\n\t\tlet spacing = this.discreteDomains ? NO_OF_YEAR_MONTHS : 0;\n\t\tlet noOfWeeks = this.state.noOfWeeks ? this.state.noOfWeeks : 52;\n\t\tthis.baseWidth = (noOfWeeks + spacing) * COL_WIDTH\n\t\t\t+ getExtraWidth(this.measures);\n\t}\n\n\tprepareData(data=this.data) {\n\t\tif(data.start && data.end && data.start > data.end) {\n\t\t\tthrow new Error('Start date cannot be greater than end date.');\n\t\t}\n\n\t\tif(!data.start) {\n\t\t\tdata.start = new Date();\n\t\t\tdata.start.setFullYear( data.start.getFullYear() - 1 );\n\t\t}\n\t\tif(!data.end) { data.end = new Date(); }\n\t\tdata.dataPoints = data.dataPoints || {};\n\n\t\tif(parseInt(Object.keys(data.dataPoints)[0]) > 100000) {\n\t\t\tlet points = {};\n\t\t\tObject.keys(data.dataPoints).forEach(timestampSec => {\n\t\t\t\tlet date = new Date(timestampSec * NO_OF_MILLIS);\n\t\t\t\tpoints[getYyyyMmDd(date)] = data.dataPoints[timestampSec];\n\t\t\t});\n\t\t\tdata.dataPoints = points;\n\t\t}\n\n\t\treturn data;\n\t}\n\n\tcalc() {\n\t\tlet s = this.state;\n\n\t\ts.start = clone(this.data.start);\n\t\ts.end = clone(this.data.end);\n\n\t\ts.firstWeekStart = clone(s.start);\n\t\ts.noOfWeeks = getWeeksBetween(s.start, s.end);\n\t\ts.distribution = calcDistribution(\n\t\t\tObject.values(this.data.dataPoints), HEATMAP_DISTRIBUTION_SIZE);\n\n\t\ts.domainConfigs = this.getDomains();\n\t}\n\n\tsetupComponents() {\n\t\tlet s = this.state;\n\t\tlet lessCol = this.discreteDomains ? 0 : 1;\n\n\t\tlet componentConfigs = s.domainConfigs.map((config, i) => [\n\t\t\t'heatDomain',\n\t\t\t{\n\t\t\t\tindex: config.index,\n\t\t\t\tcolWidth: COL_WIDTH,\n\t\t\t\trowHeight: ROW_HEIGHT,\n\t\t\t\tsquareSize: HEATMAP_SQUARE_SIZE,\n\t\t\t\tradius: this.rawChartArgs.radius || 0,\n\t\t\t\txTranslate: s.domainConfigs\n\t\t\t\t\t.filter((config, j) => j < i)\n\t\t\t\t\t.map(config => config.cols.length - lessCol)\n\t\t\t\t\t.reduce((a, b) => a + b, 0)\n\t\t\t\t\t* COL_WIDTH\n\t\t\t},\n\t\t\tfunction() {\n\t\t\t\treturn s.domainConfigs[i];\n\t\t\t}.bind(this)\n\n\t\t]);\n\n\t\tthis.components = new Map(componentConfigs\n\t\t\t.map((args, i) => {\n\t\t\t\tlet component = getComponent(...args);\n\t\t\t\treturn [args[0] + '-' + i, component];\n\t\t\t})\n\t\t);\n\n\t\tlet y = 0;\n\t\tDAY_NAMES_SHORT.forEach((dayName, i) => {\n\t\t\tif([1, 3, 5].includes(i)) {\n\t\t\t\tlet dayText = makeText('subdomain-name', -COL_WIDTH/2, y, dayName,\n\t\t\t\t\t{\n\t\t\t\t\t\tfontSize: HEATMAP_SQUARE_SIZE,\n\t\t\t\t\t\tdy: 8,\n\t\t\t\t\t\ttextAnchor: 'end'\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t\tthis.drawArea.appendChild(dayText);\n\t\t\t}\n\t\t\ty += ROW_HEIGHT;\n\t\t});\n\t}\n\n\tupdate(data) {\n\t\tif(!data) {\n\t\t\tconsole.error('No data to update.');\n\t\t}\n\n\t\tthis.data = this.prepareData(data);\n\t\tthis.draw();\n\t\tthis.bindTooltip();\n\t}\n\n\tbindTooltip() {\n\t\tthis.container.addEventListener('mousemove', (e) => {\n\t\t\tthis.components.forEach(comp => {\n\t\t\t\tlet daySquares = comp.store;\n\t\t\t\tlet daySquare = e.target;\n\t\t\t\tif(daySquares.includes(daySquare)) {\n\n\t\t\t\t\tlet count = daySquare.getAttribute('data-value');\n\t\t\t\t\tlet dateParts = daySquare.getAttribute('data-date').split('-');\n\n\t\t\t\t\tlet month = getMonthName(parseInt(dateParts[1])-1, true);\n\n\t\t\t\t\tlet gOff = this.container.getBoundingClientRect(), pOff = daySquare.getBoundingClientRect();\n\n\t\t\t\t\tlet width = parseInt(e.target.getAttribute('width'));\n\t\t\t\t\tlet x = pOff.left - gOff.left + width/2;\n\t\t\t\t\tlet y = pOff.top - gOff.top;\n\t\t\t\t\tlet value = count + ' ' + this.countLabel;\n\t\t\t\t\tlet name = ' on ' + month + ' ' + dateParts[0] + ', ' + dateParts[2];\n\n\t\t\t\t\tthis.tip.setValues(x, y, {name: name, value: value, valueFirst: 1}, []);\n\t\t\t\t\tthis.tip.showTip();\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t}\n\n\trenderLegend() {\n\t\tthis.legendArea.textContent = '';\n\t\tlet x = 0;\n\t\tlet y = ROW_HEIGHT;\n\t\tlet radius = this.rawChartArgs.radius || 0;\n\n\t\tlet lessText = makeText('subdomain-name', x, y, 'Less',\n\t\t\t{\n\t\t\t\tfontSize: HEATMAP_SQUARE_SIZE + 1,\n\t\t\t\tdy: 9\n\t\t\t}\n\t\t);\n\t\tx = (COL_WIDTH * 2) + COL_WIDTH/2;\n\t\tthis.legendArea.appendChild(lessText);\n\n\t\tthis.colors.slice(0, HEATMAP_DISTRIBUTION_SIZE).map((color, i) => {\n\t\t\tconst square = heatSquare('heatmap-legend-unit', x + (COL_WIDTH + 3) * i,\n\t\t\t\ty, HEATMAP_SQUARE_SIZE, radius, color);\n\t\t\tthis.legendArea.appendChild(square);\n\t\t});\n\n\t\tlet moreTextX = x + HEATMAP_DISTRIBUTION_SIZE * (COL_WIDTH + 3) + COL_WIDTH/4;\n\t\tlet moreText = makeText('subdomain-name', moreTextX, y, 'More',\n\t\t\t{\n\t\t\t\tfontSize: HEATMAP_SQUARE_SIZE + 1,\n\t\t\t\tdy: 9\n\t\t\t}\n\t\t);\n\t\tthis.legendArea.appendChild(moreText);\n\t}\n\n\tgetDomains() {\n\t\tlet s = this.state;\n\t\tconst [startMonth, startYear] = [s.start.getMonth(), s.start.getFullYear()];\n\t\tconst [endMonth, endYear] = [s.end.getMonth(), s.end.getFullYear()];\n\n\t\tconst noOfMonths = (endMonth - startMonth + 1) + (endYear - startYear) * 12;\n\n\t\tlet domainConfigs = [];\n\n\t\tlet startOfMonth = clone(s.start);\n\t\tfor(var i = 0; i < noOfMonths; i++) {\n\t\t\tlet endDate = s.end;\n\t\t\tif(!areInSameMonth(startOfMonth, s.end)) {\n\t\t\t\tlet [month, year] = [startOfMonth.getMonth(), startOfMonth.getFullYear()];\n\t\t\t\tendDate = getLastDateInMonth(month, year);\n\t\t\t}\n\t\t\tdomainConfigs.push(this.getDomainConfig(startOfMonth, endDate));\n\n\t\t\taddDays(endDate, 1);\n\t\t\tstartOfMonth = endDate;\n\t\t}\n\n\t\treturn domainConfigs;\n\t}\n\n\tgetDomainConfig(startDate, endDate='') {\n\t\tlet [month, year] = [startDate.getMonth(), startDate.getFullYear()];\n\t\tlet startOfWeek = setDayToSunday(startDate); // TODO: Monday as well\n\t\tendDate = clone(endDate) || getLastDateInMonth(month, year);\n\n\t\tlet domainConfig = {\n\t\t\tindex: month,\n\t\t\tcols: []\n\t\t};\n\n\t\taddDays(endDate, 1);\n\t\tlet noOfMonthWeeks = getWeeksBetween(startOfWeek, endDate);\n\n\t\tlet cols = [], col;\n\t\tfor(var i = 0; i < noOfMonthWeeks; i++) {\n\t\t\tcol = this.getCol(startOfWeek, month);\n\t\t\tcols.push(col);\n\n\t\t\tstartOfWeek = new Date(col[NO_OF_DAYS_IN_WEEK - 1].yyyyMmDd);\n\t\t\taddDays(startOfWeek, 1);\n\t\t}\n\n\t\tif(col[NO_OF_DAYS_IN_WEEK - 1].dataValue !== undefined) {\n\t\t\taddDays(startOfWeek, 1);\n\t\t\tcols.push(this.getCol(startOfWeek, month, true));\n\t\t}\n\n\t\tdomainConfig.cols = cols;\n\n\t\treturn domainConfig;\n\t}\n\n\tgetCol(startDate, month, empty = false) {\n\t\tlet s = this.state;\n\n\t\t// startDate is the start of week\n\t\tlet currentDate = clone(startDate);\n\t\tlet col = [];\n\n\t\tfor(var i = 0; i < NO_OF_DAYS_IN_WEEK; i++, addDays(currentDate, 1)) {\n\t\t\tlet config = {};\n\n\t\t\t// Non-generic adjustment for entire heatmap, needs state\n\t\t\tlet currentDateWithinData = currentDate >= s.start && currentDate <= s.end;\n\n\t\t\tif(empty || currentDate.getMonth() !== month || !currentDateWithinData) {\n\t\t\t\tconfig.yyyyMmDd = getYyyyMmDd(currentDate);\n\t\t\t} else {\n\t\t\t\tconfig = this.getSubDomainConfig(currentDate);\n\t\t\t}\n\t\t\tcol.push(config);\n\t\t}\n\n\t\treturn col;\n\t}\n\n\tgetSubDomainConfig(date) {\n\t\tlet yyyyMmDd = getYyyyMmDd(date);\n\t\tlet dataValue = this.data.dataPoints[yyyyMmDd];\n\t\tlet config = {\n\t\t\tyyyyMmDd: yyyyMmDd,\n\t\t\tdataValue: dataValue || 0,\n\t\t\tfill: this.colors[getMaxCheckpoint(dataValue, this.state.distribution)]\n\t\t};\n\t\treturn config;\n\t}\n}\n", "import BaseChart from './BaseChart';\nimport { dataPrep, zeroDataPrep, getShortenedLabels } from '../utils/axis-chart-utils';\nimport { AXIS_LEGEND_BAR_SIZE } from '../utils/constants';\nimport { getComponent } from '../objects/ChartComponents';\nimport { getOffset, fire } from '../utils/dom';\nimport { calcChartIntervals, getIntervalSize, getValueRange, getZeroIndex, scale, getClosestInArray } from '../utils/intervals';\nimport { floatTwo } from '../utils/helpers';\nimport { makeOverlay, updateOverlay, legendBar } from '../utils/draw';\nimport { getTopOffset, getLeftOffset, MIN_BAR_PERCENT_HEIGHT, BAR_CHART_SPACE_RATIO,\n\tLINE_CHART_DOT_SIZE } from '../utils/constants';\n\nexport default class AxisChart extends BaseChart {\n\tconstructor(parent, args) {\n\t\tsuper(parent, args);\n\n\t\tthis.barOptions = args.barOptions || {};\n\t\tthis.lineOptions = args.lineOptions || {};\n\n\t\tthis.type = args.type || 'line';\n\t\tthis.init = 1;\n\n\t\tthis.setup();\n\t}\n\n\tsetMeasures() {\n\t\tif(this.data.datasets.length <= 1) {\n\t\t\tthis.config.showLegend = 0;\n\t\t\tthis.measures.paddings.bottom = 30;\n\t\t}\n\t}\n\n\tconfigure(options) {\n\t\tsuper.configure(options);\n\n\t\toptions.axisOptions = options.axisOptions || {};\n\t\toptions.tooltipOptions = options.tooltipOptions || {};\n\n\t\tthis.config.xAxisMode = options.axisOptions.xAxisMode || 'span';\n\t\tthis.config.yAxisMode = options.axisOptions.yAxisMode || 'span';\n\t\tthis.config.xIsSeries = options.axisOptions.xIsSeries || 0;\n\t\tthis.config.shortenYAxisNumbers = options.axisOptions.shortenYAxisNumbers || 0;\n\n\t\tthis.config.formatTooltipX = options.tooltipOptions.formatTooltipX;\n\t\tthis.config.formatTooltipY = options.tooltipOptions.formatTooltipY;\n\n\t\tthis.config.valuesOverPoints = options.valuesOverPoints;\n\t}\n\n\tprepareData(data=this.data) {\n\t\treturn dataPrep(data, this.type);\n\t}\n\n\tprepareFirstData(data=this.data) {\n\t\treturn zeroDataPrep(data);\n\t}\n\n\tcalc(onlyWidthChange = false) {\n\t\tthis.calcXPositions();\n\t\tif(!onlyWidthChange) {\n\t\t\tthis.calcYAxisParameters(this.getAllYValues(), this.type === 'line');\n\t\t}\n\t\tthis.makeDataByIndex();\n\t}\n\n\tcalcXPositions() {\n\t\tlet s = this.state;\n\t\tlet labels = this.data.labels;\n\t\ts.datasetLength = labels.length;\n\n\t\ts.unitWidth = this.width/(s.datasetLength);\n\t\t// Default, as per bar, and mixed. Only line will be a special case\n\t\ts.xOffset = s.unitWidth/2;\n\n\t\t// // For a pure Line Chart\n\t\t// s.unitWidth = this.width/(s.datasetLength - 1);\n\t\t// s.xOffset = 0;\n\n\t\ts.xAxis = {\n\t\t\tlabels: labels,\n\t\t\tpositions: labels.map((d, i) =>\n\t\t\t\tfloatTwo(s.xOffset + i * s.unitWidth)\n\t\t\t)\n\t\t};\n\t}\n\n\tcalcYAxisParameters(dataValues, withMinimum = 'false') {\n\t\tconst yPts = calcChartIntervals(dataValues, withMinimum);\n\t\tconst scaleMultiplier = this.height / getValueRange(yPts);\n\t\tconst intervalHeight = getIntervalSize(yPts) * scaleMultiplier;\n\t\tconst zeroLine = this.height - (getZeroIndex(yPts) * intervalHeight);\n\n\t\tthis.state.yAxis = {\n\t\t\tlabels: yPts,\n\t\t\tpositions: yPts.map(d => zeroLine - d * scaleMultiplier),\n\t\t\tscaleMultiplier: scaleMultiplier,\n\t\t\tzeroLine: zeroLine,\n\t\t};\n\n\t\t// Dependent if above changes\n\t\tthis.calcDatasetPoints();\n\t\tthis.calcYExtremes();\n\t\tthis.calcYRegions();\n\t}\n\n\tcalcDatasetPoints() {\n\t\tlet s = this.state;\n\t\tlet scaleAll = values => values.map(val => scale(val, s.yAxis));\n\n\t\ts.datasets = this.data.datasets.map((d, i) => {\n\t\t\tlet values = d.values;\n\t\t\tlet cumulativeYs = d.cumulativeYs || [];\n\t\t\treturn {\n\t\t\t\tname: d.name && d.name.replace(/<|>|&/g, (char) => char == '&' ? '&amp;' : char == '<' ? '&lt;' : '&gt;'),\n\t\t\t\tindex: i,\n\t\t\t\tchartType: d.chartType,\n\n\t\t\t\tvalues: values,\n\t\t\t\tyPositions: scaleAll(values),\n\n\t\t\t\tcumulativeYs: cumulativeYs,\n\t\t\t\tcumulativeYPos: scaleAll(cumulativeYs),\n\t\t\t};\n\t\t});\n\t}\n\n\tcalcYExtremes() {\n\t\tlet s = this.state;\n\t\tif(this.barOptions.stacked) {\n\t\t\ts.yExtremes = s.datasets[s.datasets.length - 1].cumulativeYPos;\n\t\t\treturn;\n\t\t}\n\t\ts.yExtremes = new Array(s.datasetLength).fill(9999);\n\t\ts.datasets.map(d => {\n\t\t\td.yPositions.map((pos, j) => {\n\t\t\t\tif(pos < s.yExtremes[j]) {\n\t\t\t\t\ts.yExtremes[j] = pos;\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t}\n\n\tcalcYRegions() {\n\t\tlet s = this.state;\n\t\tif(this.data.yMarkers) {\n\t\t\tthis.state.yMarkers = this.data.yMarkers.map(d => {\n\t\t\t\td.position = scale(d.value, s.yAxis);\n\t\t\t\tif(!d.options) d.options = {};\n\t\t\t\t// if(!d.label.includes(':')) {\n\t\t\t\t// \td.label += ': ' + d.value;\n\t\t\t\t// }\n\t\t\t\treturn d;\n\t\t\t});\n\t\t}\n\t\tif(this.data.yRegions) {\n\t\t\tthis.state.yRegions = this.data.yRegions.map(d => {\n\t\t\t\td.startPos = scale(d.start, s.yAxis);\n\t\t\t\td.endPos = scale(d.end, s.yAxis);\n\t\t\t\tif(!d.options) d.options = {};\n\t\t\t\treturn d;\n\t\t\t});\n\t\t}\n\t}\n\n\tgetAllYValues() {\n\t\tlet key = 'values';\n\n\t\tif(this.barOptions.stacked) {\n\t\t\tkey = 'cumulativeYs';\n\t\t\tlet cumulative = new Array(this.state.datasetLength).fill(0);\n\t\t\tthis.data.datasets.map((d, i) => {\n\t\t\t\tlet values = this.data.datasets[i].values;\n\t\t\t\td[key] = cumulative = cumulative.map((c, i) => c + values[i]);\n\t\t\t});\n\t\t}\n\n\t\tlet allValueLists = this.data.datasets.map(d => d[key]);\n\t\tif(this.data.yMarkers) {\n\t\t\tallValueLists.push(this.data.yMarkers.map(d => d.value));\n\t\t}\n\t\tif(this.data.yRegions) {\n\t\t\tthis.data.yRegions.map(d => {\n\t\t\t\tallValueLists.push([d.end, d.start]);\n\t\t\t});\n\t\t}\n\n\t\treturn [].concat(...allValueLists);\n\t}\n\n\tsetupComponents() {\n\t\tlet componentConfigs = [\n\t\t\t[\n\t\t\t\t'yAxis',\n\t\t\t\t{\n\t\t\t\t\tmode: this.config.yAxisMode,\n\t\t\t\t\twidth: this.width,\n\t\t\t\t\tshortenNumbers: this.config.shortenYAxisNumbers\n\t\t\t\t\t// pos: 'right'\n\t\t\t\t},\n\t\t\t\tfunction() {\n\t\t\t\t\treturn this.state.yAxis;\n\t\t\t\t}.bind(this)\n\t\t\t],\n\n\t\t\t[\n\t\t\t\t'xAxis',\n\t\t\t\t{\n\t\t\t\t\tmode: this.config.xAxisMode,\n\t\t\t\t\theight: this.height,\n\t\t\t\t\t// pos: 'right'\n\t\t\t\t},\n\t\t\t\tfunction() {\n\t\t\t\t\tlet s = this.state;\n\t\t\t\t\ts.xAxis.calcLabels = getShortenedLabels(this.width,\n\t\t\t\t\t\ts.xAxis.labels, this.config.xIsSeries);\n\n\t\t\t\t\treturn s.xAxis;\n\t\t\t\t}.bind(this)\n\t\t\t],\n\n\t\t\t[\n\t\t\t\t'yRegions',\n\t\t\t\t{\n\t\t\t\t\twidth: this.width,\n\t\t\t\t\tpos: 'right'\n\t\t\t\t},\n\t\t\t\tfunction() {\n\t\t\t\t\treturn this.state.yRegions;\n\t\t\t\t}.bind(this)\n\t\t\t],\n\t\t];\n\n\t\tlet barDatasets = this.state.datasets.filter(d => d.chartType === 'bar');\n\t\tlet lineDatasets = this.state.datasets.filter(d => d.chartType === 'line');\n\n\t\tlet barsConfigs = barDatasets.map(d => {\n\t\t\tlet index = d.index;\n\t\t\treturn [\n\t\t\t\t'barGraph' + '-' + d.index,\n\t\t\t\t{\n\t\t\t\t\tindex: index,\n\t\t\t\t\tcolor: this.colors[index],\n\t\t\t\t\tstacked: this.barOptions.stacked,\n\n\t\t\t\t\t// same for all datasets\n\t\t\t\t\tvaluesOverPoints: this.config.valuesOverPoints,\n\t\t\t\t\tminHeight: this.height * MIN_BAR_PERCENT_HEIGHT,\n\t\t\t\t},\n\t\t\t\tfunction() {\n\t\t\t\t\tlet s = this.state;\n\t\t\t\t\tlet d = s.datasets[index];\n\t\t\t\t\tlet stacked = this.barOptions.stacked;\n\n\t\t\t\t\tlet spaceRatio = this.barOptions.spaceRatio || BAR_CHART_SPACE_RATIO;\n\t\t\t\t\tlet barsWidth = s.unitWidth * (1 - spaceRatio);\n\t\t\t\t\tlet barWidth = barsWidth/(stacked ? 1 : barDatasets.length);\n\n\t\t\t\t\tlet xPositions = s.xAxis.positions.map(x => x - barsWidth/2);\n\t\t\t\t\tif(!stacked) {\n\t\t\t\t\t\txPositions = xPositions.map(p => p + barWidth * index);\n\t\t\t\t\t}\n\n\t\t\t\t\tlet labels = new Array(s.datasetLength).fill('');\n\t\t\t\t\tif(this.config.valuesOverPoints) {\n\t\t\t\t\t\tif(stacked && d.index === s.datasets.length - 1) {\n\t\t\t\t\t\t\tlabels = d.cumulativeYs;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlabels = d.values;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tlet offsets = new Array(s.datasetLength).fill(0);\n\t\t\t\t\tif(stacked) {\n\t\t\t\t\t\toffsets = d.yPositions.map((y, j) => y - d.cumulativeYPos[j]);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\txPositions: xPositions,\n\t\t\t\t\t\tyPositions: d.yPositions,\n\t\t\t\t\t\toffsets: offsets,\n\t\t\t\t\t\t// values: d.values,\n\t\t\t\t\t\tlabels: labels,\n\n\t\t\t\t\t\tzeroLine: s.yAxis.zeroLine,\n\t\t\t\t\t\tbarsWidth: barsWidth,\n\t\t\t\t\t\tbarWidth: barWidth,\n\t\t\t\t\t};\n\t\t\t\t}.bind(this)\n\t\t\t];\n\t\t});\n\n\t\tlet lineConfigs = lineDatasets.map(d => {\n\t\t\tlet index = d.index;\n\t\t\treturn [\n\t\t\t\t'lineGraph' + '-' + d.index,\n\t\t\t\t{\n\t\t\t\t\tindex: index,\n\t\t\t\t\tcolor: this.colors[index],\n\t\t\t\t\tsvgDefs: this.svgDefs,\n\t\t\t\t\theatline: this.lineOptions.heatline,\n\t\t\t\t\tregionFill: this.lineOptions.regionFill,\n\t\t\t\t\tspline: this.lineOptions.spline,\n\t\t\t\t\thideDots: this.lineOptions.hideDots,\n\t\t\t\t\thideLine: this.lineOptions.hideLine,\n\n\t\t\t\t\t// same for all datasets\n\t\t\t\t\tvaluesOverPoints: this.config.valuesOverPoints,\n\t\t\t\t},\n\t\t\t\tfunction() {\n\t\t\t\t\tlet s = this.state;\n\t\t\t\t\tlet d = s.datasets[index];\n\t\t\t\t\tlet minLine = s.yAxis.positions[0] < s.yAxis.zeroLine\n\t\t\t\t\t\t? s.yAxis.positions[0] : s.yAxis.zeroLine;\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\txPositions: s.xAxis.positions,\n\t\t\t\t\t\tyPositions: d.yPositions,\n\n\t\t\t\t\t\tvalues: d.values,\n\n\t\t\t\t\t\tzeroLine: minLine,\n\t\t\t\t\t\tradius: this.lineOptions.dotSize || LINE_CHART_DOT_SIZE,\n\t\t\t\t\t};\n\t\t\t\t}.bind(this)\n\t\t\t];\n\t\t});\n\n\t\tlet markerConfigs = [\n\t\t\t[\n\t\t\t\t'yMarkers',\n\t\t\t\t{\n\t\t\t\t\twidth: this.width,\n\t\t\t\t\tpos: 'right'\n\t\t\t\t},\n\t\t\t\tfunction() {\n\t\t\t\t\treturn this.state.yMarkers;\n\t\t\t\t}.bind(this)\n\t\t\t]\n\t\t];\n\n\t\tcomponentConfigs = componentConfigs.concat(barsConfigs, lineConfigs, markerConfigs);\n\n\t\tlet optionals = ['yMarkers', 'yRegions'];\n\t\tthis.dataUnitComponents = [];\n\n\t\tthis.components = new Map(componentConfigs\n\t\t\t.filter(args => !optionals.includes(args[0]) || this.state[args[0]])\n\t\t\t.map(args => {\n\t\t\t\tlet component = getComponent(...args);\n\t\t\t\tif(args[0].includes('lineGraph') || args[0].includes('barGraph')) {\n\t\t\t\t\tthis.dataUnitComponents.push(component);\n\t\t\t\t}\n\t\t\t\treturn [args[0], component];\n\t\t\t}));\n\t}\n\n\tmakeDataByIndex() {\n\t\tthis.dataByIndex = {};\n\n\t\tlet s = this.state;\n\t\tlet formatX = this.config.formatTooltipX;\n\t\tlet formatY = this.config.formatTooltipY;\n\t\tlet titles = s.xAxis.labels;\n\n\t\ttitles.map((label, index) => {\n\t\t\tlet values = this.state.datasets.map((set, i) => {\n\t\t\t\tlet value = set.values[index];\n\t\t\t\treturn {\n\t\t\t\t\ttitle: set.name,\n\t\t\t\t\tvalue: value,\n\t\t\t\t\tyPos: set.yPositions[index],\n\t\t\t\t\tcolor: this.colors[i],\n\t\t\t\t\tformatted: formatY ? formatY(value) : value,\n\t\t\t\t};\n\t\t\t});\n\n\t\t\tthis.dataByIndex[index] = {\n\t\t\t\tlabel: label,\n\t\t\t\tformattedLabel: formatX ? formatX(label) : label,\n\t\t\t\txPos: s.xAxis.positions[index],\n\t\t\t\tvalues: values,\n\t\t\t\tyExtreme: s.yExtremes[index],\n\t\t\t};\n\t\t});\n\t}\n\n\tbindTooltip() {\n\t\t// NOTE: could be in tooltip itself, as it is a given functionality for its parent\n\t\tthis.container.addEventListener('mousemove', (e) => {\n\t\t\tlet m = this.measures;\n\t\t\tlet o = getOffset(this.container);\n\t\t\tlet relX = e.pageX - o.left - getLeftOffset(m);\n\t\t\tlet relY = e.pageY - o.top;\n\n\t\t\tif(relY < this.height + getTopOffset(m)\n\t\t\t\t&& relY >  getTopOffset(m)) {\n\t\t\t\tthis.mapTooltipXPosition(relX);\n\t\t\t} else {\n\t\t\t\tthis.tip.hideTip();\n\t\t\t}\n\t\t});\n\t}\n\n\tmapTooltipXPosition(relX) {\n\t\tlet s = this.state;\n\t\tif(!s.yExtremes) return;\n\n\t\tlet index = getClosestInArray(relX, s.xAxis.positions, true);\n\t\tif (index >= 0) {\n\t\t\tlet dbi = this.dataByIndex[index];\n\n\t\t\tthis.tip.setValues(\n\t\t\t\tdbi.xPos + this.tip.offset.x,\n\t\t\t\tdbi.yExtreme + this.tip.offset.y,\n\t\t\t\t{name: dbi.formattedLabel, value: ''},\n\t\t\t\tdbi.values,\n\t\t\t\tindex\n\t\t\t);\n\n\t\t\tthis.tip.showTip();\n\t\t}\n\t}\n\n\trenderLegend() {\n\t\tlet s = this.data;\n\t\tif(s.datasets.length > 1) {\n\t\t\tthis.legendArea.textContent = '';\n\t\t\ts.datasets.map((d, i) => {\n\t\t\t\tlet barWidth = AXIS_LEGEND_BAR_SIZE;\n\t\t\t\t// let rightEndPoint = this.baseWidth - this.measures.margins.left - this.measures.margins.right;\n\t\t\t\t// let multiplier = s.datasets.length - i;\n\t\t\t\tlet rect = legendBar(\n\t\t\t\t\t// rightEndPoint - multiplier * barWidth,\t// To right align\n\t\t\t\t\tbarWidth * i,\n\t\t\t\t\t'0',\n\t\t\t\t\tbarWidth,\n\t\t\t\t\tthis.colors[i],\n\t\t\t\t\td.name,\n\t\t\t\t\tthis.config.truncateLegends);\n\t\t\t\tthis.legendArea.appendChild(rect);\n\t\t\t});\n\t\t}\n\t}\n\n\n\n\t// Overlay\n\tmakeOverlay() {\n\t\tif(this.init) {\n\t\t\tthis.init = 0;\n\t\t\treturn;\n\t\t}\n\t\tif(this.overlayGuides) {\n\t\t\tthis.overlayGuides.forEach(g => {\n\t\t\t\tlet o = g.overlay;\n\t\t\t\to.parentNode.removeChild(o);\n\t\t\t});\n\t\t}\n\n\t\tthis.overlayGuides = this.dataUnitComponents.map(c => {\n\t\t\treturn {\n\t\t\t\ttype: c.unitType,\n\t\t\t\toverlay: undefined,\n\t\t\t\tunits: c.units,\n\t\t\t};\n\t\t});\n\n\t\tif(this.state.currentIndex === undefined) {\n\t\t\tthis.state.currentIndex = this.state.datasetLength - 1;\n\t\t}\n\n\t\t// Render overlays\n\t\tthis.overlayGuides.map(d => {\n\t\t\tlet currentUnit = d.units[this.state.currentIndex];\n\n\t\t\td.overlay = makeOverlay[d.type](currentUnit);\n\t\t\tthis.drawArea.appendChild(d.overlay);\n\t\t});\n\t}\n\n\tupdateOverlayGuides() {\n\t\tif(this.overlayGuides) {\n\t\t\tthis.overlayGuides.forEach(g => {\n\t\t\t\tlet o = g.overlay;\n\t\t\t\to.parentNode.removeChild(o);\n\t\t\t});\n\t\t}\n\t}\n\n\tbindOverlay() {\n\t\tthis.parent.addEventListener('data-select', () => {\n\t\t\tthis.updateOverlay();\n\t\t});\n\t}\n\n\tbindUnits() {\n\t\tthis.dataUnitComponents.map(c => {\n\t\t\tc.units.map(unit => {\n\t\t\t\tunit.addEventListener('click', () => {\n\t\t\t\t\tlet index = unit.getAttribute('data-point-index');\n\t\t\t\t\tthis.setCurrentDataPoint(index);\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\n\t\t// Note: Doesn't work as tooltip is absolutely positioned\n\t\tthis.tip.container.addEventListener('click', () => {\n\t\t\tlet index = this.tip.container.getAttribute('data-point-index');\n\t\t\tthis.setCurrentDataPoint(index);\n\t\t});\n\t}\n\n\tupdateOverlay() {\n\t\tthis.overlayGuides.map(d => {\n\t\t\tlet currentUnit = d.units[this.state.currentIndex];\n\t\t\tupdateOverlay[d.type](currentUnit, d.overlay);\n\t\t});\n\t}\n\n\tonLeftArrow() {\n\t\tthis.setCurrentDataPoint(this.state.currentIndex - 1);\n\t}\n\n\tonRightArrow() {\n\t\tthis.setCurrentDataPoint(this.state.currentIndex + 1);\n\t}\n\n\tgetDataPoint(index=this.state.currentIndex) {\n\t\tlet s = this.state;\n\t\tlet data_point = {\n\t\t\tindex: index,\n\t\t\tlabel: s.xAxis.labels[index],\n\t\t\tvalues: s.datasets.map(d => d.values[index])\n\t\t};\n\t\treturn data_point;\n\t}\n\n\tsetCurrentDataPoint(index) {\n\t\tlet s = this.state;\n\t\tindex = parseInt(index);\n\t\tif(index < 0) index = 0;\n\t\tif(index >= s.xAxis.labels.length) index = s.xAxis.labels.length - 1;\n\t\tif(index === s.currentIndex) return;\n\t\ts.currentIndex = index;\n\t\tfire(this.parent, \"data-select\", this.getDataPoint());\n\t}\n\n\n\n\t// API\n\taddDataPoint(label, datasetValues, index=this.state.datasetLength) {\n\t\tsuper.addDataPoint(label, datasetValues, index);\n\t\tthis.data.labels.splice(index, 0, label);\n\t\tthis.data.datasets.map((d, i) => {\n\t\t\td.values.splice(index, 0, datasetValues[i]);\n\t\t});\n\t\tthis.update(this.data);\n\t}\n\n\tremoveDataPoint(index = this.state.datasetLength-1) {\n\t\tif (this.data.labels.length <= 1) {\n\t\t\treturn;\n\t\t}\n\t\tsuper.removeDataPoint(index);\n\t\tthis.data.labels.splice(index, 1);\n\t\tthis.data.datasets.map(d => {\n\t\t\td.values.splice(index, 1);\n\t\t});\n\t\tthis.update(this.data);\n\t}\n\n\tupdateDataset(datasetValues, index=0) {\n\t\tthis.data.datasets[index].values = datasetValues;\n\t\tthis.update(this.data);\n\t}\n\t// addDataset(dataset, index) {}\n\t// removeDataset(index = 0) {}\n\n\tupdateDatasets(datasets) {\n\t\tthis.data.datasets.map((d, i) => {\n\t\t\tif(datasets[i]) {\n\t\t\t\td.values = datasets[i];\n\t\t\t}\n\t\t});\n\t\tthis.update(this.data);\n\t}\n\n\t// updateDataPoint(dataPoint, index = 0) {}\n\t// addDataPoint(dataPoint, index = 0) {}\n\t// removeDataPoint(index = 0) {}\n}\n", "import AggregationChart from './AggregationChart';\nimport { getComponent } from '../objects/ChartComponents';\nimport { getOffset } from '../utils/dom';\nimport { getPositionByAngle } from '../utils/helpers';\nimport { makeArcStrokePathStr, makeStrokeCircleStr } from '../utils/draw';\nimport { lightenDarkenColor } from '../utils/colors';\nimport { transform } from '../utils/animation';\nimport { FULL_ANGLE } from '../utils/constants';\n\nexport default class DonutChart extends AggregationChart {\n\tconstructor(parent, args) {\n\t\tsuper(parent, args);\n\t\tthis.type = 'donut';\n\t\tthis.initTimeout = 0;\n\t\tthis.init = 1;\n\n\t\tthis.setup();\n\t}\n\n\tconfigure(args) {\n\t\tsuper.configure(args);\n\t\tthis.mouseMove = this.mouseMove.bind(this);\n\t\tthis.mouseLeave = this.mouseLeave.bind(this);\n\n\t\tthis.hoverRadio = args.hoverRadio || 0.1;\n\t\tthis.config.startAngle = args.startAngle || 0;\n\n\t\tthis.clockWise = args.clockWise || false;\n\t\tthis.strokeWidth = args.strokeWidth || 30;\n\t}\n\n\tcalc() {\n\t\tsuper.calc();\n\t\tlet s = this.state;\n\t\tthis.radius =\n\t\t\tthis.height > this.width\n\t\t\t\t? this.center.x - this.strokeWidth / 2\n\t\t\t\t: this.center.y - this.strokeWidth / 2;\n\n\t\tconst { radius, clockWise } = this;\n\n\t\tconst prevSlicesProperties = s.slicesProperties || [];\n\t\ts.sliceStrings = [];\n\t\ts.slicesProperties = [];\n\t\tlet curAngle = 180 - this.config.startAngle;\n\n\t\ts.sliceTotals.map((total, i) => {\n\t\t\tconst startAngle = curAngle;\n\t\t\tconst originDiffAngle = (total / s.grandTotal) * FULL_ANGLE;\n\t\t\tconst largeArc = originDiffAngle > 180 ? 1: 0;\n\t\t\tconst diffAngle = clockWise ? -originDiffAngle : originDiffAngle;\n\t\t\tconst endAngle = curAngle = curAngle + diffAngle;\n\t\t\tconst startPosition = getPositionByAngle(startAngle, radius);\n\t\t\tconst endPosition = getPositionByAngle(endAngle, radius);\n\n\t\t\tconst prevProperty = this.init && prevSlicesProperties[i];\n\n\t\t\tlet curStart,curEnd;\n\t\t\tif(this.init) {\n\t\t\t\tcurStart = prevProperty ? prevProperty.startPosition : startPosition;\n\t\t\t\tcurEnd = prevProperty ? prevProperty.endPosition : startPosition;\n\t\t\t} else {\n\t\t\t\tcurStart = startPosition;\n\t\t\t\tcurEnd = endPosition;\n\t\t\t}\n\t\t\tconst curPath =\n\t\t\t\toriginDiffAngle === 360\n\t\t\t\t\t? makeStrokeCircleStr(curStart, curEnd, this.center, this.radius, this.clockWise, largeArc)\n\t\t\t\t\t: makeArcStrokePathStr(curStart, curEnd, this.center, this.radius, this.clockWise, largeArc);\n\n\t\t\ts.sliceStrings.push(curPath);\n\t\t\ts.slicesProperties.push({\n\t\t\t\tstartPosition,\n\t\t\t\tendPosition,\n\t\t\t\tvalue: total,\n\t\t\t\ttotal: s.grandTotal,\n\t\t\t\tstartAngle,\n\t\t\t\tendAngle,\n\t\t\t\tangle: diffAngle\n\t\t\t});\n\n\t\t});\n\t\tthis.init = 0;\n\t}\n\n\tsetupComponents() {\n\t\tlet s = this.state;\n\n\t\tlet componentConfigs = [\n\t\t\t[\n\t\t\t\t'donutSlices',\n\t\t\t\t{ },\n\t\t\t\tfunction() {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tsliceStrings: s.sliceStrings,\n\t\t\t\t\t\tcolors: this.colors,\n\t\t\t\t\t\tstrokeWidth: this.strokeWidth,\n\t\t\t\t\t};\n\t\t\t\t}.bind(this)\n\t\t\t]\n\t\t];\n\n\t\tthis.components = new Map(componentConfigs\n\t\t\t.map(args => {\n\t\t\t\tlet component = getComponent(...args);\n\t\t\t\treturn [args[0], component];\n\t\t\t}));\n\t}\n\n\tcalTranslateByAngle(property){\n\t\tconst{ radius, hoverRadio } = this;\n\t\tconst position = getPositionByAngle(property.startAngle+(property.angle / 2),radius);\n\t\treturn `translate3d(${(position.x) * hoverRadio}px,${(position.y) * hoverRadio}px,0)`;\n\t}\n\n\thoverSlice(path,i,flag,e){\n\t\tif(!path) return;\n\t\tconst color = this.colors[i];\n\t\tif(flag) {\n\t\t\ttransform(path, this.calTranslateByAngle(this.state.slicesProperties[i]));\n\t\t\tpath.style.stroke = lightenDarkenColor(color, 50);\n\t\t\tlet g_off = getOffset(this.svg);\n\t\t\tlet x = e.pageX - g_off.left + 10;\n\t\t\tlet y = e.pageY - g_off.top - 10;\n\t\t\tlet title = (this.formatted_labels && this.formatted_labels.length > 0\n\t\t\t\t? this.formatted_labels[i] : this.state.labels[i]) + ': ';\n\t\t\tlet percent = (this.state.sliceTotals[i] * 100 / this.state.grandTotal).toFixed(1);\n\t\t\tthis.tip.setValues(x, y, {name: title, value: percent + \"%\"});\n\t\t\tthis.tip.showTip();\n\t\t} else {\n\t\t\ttransform(path,'translate3d(0,0,0)');\n\t\t\tthis.tip.hideTip();\n\t\t\tpath.style.stroke = color;\n\t\t}\n\t}\n\n\tbindTooltip() {\n\t\tthis.container.addEventListener('mousemove', this.mouseMove);\n\t\tthis.container.addEventListener('mouseleave', this.mouseLeave);\n\t}\n\n\tmouseMove(e){\n\t\tconst target = e.target;\n\t\tlet slices = this.components.get('donutSlices').store;\n\t\tlet prevIndex = this.curActiveSliceIndex;\n\t\tlet prevAcitve = this.curActiveSlice;\n\t\tif(slices.includes(target)) {\n\t\t\tlet i = slices.indexOf(target);\n\t\t\tthis.hoverSlice(prevAcitve, prevIndex,false);\n\t\t\tthis.curActiveSlice = target;\n\t\t\tthis.curActiveSliceIndex = i;\n\t\t\tthis.hoverSlice(target, i, true, e);\n\t\t} else {\n\t\t\tthis.mouseLeave();\n\t\t}\n\t}\n\n\tmouseLeave(){\n\t\tthis.hoverSlice(this.curActiveSlice,this.curActiveSliceIndex,false);\n\t}\n}\n", "import * as Charts from './chart';\n\nlet frappe     = { };\n\nfrappe.NAME    = 'Frappe Charts';\nfrappe.VERSION = '1.5.6';\n\nfrappe         = Object.assign({ }, frappe, Charts);\n\nexport default frappe;"], "names": ["expr", "con", "document", "querySelector", "getOffset", "element", "rect", "getBoundingClientRect", "top", "documentElement", "scrollTop", "body", "left", "scrollLeft", "isHidden", "el", "offsetParent", "isElementInViewport", "bottom", "window", "innerHeight", "clientHeight", "right", "innerWidth", "clientWidth", "getElementC<PERSON>nt<PERSON>th", "styles", "getComputedStyle", "padding", "parseFloat", "paddingLeft", "paddingRight", "fire", "target", "type", "properties", "evt", "createEvent", "initEvent", "j", "dispatchEvent", "getTopOffset", "m", "titleHeight", "margins", "paddings", "getLeftOffset", "getExtraHeight", "legend<PERSON><PERSON>ght", "getExtraWidth", "floatTwo", "d", "toFixed", "fillA<PERSON>y", "array", "count", "start", "length", "fillerArray", "Array", "Math", "abs", "fill", "concat", "getStringWidth", "string", "char<PERSON><PERSON><PERSON>", "getPositionByAngle", "angle", "radius", "sin", "ANGLE_RATIO", "cos", "isValidNumber", "candidate", "nonNegative", "Number", "isNaN", "undefined", "isFinite", "round", "getBarHeightAndYAttr", "yTop", "zeroLine", "height", "y", "equilizeNoOfElements", "array1", "array2", "extraCount", "truncateString", "txt", "len", "slice", "shortenLargeNumber", "label", "number", "p", "floor", "log10", "l", "shortened", "pow", "getSplineCurvePointsStr", "xList", "yList", "points", "i", "push", "line", "pointA", "pointB", "lengthX", "lengthY", "sqrt", "atan2", "controlPoint", "current", "previous", "next", "reverse", "o", "PI", "command", "reduce", "acc", "point", "a", "cps", "cpe", "limitColor", "r", "lightenDarkenColor", "color", "amt", "col", "getColor", "usePound", "num", "parseInt", "b", "g", "toString", "isValidColor", "RGB_RE", "test", "$", "createSVG", "tag", "createElementNS", "val", "append<PERSON><PERSON><PERSON>", "ref", "parentNode", "insertBefore", "keys", "map", "style", "prop", "setAttribute", "renderVerticalGradient", "svgDefElem", "gradientId", "setGradientStop", "gradElem", "offset", "opacity", "makeSVGContainer", "parent", "className", "width", "makeSVGDefs", "svgContainer", "makeSVGGroup", "transform", "args", "inside", "<PERSON><PERSON><PERSON>", "pathStr", "makeArcPathStr", "startPosition", "endPosition", "center", "clockWise", "largeArc", "arcStartX", "x", "arcStartY", "arcEndX", "arcEndY", "makeCircleStr", "midArc", "makeArcStrokePathStr", "makeStrokeCircleStr", "makeGradient", "lighter", "gradientDef", "opacities", "percentageBar", "depth", "PERCENTAGE_BAR_DEFAULT_DEPTH", "heatSquare", "size", "data", "key", "<PERSON><PERSON><PERSON>", "LABEL_MAX_CHARS", "text", "FONT_SIZE", "FONT_FILL", "group", "legendDot", "makeText", "content", "options", "fontSize", "dy", "textAnchor", "makeVertLine", "y1", "y2", "stroke", "BASE_LINE_COLOR", "LABEL_MARGIN", "makeHoriLine", "x1", "x2", "lineType", "shortenNumbers", "yLine", "pos", "mode", "AXIS_TICK_LENGTH", "xLine", "<PERSON><PERSON><PERSON><PERSON>", "labelPos", "labelSvg", "yRegion", "region", "datasetBar", "index", "meta", "minHeight", "datasetDot", "dot", "getPaths", "pointsStr", "join", "spline", "path", "heatline", "gradient_id", "svgDefs", "paths", "regionFill", "gradient_id_region", "translate", "unit", "oldCoord", "newCoord", "duration", "old", "STD_EASING", "translateVertLine", "newX", "oldX", "MARKER_LINE_ANIM_DUR", "translateHoriLine", "newY", "oldY", "animateRegion", "rectGroup", "newY1", "newY2", "oldY2", "newHeight", "childNodes", "stroke-dasharray", "getAttribute", "animateBar", "bar", "nodeName", "UNIT_ANIM_DUR", "split", "animateDot", "cx", "cy", "animate<PERSON>ath", "newXList", "newYList", "pathComponents", "anim<PERSON><PERSON>", "PATH_ANIM_DUR", "regStartPt", "regEndPt", "animRegion", "animatePathStr", "old<PERSON><PERSON>", "animateSVGElement", "props", "dur", "easingType", "oldValues", "animElement", "cloneNode", "newElement", "attributeName", "animateElement", "currentValue", "value", "animAttr", "EASING", "webkitTransform", "msTransform", "mozTransform", "oTransform", "animateSVG", "elements", "newElements", "animElements", "<PERSON><PERSON><PERSON><PERSON>", "animSvg", "runSMILAnimation", "svgElement", "elementsToAnimate", "animSvgElement", "<PERSON><PERSON><PERSON><PERSON>", "REPLACE_ALL_NEW_DUR", "downloadFile", "filename", "createElement", "blob", "Blob", "url", "URL", "createObjectURL", "href", "download", "click", "revokeObjectURL", "prepareForExport", "svg", "clone", "classList", "add", "styleEl", "create", "CSSTEXT", "<PERSON><PERSON><PERSON><PERSON>", "container", "innerHTML", "treatAsUtc", "date", "result", "Date", "setMinutes", "getMinutes", "getTimezoneOffset", "getYyyyMmDd", "dd", "getDate", "mm", "getMonth", "getFullYear", "getTime", "getWeeksBetween", "startDate", "endDate", "weekStartDate", "setDayToSunday", "ceil", "getDaysBetween", "NO_OF_DAYS_IN_WEEK", "millisecondsPerDay", "SEC_IN_DAY", "NO_OF_MILLIS", "areInSameMonth", "getMonthName", "short", "monthName", "MONTH_NAMES", "getLastDateInMonth", "month", "year", "newDate", "day", "getDay", "addDays", "numberOfDays", "setDate", "getComponent", "name", "constants", "getData", "Object", "componentConfigs", "filter", "includes", "k", "config", "assign", "ChartComponent", "normalize", "mantissa", "exponent", "sig", "exp", "getChartRangeIntervals", "max", "min", "upperBound", "lowerBound", "range", "noOfParts", "partSize", "intervals", "getChartIntervals", "maxValue", "minValue", "normalMaxValue", "normalMinValue", "calcChartIntervals", "values", "getPositiveFirstIntervals", "absMinValue", "intervalSize", "unshift", "withMinimum", "pseudoMaxValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getZeroIndex", "yPts", "interval", "getIntervalSize", "indexOf", "orderedArray", "getValueRange", "scale", "yAxis", "scaleMultiplier", "getClosestInArray", "goal", "arr", "closest", "prev", "curr", "calcDistribution", "distributionSize", "dataMaxValue", "distributionStep", "distribution", "checkpoint", "getMaxCheckpoint", "dataPrep", "labels", "datasetLength", "datasets", "zeroArray", "vals", "chartType", "AXIS_DATASET_CHART_TYPES", "yRegions", "end", "zeroDataPrep", "realData", "zeroData", "yMarkers", "getShortenedLabels", "chartWidth", "isSeries", "allowedSpace", "allowedLetters", "DEFAULT_CHAR_WIDTH", "seriesMultiple", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getChartByType", "AxisChart", "chartTypes", "error", "css", "insertAt", "head", "getElementsByTagName", "styleSheet", "cssText", "createTextNode", "BASE_MEASURES", "INIT_CHART_UPDATE_TIMEOUT", "DEFAULT_CHART_COLORS", "DEFAULT_COLORS", "SvgTip", "colors", "<PERSON><PERSON><PERSON>", "titleValue", "listValues", "titleV<PERSON>ue<PERSON>irs<PERSON>", "setup", "makeTooltip", "calcPosition", "this", "hideTip", "title", "dataPointList", "addEventListener", "set", "_this2", "formatted", "li", "offsetWidth", "offsetHeight", "maxLeft", "pointer", "pointerOffset", "valueFirst", "refresh", "PRESET_COLOR_MAP", "exec", "c", "ch", "makeOverlay", "transformValue", "overlay", "updateOverlay", "attributes", "attr", "specified", "nodeValue", "BaseChart", "HTMLElement", "Error", "rawChartArgs", "prepareData", "prepareFirstData", "validateColors", "isNavigable", "animate", "truncateLegends", "measures", "JSON", "parse", "stringify", "setMeasures", "showLegend", "argHeight", "baseHeight", "state", "initTimeout", "overlays", "configure", "validColors", "for<PERSON>ach", "warn", "boundDrawFn", "_this", "draw", "removeEventListener", "makeContainer", "updateWidth", "independentWidth", "tip", "bindTooltip", "onlyWidthChange", "init", "calc", "makeChartArea", "setupComponents", "components", "drawArea", "render", "update", "renderLegend", "setupNavigation", "baseWidth", "titleEL", "titleFontSize", "legend<PERSON>rea", "updateTipOffset", "Map", "make", "updateNav", "bindUnits", "bindOverlay", "keyActions", "onEnterKey", "bind", "onLeftArrow", "onUpArrow", "onRightArrow", "onDownArrow", "e", "_this4", "event", "keyCode", "chartSvg", "AggregationChart", "formatTooltipY", "tooltipOptions", "maxSlices", "maxLegendPoints", "s", "sliceTotals", "allTotals", "total", "totals", "sort", "sumOfRemaining", "grandTotal", "textContent", "legendTotals", "<PERSON><PERSON><PERSON><PERSON>", "divisor", "_this3", "DAY_NAMES_SHORT", "layerClass", "layerTransform", "makeElements", "animateElements", "store", "layer", "oldData", "sliceStrings", "strokeWidth", "transition", "newData", "xPositions", "widths", "barHeight", "<PERSON><PERSON><PERSON><PERSON>", "positions", "position", "newPos", "<PERSON><PERSON><PERSON><PERSON>", "oldPos", "<PERSON><PERSON><PERSON><PERSON>", "calcLabels", "_this5", "newOptions", "startPos", "endPos", "_this6", "newStarts", "oldStarts", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rowHeight", "squareSize", "xTranslate", "serializedSubDomains", "cols", "week", "weekNo", "toUpperCase", "yyyyMmDd", "dataValue", "square", "unitType", "units", "yPositions", "offsets", "barsWidth", "newXPos", "newYPos", "newOffsets", "oldXPos", "oldYPos", "oldOffsets", "hideLine", "hideDots", "valuesOverPoints", "newValues", "Percentage<PERSON>hart", "barOptions", "component", "xPos", "bars", "get", "gOff", "pOff", "formattedLabels", "fraction", "set<PERSON><PERSON><PERSON>", "showTip", "<PERSON><PERSON><PERSON>", "mouseMove", "mouseLeave", "hoverRadio", "startAngle", "prevSlicesProperties", "slicesProperties", "curAngle", "originDiffAngle", "diffAngle", "endAngle", "prevProperty", "curStart", "curEnd", "curPath", "property", "flag", "calTranslateByAngle", "g_off", "pageX", "pageY", "formatted_labels", "percent", "slices", "prevIndex", "curActiveSliceIndex", "prevAcitve", "curActiveSlice", "hoverSlice", "Heatmap", "<PERSON><PERSON><PERSON><PERSON>", "validStarts", "startSubDomain", "startSubDomainIndex", "discreteDomains", "ROW_HEIGHT", "HEATMAP_SQUARE_SIZE", "spacing", "noOfWeeks", "setFullYear", "dataPoints", "timestampSec", "firstWeekStart", "domainConfigs", "getDomains", "lessCol", "day<PERSON><PERSON>", "dayText", "daySquares", "comp", "daySquare", "dateParts", "lessText", "COL_WIDTH", "moreText", "HEATMAP_DISTRIBUTION_SIZE", "startMonth", "startYear", "noOfMonths", "startOfMonth", "getDomainConfig", "startOfWeek", "domainConfig", "noOfMonthWeeks", "getCol", "empty", "currentDate", "currentDateWithinData", "getSubDomainConfig", "lineOptions", "axisOptions", "xAxisMode", "yAxisMode", "xIsSeries", "shortenYAxisNumbers", "formatTooltipX", "calcXPositions", "calcYAxisParameters", "getAllYValues", "makeDataByIndex", "unitWidth", "xOffset", "xAxis", "dataValues", "intervalHeight", "calcDatasetPoints", "calcYExtremes", "calcYRegions", "scaleAll", "cumulativeYs", "replace", "char", "stacked", "yExtremes", "cumulativeYPos", "cumulative", "allValueLists", "barDatasets", "lineDatasets", "barsConfigs", "spaceRatio", "lineConfigs", "minLine", "dotSize", "markerConfigs", "optionals", "dataUnitComponents", "dataByIndex", "formatX", "formatY", "relX", "relY", "mapTooltipXPosition", "dbi", "yExtreme", "formattedLabel", "overlayGuides", "currentIndex", "currentUnit", "_this7", "setCurrentDataPoint", "_this9", "_this10", "getDataPoint", "datasetValues", "splice", "<PERSON><PERSON><PERSON><PERSON>", "Chart", "frappe", "NAME", "VERSION", "Charts"], "mappings": "yDACwB,gBAATA,IAAoBC,GAAOC,UAAUC,cAAcH,GAAQA,GAAQ,KA4ClF,QAAgBI,GAAUC,MACrBC,GAAOD,EAAQE,mCAKbD,EAAKE,KAAON,SAASO,gBAAgBC,WAAaR,SAASS,KAAKD,gBAC/DJ,EAAKM,MAAQV,SAASO,gBAAgBI,YAAcX,SAASS,KAAKE,aAO1E,QAAgBC,GAASC,SACI,QAApBA,EAAGC,aAGZ,QAAgBC,GAAoBF,MAE/BT,GAAOS,EAAGR,8BAGbD,GAAKE,KAAO,GACNF,EAAKM,MAAQ,GACbN,EAAKY,SAAWC,OAAOC,aAAelB,SAASO,gBAAgBY,iBAC1DC,QAAUH,OAAOI,YAAcrB,SAASO,gBAAgBe,aAIrE,QAAgBC,GAAuBpB,MAClCqB,GAASP,OAAOQ,iBAAiBtB,GACjCuB,EAAUC,WAAWH,EAAOI,aAC/BD,WAAWH,EAAOK,oBAEZ1B,GAAQmB,YAAcI,EA2B9B,QAAgBI,GAAKC,EAAQC,EAAMC,MAC9BC,GAAMlC,SAASmC,YAAY,gBAE3BC,UAAUJ,GAAM,GAAM,OAErB,GAAIK,KAAKJ,KACTI,GAAKJ,EAAWI,SAGdN,GAAOO,cAAcJ,GC7E7B,QAAgBK,GAAaC,SACrBA,GAAEC,YAAcD,EAAEE,QAAQpC,IAAMkC,EAAEG,SAASrC,IAGnD,QAAgBsC,GAAcJ,SACtBA,GAAEE,QAAQhC,KAAO8B,EAAEG,SAASjC,KAGpC,QAAgBmC,GAAeL,SACPA,GAAEE,QAAQpC,IAAMkC,EAAEE,QAAQ1B,OAC9CwB,EAAEG,SAASrC,IAAMkC,EAAEG,SAAS3B,OAC5BwB,EAAEC,YAAcD,EAAEM,aAItB,QAAgBC,GAAcP,SACPA,GAAEE,QAAQhC,KAAO8B,EAAEE,QAAQtB,MAC9CoB,EAAEG,SAASjC,KAAO8B,EAAEG,SAASvB,MClDjC,QAAgB4B,GAASC,SACjBtB,YAAWsB,EAAEC,QAAQ,IAyC7B,QAAgBC,GAAUC,EAAOC,EAAOlD,MAASmD,0DAC5CnD,OACOmD,EAAQF,EAAM,GAAKA,EAAMA,EAAMG,OAAS,OAE/CC,GAAc,GAAIC,OAAMC,KAAKC,IAAIN,IAAQO,KAAKzD,YAC1CmD,EAAQE,EAAYK,OAAOT,GAASA,EAAMS,OAAOL,GAS1D,QAAgBM,GAAeC,EAAQC,UAC9BD,EAAO,IAAIR,OAASS,EAyB7B,QAAgBC,GAAmBC,EAAOC,YAErCT,KAAKU,IAAIF,EAAQG,IAAeF,IAChCT,KAAKY,IAAIJ,EAAQG,IAAeF,GASrC,QAAgBI,GAAcC,MAAWC,kEACpCC,OAAOC,MAAMH,SACMI,KAAdJ,MACCE,OAAOG,SAASL,MACjBC,GAAeD,EAAY,KAQrC,QAAgBM,GAAM7B,SAGdyB,QAAOhB,KAAKoB,MAAM7B,EAAI,MAAQ,eCjHtB8B,GAAqBC,EAAMC,MACtCC,UAAQC,eACRH,IAAQC,KACFA,EAAWD,IAChBA,MAEKA,EAAOC,IACZA,IAGGC,EAAQC,GAGjB,QAAgBC,GAAqBC,EAAQC,MAC5CC,0DAAaD,EAAO/B,OAAS8B,EAAO9B,aAGjCgC,GAAa,IACNpC,EAAUkC,EAAQE,KAElBpC,EAAUmC,EAAQC,IAEpBF,EAAQC,GAGjB,QAAgBE,GAAeC,EAAKC,MAC9BD,QAGDA,GAAIlC,OAASmC,EACTD,EAAIE,MAAM,EAAGD,EAAI,GAAK,MAEtBD,EAIT,QAAgBG,GAAmBC,MAC9BC,aACiB,gBAAVD,GAAoBC,EAASD,MACnC,IAAqB,gBAAVA,OACNnB,OAAOmB,GACZnB,OAAOC,MAAMmB,IAAS,MAAOD,MAI9BE,GAAIrC,KAAKsC,MAAMtC,KAAKuC,MAAMvC,KAAKC,IAAImC,QACnCC,GAAK,EAAG,MAAOD,MACfI,GAAIxC,KAAKsC,MAAMD,EAAI,GACnBI,EAAazC,KAAK0C,IAAI,GAAIL,EAAQ,EAAJG,KAAWJ,EAASpC,KAAK0C,IAAI,GAAIL,IAAI7C,QAAQ,SAGxEQ,MAAKoB,MAAgB,IAAVqB,GAAe,IAAM,KAAO,GAAI,IAAK,IAAK,IAAK,KAAKD,GAIvE,QAAgBG,GAAwBC,EAAOC,OAG1C,GADAC,MACIC,EAAE,EAAEA,EAAEH,EAAM/C,OAAOkD,MACnBC,MAAMJ,EAAMG,GAAIF,EAAME,QAI1BE,GAAO,SAACC,EAAQC,MACfC,GAAUD,EAAO,GAAKD,EAAO,GAC7BG,EAAUF,EAAO,GAAKD,EAAO,iBAExBlD,KAAKsD,KAAKtD,KAAK0C,IAAIU,EAAS,GAAKpD,KAAK0C,IAAIW,EAAS,UACpDrD,KAAKuD,MAAMF,EAASD,KAIzBI,EAAe,SAACC,EAASC,EAAUC,EAAMC,MAGxCC,GAAIZ,EAFAS,GAAYD,EACZE,GAAQF,GAEZjD,EAAQqD,EAAErD,OAASoD,EAAU5D,KAAK8D,GAAK,GACvCjE,EAfW,GAeFgE,EAAEhE,cACP4D,EAAQ,GAAKzD,KAAKY,IAAIJ,GAASX,EAC/B4D,EAAQ,GAAKzD,KAAKU,IAAIF,GAASX,UAUzB,UAACiD,EAAQiB,SAChBjB,GAAOkB,OAAO,SAACC,EAAKC,EAAOnB,EAAGoB,SAAY,KAANpB,EACrCmB,EAAM,OAAMA,EAAM,GAClBD,MAAOF,EAAQG,EAAOnB,EAAGoB,IAAM,KAGtBrB,EAZI,SAACoB,EAAOnB,EAAGoB,MAC1BC,GAAMZ,EAAaW,EAAEpB,EAAI,GAAIoB,EAAEpB,EAAI,GAAImB,GACvCG,EAAMb,EAAaU,EAAOC,EAAEpB,EAAI,GAAIoB,EAAEpB,EAAI,IAAI,cACtCqB,EAAI,OAAMA,EAAI,OAAMC,EAAI,OAAMA,EAAI,OAAMH,EAAM,OAAMA,EAAM,KCvExE,QAASI,GAAWC,SACfA,GAAI,IAAY,IACXA,EAAI,EAAU,EAChBA,EAGR,QAAgBC,GAAmBC,EAAOC,MACrCC,GAAMC,GAASH,GACfI,GAAW,CACD,MAAVF,EAAI,OACDA,EAAI1C,MAAM,MACL,MAER6C,GAAMC,SAASJ,EAAI,IACnBJ,EAAID,GAAYQ,GAAO,IAAMJ,GAC7BM,EAAIV,GAAaQ,GAAO,EAAK,KAAUJ,GACvCO,EAAIX,GAAkB,IAANQ,GAAkBJ,UAC9BG,EAAS,IAAI,KAAOI,EAAKD,GAAK,EAAMT,GAAK,IAAKW,SAAS,IAGhE,QAAgBC,GAAa9E,MAGxB+E,GAAS,mHADA,uCAECC,KAAKhF,IAAW+E,EAAOC,KAAKhF,GC7B3C,QAASiF,GAAElJ,EAAMC,SACO,gBAATD,IAAoBC,GAAOC,UAAUC,cAAcH,GAAQA,GAAQ,KAGlF,QAAgBmJ,GAAUC,EAAK3B,MAC1BpH,GAAUH,SAASmJ,gBAAgB,6BAA8BD,OAEhE,GAAIzC,KAAKc,GAAG,IACZ6B,GAAM7B,EAAEd,MAEF,WAANA,IACD2C,GAAKC,YAAYlJ,OAEf,IAAU,WAANsG,EAAgB,IACpB6C,GAAMN,EAAEI,KACRG,WAAWC,aAAarJ,EAASmJ,KAC7BD,YAAYC,OAEJ,WAAN7C,EACQ,qBAAR2C,iBAAAA,YACFK,KAAKL,GAAKM,IAAI,cACZC,MAAMC,GAAQR,EAAIQ,MAInB,cAANnD,MAAyB,SACnB,cAANA,IACF,YAAyB2C,IAEjBS,aAAapD,EAAG2C,UAKpBjJ,GAGR,QAAS2J,GAAuBC,EAAYC,SACpCf,GAAU,yBACRc,KACJC,KACA,KACA,KACA,KACA,IAIN,QAASC,GAAgBC,EAAUC,EAAQhC,EAAOiC,SAC1CnB,GAAU,eACNiB,uBACc/B,SACdgC,iBACMC,IAIlB,QAAgBC,GAAiBC,EAAQC,EAAWC,EAAOtF,SACnD+D,GAAU,iBACLsB,SACHD,QACDE,SACCtF,IAIV,QAAgBuF,GAAYC,SACpBzB,GAAU,eACRyB,IAIV,QAAgBC,GAAaJ,MAAWK,0DAAU,GAAIN,6DAAO1F,GACxDiG,aACQN,YACAK,SAETN,KAAQO,EAAKC,OAASR,GAClBrB,EAAU,IAAK4B,GAWvB,QAAgBE,GAASC,SACjB/B,GAAU,yEAD0B,KAGvC+B,wEAHkD,mEAAa,6EAAoB,KAYxF,QAAgBC,GAAeC,EAAeC,EAAaC,EAAQjH,MAAQkH,0DAAU,EAAGC,yDAAS,EAC3FC,EAAyBH,EAAOI,EAAIN,EAAcM,EAAvCC,EAA0CL,EAAOjG,EAAI+F,EAAc/F,EAC9EuG,EAAqBN,EAAOI,EAAIL,EAAYK,EAAnCG,EAAsCP,EAAOjG,EAAIgG,EAAYhG,YAChEiG,EAAOI,MAAKJ,EAAOjG,YAC1BoG,MAAaE,aACZtH,MAAUA,QAAYmH,OAAYD,EAAY,EAAI,YACpDK,MAAWC,OAGf,QAAgBC,GAAcV,EAAeC,EAAaC,EAAQjH,MAAQkH,0DAAU,EAAGC,yDAAS,EAC1FC,EAAyBH,EAAOI,EAAIN,EAAcM,EAAvCC,EAA0CL,EAAOjG,EAAI+F,EAAc/F,EAC9EuG,EAA6BN,EAAOI,EAAIL,EAAYK,EAA3CK,EAAyD,EAAXT,EAAOjG,EAA7CwG,EAAoDP,EAAOjG,EAAIgG,EAAYhG,YACtFiG,EAAOI,MAAKJ,EAAOjG,YAC1BoG,MAAaE,aACZtH,MAAUA,QAAYmH,OAAYD,EAAY,EAAI,YACpDK,MAAWG,cACVN,MAAaM,aACZ1H,MAAUA,QAAYmH,OAAYD,EAAY,EAAI,YACpDK,MAAWC,OAGf,QAAgBG,GAAqBZ,EAAeC,EAAaC,EAAQjH,MAAQkH,0DAAU,EAAGC,yDAAS,EACjGC,EAAyBH,EAAOI,EAAIN,EAAcM,EAAvCC,EAA0CL,EAAOjG,EAAI+F,EAAc/F,EAC9EuG,EAAqBN,EAAOI,EAAIL,EAAYK,EAAnCG,EAAsCP,EAAOjG,EAAIgG,EAAYhG,YAEhEoG,MAAaE,aACnBtH,MAAUA,QAAYmH,OAAYD,EAAY,EAAI,YACpDK,MAAWC,EAGf,QAAgBI,GAAoBb,EAAeC,EAAaC,EAAQjH,MAAQkH,0DAAU,EAAGC,yDAAS,EAChGC,EAAyBH,EAAOI,EAAIN,EAAcM,EAAvCC,EAA0CL,EAAOjG,EAAI+F,EAAc/F,EAC9EuG,EAA6BN,EAAOI,EAAIL,EAAYK,EAA3CK,EAAuD,EAAT1H,EAAasH,EAAnDE,EAA8DP,EAAOjG,EAAI+F,EAAc/F,YAElGoG,MAAaE,aACnBtH,MAAUA,QAAYmH,OAAYD,EAAY,EAAI,YACpDK,MAAWG,YACVN,MAAaM,aACZ1H,MAAUA,QAAYmH,OAAYD,EAAY,EAAI,YACpDK,MAAWC,EAGf,QAAgBK,GAAajC,EAAY5B,MAAO8D,2DAC3CjC,EAAY,sBAA6B7B,EAAQ,KAAM8D,EAAU,UAAY,WAC7EC,EAAcpC,EAAuBC,EAAYC,GACjDmC,GAAa,EAAG,GAAK,UACtBF,QACW,GAAK,GAAK,MAGRC,EAAa,KAAM/D,EAAOgE,EAAU,MACpCD,EAAa,MAAO/D,EAAOgE,EAAU,MACrCD,EAAa,OAAQ/D,EAAOgE,EAAU,IAE/CnC,EAGR,QAAgBoC,GAAcZ,EAAGrG,EAAGqF,EAAOtF,MAC1CmH,0DAAMC,GAA8B1I,yDAAK,aAkBlCqF,GAAU,kBAfL,mBACRuC,IACArG,QACIqF,SACCtF,OACFtB,iBAEKsE,EAAmBtE,GAAO,8BAGVsB,EAASsF,QAAUA,OAAUtF,iBACvCmH,KAOnB,QAAgBE,GAAWhC,EAAWiB,EAAGrG,EAAGqH,EAAMrI,MAAQP,0DAAK,OAAQ6I,4DAClE5B,aACQN,IACRiB,IACArG,QACIqH,SACCA,KACJrI,OACEP,iBAGA6F,KAAKgD,GAAM/C,IAAI,cAChBgD,GAAOD,EAAKC,KAGXzD,EAAU,OAAQ4B,GAG1B,QAAgB8B,GAAUnB,EAAGrG,EAAGqH,MAAM5I,0DAAK,OAAQiC,yEAC/BL,EAAeK,EAAO+G,IAAmB/G,KAExDgF,cACQ,eACR,IACA,QACI2B,SACC,WACF5I,GAEHiJ,EAAO5D,EAAU,kBACT,wBACR,IACA,KACc,EAAZ6D,GAAiB,iBACI,IAAZA,GAAmB,mBAClB,aACTC,aACKlH,IAGRmH,EAAQ/D,EAAU,4BACGuC,OAAMrG,iBAEzBkE,YAAYJ,EAAU,OAAQ4B,MAC9BxB,YAAYwD,GAEXG,EAGR,QAAgBC,GAAUzB,EAAGrG,EAAGqH,MAAM5I,0DAAK,OAAQiC,yEAC/BL,EAAeK,EAAO+G,IAAmB/G,KAExDgF,cACQ,gBACP,KACA,IACD2B,OACG5I,GAEHiJ,EAAO5D,EAAU,kBACT,wBACR,IACA,KACE6D,GAAa,QACbA,GAAU,EAAK,iBACM,IAAZA,GAAmB,mBAClB,aACTC,aACKlH,IAGRmH,EAAQ/D,EAAU,4BACGuC,OAAMrG,iBAEzBkE,YAAYJ,EAAU,SAAU4B,MAChCxB,YAAYwD,GAEXG,EAGR,QAAgBE,GAAS3C,EAAWiB,EAAGrG,EAAGgI,MAASC,6DAC9CC,EAAWD,EAAQC,UAAYP,SAI5B7D,GAAU,kBACLsB,IACRiB,IACArG,UANoBP,KAAfwI,EAAQE,GAAmBF,EAAQE,GAAMD,EAAW,GAOnD,iBACIA,EAAW,UAPdD,EAAQxJ,MAAQmJ,iBACVK,EAAQG,YAAc,kBAS3BJ,IAIb,QAASK,GAAahC,EAAG3F,EAAO4H,EAAIC,MAAIN,4DACnCA,GAAQO,SAAQP,EAAQO,OAASC,OACjC1H,GAAI+C,EAAU,kBACN,iBAAmBmE,EAAQ7C,aAClC,KACA,KACAkD,KACAC,iBAEKN,EAAQO,UAIdd,EAAO5D,EAAU,UACjB,IACAwE,EAAKC,EAAKD,EAAKI,GAAeJ,EAAKI,GAAef,MACjDA,GAAY,iBACHA,GAAY,mBACV,mBACJjH,EAAQ,KAGhBc,EAAOsC,EAAU,4BACKuC,oBAGrBnC,YAAYnD,KACZmD,YAAYwD,GAEVlG,EAGR,QAASmH,GAAa3I,EAAGU,EAAOkI,EAAIC,MAAIZ,4DACnCA,GAAQO,SAAQP,EAAQO,OAASC,IACjCR,EAAQa,WAAUb,EAAQa,SAAW,IACrCb,EAAQc,iBAAgBrI,EAAQD,EAAmBC,OAKnDK,GAAI+C,EAAU,kBAHF,mBAAqBmE,EAAQ7C,WACtB,WAArB6C,EAAQa,SAAwB,SAAU,OAIvCF,KACAC,KACA,KACA,iBAEKZ,EAAQO,UAIdd,EAAO5D,EAAU,UACjB8E,EAAKC,EAAKD,EAAKF,GAAeE,EAAKF,KACnC,KACEf,GAAY,EAAI,EAAK,iBACbA,GAAY,mBACViB,EAAKC,EAAK,MAAQ,kBACtBnI,EAAM,KAGdc,EAAOsC,EAAU,+BACO9D,uBACT,UAGP,KAAT0H,GAAuB,MAATA,MACXlD,MAAMgE,OAAS,2BAGhBtE,YAAYnD,KACZmD,YAAYwD,GAEVlG,EAGR,QAAgBwH,GAAMhJ,EAAGU,EAAO2E,MAAO4C,4DACjC7I,GAAcY,KAAIA,EAAI,GAEvBiI,EAAQgB,MAAKhB,EAAQgB,IAAM,QAC3BhB,EAAQjD,SAAQiD,EAAQjD,OAAS,GACjCiD,EAAQiB,OAAMjB,EAAQiB,KAAO,QAC7BjB,EAAQO,SAAQP,EAAQO,OAASC,IACjCR,EAAQ7C,YAAW6C,EAAQ7C,UAAY,OAEvCwD,IAAM,EAAIO,GACVN,EAAsB,SAAjBZ,EAAQiB,KAAkB7D,EAAQ8D,GAAmB,QAE1C,SAAjBlB,EAAQiB,MAAmC,UAAhBjB,EAAQgB,QAChC5D,EAAQ8D,KACR9D,MAKA4C,EAAQjD,UACRiD,EAAQjD,OAEP2D,EAAa3I,EAAGU,EAAOkI,EAAIC,UACzBZ,EAAQO,iBACLP,EAAQ7C,mBACT6C,EAAQa,wBACFb,EAAQc,iBAI1B,QAAgBK,GAAM/C,EAAG3F,EAAOX,MAAQkI,4DAClC7I,GAAciH,KAAIA,EAAI,GAEvB4B,EAAQgB,MAAKhB,EAAQgB,IAAM,UAC3BhB,EAAQjD,SAAQiD,EAAQjD,OAAS,GACjCiD,EAAQiB,OAAMjB,EAAQiB,KAAO,QAC7BjB,EAAQO,SAAQP,EAAQO,OAASC,IACjCR,EAAQ7C,YAAW6C,EAAQ7C,UAAY,OAavCkD,GAAKvI,EAASoJ,GACdZ,EAAsB,SAAjBN,EAAQiB,MAAmB,EAAIC,GAAmBpJ,QAEvC,SAAjBkI,EAAQiB,MAAmC,QAAhBjB,EAAQgB,SAE/B,EAAIE,KACL,GAGCd,EAAahC,EAAG3F,EAAO4H,EAAIC,UACzBN,EAAQO,iBACLP,EAAQ7C,mBACT6C,EAAQa,WAIpB,QAAgBO,GAAQrJ,EAAGU,EAAO2E,MAAO4C,4DACpCA,GAAQqB,WAAUrB,EAAQqB,SAAW,YAIrCC,GAAWzF,EAAU,kBACb,gBAJiB,SAArBmE,EAAQqB,SAAsBZ,GACnCrD,EAAQ1G,EAAe+B,EAAO,GAAKgI,KAKlC,KACEf,IAAa,EAAK,iBACVA,GAAY,mBACV,kBACJjH,EAAM,KAGdc,EAAOmH,EAAa3I,EAAG,GAAI,EAAGqF,UACzB4C,EAAQO,QAAUC,aACfR,EAAQ7C,WAAa,YACtB6C,EAAQa,oBAGd5E,YAAYqF,GAEV/H,EAGR,QAAgBgI,GAAQlB,EAAIC,EAAIlD,EAAO3E,MAAOuH,6DAEzClI,EAASuI,EAAKC,EAEdtN,EAAO6I,EAAU,6EAIX2E,sBACepD,OAAUtF,KAG/B,IACA,QACIsF,SACCtF,GAGLkI,GAAQqB,WAAUrB,EAAQqB,SAAW,YAIrCC,GAAWzF,EAAU,kBACb,gBAJiB,SAArBmE,EAAQqB,SAAsBZ,GACnCrD,EAAQ1G,EAAe+B,EAAM,GAAI,KAAOgI,KAKvC,KACEf,IAAa,EAAK,iBACVA,GAAY,mBACV,kBACJjH,EAAM,KAGd+I,EAAS3F,EAAU,+BACKyE,iBAGrBrE,YAAYjJ,KACZiJ,YAAYqF,GAEZE,EAGR,QAAgBC,GAAWrD,EAAGxG,EAAMwF,EAAOrC,MAAOtC,0DAAM,GAAIiJ,yDAAM,EAAG3E,yDAAO,EAAG4E,8DAC5DhK,EAAqBC,EAAM+J,EAAK9J,oBAA7CC,OAAQC,UACRgF,EAES,IAAXjF,MACO6J,EAAKC,aACTD,EAAKC,WAINzK,EAAciH,KAAIA,EAAI,GACtBjH,EAAcY,KAAIA,EAAI,GACtBZ,EAAcW,GAAQ,KAAOA,EAAS,GACtCX,EAAciG,GAAO,KAAOA,EAAQ,MAErCpK,GAAO6I,EAAU,4CAEJd,qBACI2G,IACjBtD,IACArG,QACIqF,SACCtF,WAGA,KAEKW,EAAMtC,OAEb,GACDsG,aAAa,IAAK,KAClBA,aAAa,IAAK,MACnBgD,GAAO5D,EAAU,kBACT,qBACRuB,EAAM,IACN,KACEsC,GAAY,GAAK,EAAK,iBACdA,GAAY,mBACV,mBACJjH,IAGRmH,EAAQ/D,EAAU,wBACD6F,yBACItD,OAAMrG,iBAEzBkE,YAAYjJ,KACZiJ,YAAYwD,GAEXG,QArBA5M,GAyBT,QAAgB6O,GAAWzD,EAAGrG,EAAGhB,EAAQgE,MAAOtC,0DAAM,GAAIiJ,yDAAM,EAC3DI,EAAMjG,EAAU,yBACHd,qBACI2G,KAChBtD,KACArG,IACDhB,WAGK,KAEK0B,EAAMtC,OAEb,GACFsG,aAAa,KAAM,KACnBA,aAAa,KAAM,MAEnBgD,GAAO5D,EAAU,kBACT,qBACR,IACA,KACE6D,GAAY,GAAK,EAAI3I,EAAU,iBACvB2I,GAAY,mBACV,mBACJjH,IAGRmH,EAAQ/D,EAAU,wBACD6F,yBACItD,OAAMrG,iBAEzBkE,YAAY6F,KACZ7F,YAAYwD,GAEXG,QAtBAkC,GA0BT,QAAgBC,GAAS7I,EAAOC,EAAO4B,MAAOiF,6DAAY2B,4DAErDK,EADa7I,EAAMmD,IAAI,SAACvE,EAAGsB,SAAOH,GAAMG,GAAK,IAAMtB,IAC5BkK,KAAK,IAG5BjC,GAAQkC,SACXF,EAAY/I,EAAwBC,EAAOC,OAExCgJ,GAAOxE,EAAS,IAAIqE,EAAW,kBAAmBjH,MAGnDiF,EAAQoC,SAAU,IAChBC,GAAczD,EAAa+C,EAAKW,QAASvH,KACxCwB,MAAMgE,eAAiB8B,SAGzBE,SACGJ,MAIJnC,EAAQwC,WAAY,IAClBC,GAAqB7D,EAAa+C,EAAKW,QAASvH,GAAO,GAEvD6C,EAAU,IAAS1E,EAAM,OAAMyI,EAAK9J,aAAcmK,MAAgB9I,EAAMX,OAAO,GAAG,OAAMoJ,EAAK9J,WAC3F2J,OAAS7D,EAASC,gBAAwB,eAAgB6E,aAG1DF,GChmBR,QAAgBG,GAAUC,EAAMC,EAAUC,EAAUC,MAC/CC,GAA0B,gBAAbH,GAAwBA,EAAWA,EAASX,KAAK,aAEjEU,GACCnF,UAAWqF,EAASZ,KAAK,OAC1Ba,EACAE,GACA,aACCxF,UAAWuF,IAId,QAAgBE,GAAkB9B,EAAO+B,EAAMC,SACvCT,GAAUvB,GAAQgC,EAAM,IAAKD,EAAM,GAAIE,IAG/C,QAAgBC,GAAkBtC,EAAOuC,EAAMC,SACvCb,GAAU3B,GAAQ,EAAGwC,IAAQ,EAAGD,GAAOF,IAG/C,QAAgBI,IAAcC,EAAWC,EAAOC,EAAOC,MAClDC,GAAYH,EAAQC,EACpB3Q,EAAOyQ,EAAUK,WAAW,WAG/B9Q,GACE8E,OAAQ+L,EAAWE,mBAHV/Q,EAAKgR,aAAa,cAGyBH,GACtDT,GACAJ,IAGeN,EAAUe,GAAY,EAAGG,IAAS,EAAGD,GAAQP,KAI9D,QAAgBa,IAAWC,EAAK9F,EAAGxG,EAAMwF,MAAOL,0DAAO,IACpCpF,EAAqBC,8DAAWC,oBAA7CC,OAAQC,iBACRgF,EACe,SAAjBmH,EAAIC,WACKD,EAAIJ,WAAW,IAGxB1G,MAAOA,EAAOtF,OAAQA,GACvBsM,GACApB,IAIeN,EAAUwB,EADRA,EAAIF,aAAa,aAAaK,MAAM,KAAK,GAAG9L,MAAM,GAAI,IAC3B6F,EAAGrG,GAAIqL,OAG3Cc,GAAM9G,MAAOA,EAAOtF,OAAQA,EAAQsG,EAAGA,EAAGrG,EAAGA,GAAIqM,GAAepB,KAK3E,QAAgBsB,IAAWxC,EAAK1D,EAAGrG,SACd,WAAjB+J,EAAIqC,UAEUzB,EAAUZ,EADRA,EAAIkC,aAAa,aAAaK,MAAM,KAAK,GAAG9L,MAAM,GAAI,IAC3B6F,EAAGrG,GAAIqL,OAG3CtB,GAAMyC,GAAInG,EAAGoG,GAAIzM,GAAIqM,GAAepB,KAK/C,QAAgByB,IAAYlC,EAAOmC,EAAUC,EAAU9M,EAAUqK,MAC5D0C,MACA5C,EAAY2C,EAASrI,IAAI,SAACvE,EAAGsB,SAAOqL,GAASrL,GAAK,IAAMtB,IAAIkK,KAAK,IAEjEC,KACHF,EAAY/I,EAAwByL,EAAUC,OAEzCE,IAAYtC,EAAMJ,MAAOtM,EAAE,IAAMmM,GAAY8C,GAAe9B,SACnD1J,KAAKuL,GAEjBtC,EAAMf,OAAQ,IACZuD,GAAgBL,EAAS,OAAM7M,MAC/BmN,MAAeN,EAASnM,OAAO,GAAG,QAAOV,EAEvCoN,GACL1C,EAAMf,QACL3L,EAAE,IAAMkP,EAAa/C,EAAYgD,GAClCF,GACA9B,MAEc1J,KAAK2L,SAGdL,GAGR,QAAgBM,IAAeC,EAASvH,UAC/BuH,GAAUtP,EAAG+H,GAAUwG,GAAepB,IC1F/C,QAASoC,IAAkBrS,EAASsS,EAAOC,MAAKC,0DAAW,SAAU3Q,6DAAK4C,GAAWgO,4DAEhFC,EAAc1S,EAAQ2S,WAAU,GAChCC,EAAa5S,EAAQ2S,WAAU,OAE/B,GAAIE,KAAiBP,GAAO,IAC3BQ,YACiB,cAAlBD,EACehT,SAASmJ,gBAAgB,6BAA8B,oBAEvDnJ,SAASmJ,gBAAgB,6BAA8B,cAErE+J,GAAeN,EAAUI,IAAkB7S,EAAQiR,aAAa4B,GAChEG,EAAQV,EAAMO,GAEdI,iBACYJ,OACTE,KACFC,QACG,SACFT,EAAI,IAAO,WACRQ,EAAe,IAAMC,aACjBE,GAAOV,YACT,eACA,cACJ,SAGJ3Q,OACF,KAAmBA,OAGf,GAAIyE,KAAK2M,KACEvJ,aAAapD,EAAG2M,EAAS3M,MAG7B4C,YAAY4J,GAErBjR,IACS6H,aAAamJ,eAA4BG,SAEzCtJ,aAAamJ,EAAeG,UAIjCN,EAAaE,GAGtB,QAAgBnI,IAAUzK,EAASwJ,KAC1BA,MAAMiB,UAAYjB,IAClBA,MAAM2J,gBAAkB3J,IACxBA,MAAM4J,YAAc5J,IACpBA,MAAM6J,aAAe7J,IACrBA,MAAM8J,WAAa9J,EAG5B,QAAS+J,IAAWhJ,EAAciJ,MAC7BC,MACAC,OAEKnK,IAAI,eACRqG,GAAO5P,EAAQ,GACfmK,EAASyF,EAAKxG,WAEdsJ,SAAaE,WAET,GAAKhD,QACeyC,mBAAqBrS,8BAErCuG,KAAKqM,KACJrM,MAAMmM,EAAavI,MAEzBwJ,aAAajB,EAAa9C,QAG9BgE,GAAUrJ,EAAaoI,WAAU,YAExBpJ,IAAI,SAACmJ,EAAapM,KAClB,GAAGqN,aAAaF,EAAYnN,GAAIoM,EAAY,MAC/CpM,GAAG,GAAKmN,EAAYnN,KAGvBsN,EAGR,QAAgBC,IAAiB1J,EAAQ2J,EAAYC,MACpB,IAA7BA,EAAkB3Q,WAEjB4Q,GAAiBT,GAAWO,EAAYC,EACzCD,GAAW1K,YAAce,MACpB8J,YAAYH,KACZ5K,YAAY8K,eAKT,WACPA,EAAe5K,YAAce,MACxB8J,YAAYD,KACZ9K,YAAY4K,KAElBI,KC/GG,QAASC,IAAaC,EAAU9H,MAClC5E,GAAI7H,SAASwU,cAAc,OAC7B7K,MAAQ,mBACN8K,GAAO,GAAIC,MAAKjI,GAAOzK,KAAM,iCAC7B2S,EAAM1T,OAAO2T,IAAIC,gBAAgBJ,KACnCK,KAAOH,IACPI,SAAWR,WACJ9T,KAAK4I,YAAYxB,KACxBmN,mBACS,oBACDvU,KAAK2T,YAAYvM,UACnB+M,IAAIK,gBAAgBN,IACzB,KAGJ,QAAgBO,IAAiBC,MAC5BC,GAAQD,EAAIrC,WAAU,KACpBuC,UAAUC,IAAI,qBACdzL,aAAa,QAAS,gCACtBA,aAAa,cAAe,mCAC9B0L,GAAUvM,EAAEwM,OAAO,mBACTC,OAERjM,aAAa+L,EAASH,EAAMM,eAE9BC,GAAY3M,EAAEwM,OAAO,gBACfnM,YAAY+L,GAEfO,EAAUC,UCblB,QAASC,IAAWC,MACfC,GAAS,GAAIC,MAAKF,YACfG,WAAWF,EAAOG,aAAeH,EAAOI,qBACxCJ,EAGR,QAAgBK,IAAYN,MACvBO,GAAKP,EAAKQ,UACVC,EAAKT,EAAKU,WAAa,SAE1BV,EAAKW,eACJF,EAAG,EAAI,GAAK,KAAOA,GACnBF,EAAG,EAAI,GAAK,KAAOA,GACnBhH,KAAK,KAGR,QAAgB+F,IAAMU,SACd,IAAIE,MAAKF,EAAKY,WAiBtB,QAAgBC,IAAgBC,EAAWC,MACtCC,GAAgBC,GAAeH,SAC5BlT,MAAKsT,KAAKC,GAAeH,EAAeD,GAAWK,IAG3D,QAAgBD,IAAeL,EAAWC,MACrCM,GAAqBC,GAAaC,UAC9BxB,GAAWgB,GAAWhB,GAAWe,IAAcO,EAGxD,QAAgBG,IAAeV,EAAWC,SAClCD,GAAUJ,aAAeK,EAAQL,YACpCI,EAAUH,gBAAkBI,EAAQJ,cAGzC,QAAgBc,IAAa9Q,MAAG+Q,2DAC3BC,EAAYC,GAAYjR,SACrB+Q,GAAQC,EAAU9R,MAAM,EAAG,GAAK8R,EAGxC,QAAgBE,IAAoBC,EAAOC,SACnC,IAAI7B,MAAK6B,EAAMD,EAAQ,EAAG,GAIlC,QAAgBb,IAAejB,MAC1BgC,GAAU1C,GAAMU,GACdiC,EAAMD,EAAQE,eACT,KAARD,MACMD,GAAW,EAAKC,GAElBD,EAIR,QAAgBG,IAAQnC,EAAMoC,KACxBC,QAAQrC,EAAKQ,UAAY4B,GC6V/B,QAAgBE,IAAaC,EAAMC,EAAWC,MACzC9O,GAAO+O,OAAO/O,KAAKgP,IAAkBC,OAAO,kBAAKL,GAAKM,SAASC,KAC/DC,EAASJ,GAAiBhP,EAAK,kBAC5BqP,OAAOD,aACFP,UACFC,IAEH,GAAIQ,IAAeF,GC1b3B,QAASG,IAAUxN,MAKX,IAAJA,SACM,EAAG,MAET7G,MAAM6G,UACAyN,UAAW,iBAAkBC,SAAU,QAE5CC,GAAM3N,EAAI,EAAI,GAAK,MACnB3G,SAAS2G,UACJyN,SAAgB,iBAANE,EAAwBD,SAAU,OAGjDxV,KAAKC,IAAI6H,MACT4N,GAAM1V,KAAKsC,MAAMtC,KAAKuC,MAAMuF,WAGxB2N,GAFE3N,EAAE9H,KAAK0C,IAAI,GAAIgT,IAENA,GAGpB,QAASC,IAAuBC,MAAKC,0DAAI,EACpCC,EAAa9V,KAAKsT,KAAKsC,GACvBG,EAAa/V,KAAKsC,MAAMuT,GACxBG,EAAQF,EAAaC,EAErBE,EAAYD,EACZE,EAAW,CAGZF,GAAQ,IACPA,EAAQ,GAAM,UAGKD,KAEVC,EAAM,IACP,GAITA,GAAS,MAEAA,KADC,IAKA,IAAVA,MACU,IACD,OAIR,GADAG,MACIpT,EAAI,EAAGA,GAAKkT,EAAWlT,MACpBC,KAAK+S,EAAaG,EAAWnT,SAEjCoT,GAGR,QAASC,IAAkBC,MAAUC,0DAAS,IACZhB,GAAUe,aAAtCE,OAAgBf,OACjBgB,EAAiBF,EAAWA,EAAStW,KAAK0C,IAAI,GAAI8S,GAAW,EAK7DW,EAAYR,KAFCY,EAAe/W,QAAQ,GAEegX,YAC3CL,EAAUnQ,IAAI,kBAASyJ,GAAQzP,KAAK0C,IAAI,GAAI8S,KAIzD,QAAgBiB,IAAmBC,WAYzBC,GAA0BN,EAAUO,OAOxC,GANAT,GAAYC,GAAkBC,GAE9BQ,EAAeV,EAAU,GAAKA,EAAU,GAGxC1G,EAAQ,EACJ1M,EAAI,EAAG0M,EAAQmH,EAAa7T,OAC1B8T,IACCC,SAAU,EAAKrH,SAEnB0G,MAvBkCY,2DAMtCV,EAAWrW,KAAK4V,kBAAOc,IACvBJ,EAAWtW,KAAK6V,kBAAOa,IAGTP,QAkBfE,GAAY,GAAKC,GAAY,EACpBhB,GAAUe,GAAU,KAC3BU,EAGSX,GAAkBC,EAAUC,GAF5BF,GAAkBC,OAQ3B,IAAGA,EAAW,GAAKC,EAAW,EAAG,IAOjCM,GAAc5W,KAAKC,IAAIqW,EAExBD,IAAYO,GACHtB,GAAUe,GAAU,KACnBM,EAA0BN,EAAUO,KAGrCtB,GAAUsB,GAAa,KACfD,EAA0BC,EAAaP,GACjCzS,UAAUoC,IAAI,mBAAW,EAANzG,SAOzC,IAAG8W,GAAY,GAAKC,GAAY,EAAG,IAInCU,GAAiBhX,KAAKC,IAAIqW,GAC1BW,EAAiBjX,KAAKC,IAAIoW,EAEnBf,IAAU0B,GAAgB,QACjCD,EAGSX,GAAkBY,EAAgBC,GAFlCb,GAAkBY,IAKTpT,UAAUoC,IAAI,mBAAW,EAANzG,UAGnC4W,GAGR,QAAgBe,IAAaC,MAExBC,GAAWC,GAAgBF,SAC5BA,GAAKG,QAAQ,IAAM,EAGTH,EAAKG,QAAQ,GAChBH,EAAK,GAAK,GAIL,EADJA,EAAK,GACUC,GAKX,EADJD,EAAKA,EAAKtX,OAAS,GACJuX,GAAYD,EAAKtX,OAAS,GAiBrD,QAAgBwX,IAAgBE,SACxBA,GAAa,GAAKA,EAAa,GAGvC,QAAgBC,IAAcD,SACtBA,GAAaA,EAAa1X,OAAO,GAAK0X,EAAa,GAG3D,QAAgBE,IAAM/R,EAAKgS,SACnBpY,GAASoY,EAAMnW,SAAWmE,EAAMgS,EAAMC,iBAY9C,QAAgBC,IAAkBC,EAAMC,MAAK1M,2DACxC2M,EAAUD,EAAI9T,OAAO,SAASgU,EAAMC,SAC/BjY,MAAKC,IAAIgY,EAAOJ,GAAQ7X,KAAKC,IAAI+X,EAAOH,GAAQI,EAAOD,aAGzD5M,GAAQ0M,EAAIR,QAAQS,GAAWA,EAGvC,QAAgBG,IAAiBxB,EAAQyB,OASpC,GALAC,GAAepY,KAAK4V,kBAAOc,IAE3B2B,EAAmB,GAAKF,EAAmB,GAC3CG,KAEIvV,EAAI,EAAGA,EAAIoV,EAAkBpV,IAAK,IACrCwV,GAAaH,GAAgBC,EAAmBtV,KACvCC,KAAKuV,SAGZD,GAGR,QAAgBE,IAAiB/I,EAAO6I,SAChCA,GAAatD,OAAO,kBAAKzV,GAAIkQ,IAAO5P,OC1OrC,QAAS4Y,IAAS1P,EAAMzK,KACzBoa,OAAS3P,EAAK2P,cAEfC,GAAgB5P,EAAK2P,OAAO7Y,OAG5B+Y,EAAW7P,EAAK6P,SAChBC,EAAY,GAAI9Y,OAAM4Y,GAAezY,KAAK,SAC1C0Y,gBAGMC,OAID7S,IAAI,eAERzG,EAAEmX,OAEC,IAEFoC,GAAOvZ,EAAEmX,YACNoC,EAAK9S,IAAI,kBAAS/E,OAAMyE,GAAa,EAANA,KAG9B7F,OAAS8Y,EACTG,EAAK7W,MAAM,EAAG0W,GAEdlZ,EAAUqZ,EAAMH,EAAgBG,EAAKjZ,OAAQ,UAVnD6W,OAASmC,CAkBRtZ,GAAEwZ,YACDC,GAAyB/D,SAAS3W,KACpCya,UAAYza,KASbyK,EAAKkQ,YACFA,SAASjT,IAAI,eACdzG,EAAE2Z,IAAM3Z,EAAEK,MAAO,QACCL,EAAE2Z,IAAK3Z,EAAEK,SAA1BA,aAASsZ,YAKRnQ,EAGR,QAAgBoQ,IAAaC,MACxBT,GAAgBS,EAASV,OAAO7Y,OAChCgZ,EAAY,GAAI9Y,OAAM4Y,GAAezY,KAAK,GAE1CmZ,UACKD,EAASV,OAAOzW,MAAM,GAAI,YACxBmX,EAASR,SAAS5S,IAAI,wBAExB,UACE6S,EAAU5W,MAAM,GAAI,aACjB1C,EAAEwZ,oBAKbK,GAASE,aACFA,iBAEA,QACA,MAKPF,EAASH,aACFA,iBAEA,MACF,QACE,MAKHI,EAGR,QAAgBE,IAAmBC,MAAYd,6DAAWe,6DACrDC,EAAeF,EAAad,EAAO7Y,MACpC6Z,IAAgB,IAAGA,EAAe,MACjCC,GAAiBD,EAAeE,GAEhCC,YACDJ,EAAU,IAERK,GAAiB9Z,KAAK4V,kBAAO8C,EAAO1S,IAAI,kBAAS7D,GAAMtC,aAC1CG,KAAKsT,KAAKwG,EAAeH,SAG1BjB,GAAO1S,IAAI,SAAC7D,EAAOY,aAC1B,IACAlD,OAAS8Z,IAEbF,EAOA1W,EAAI8W,GAAmB,MACjB,MAPNF,EAAe,EAAI,EACbxX,EAAMF,MAAM,EAAG0X,EAAe,GAAK,OAEnCxX,EAAMF,MAAM,EAAG0X,GAAkB,MAQrCxX,IC3GT,QAAS4X,SAAehB,0DAAY,OAAQnS,eAAQ8C,qBACjC,eAAdqP,KACKza,KAAO,OACR,GAAI0b,IAAUpT,EAAQ8C,IAGzBuQ,GAAWlB,GAKT,GAAIkB,IAAWlB,GAAWnS,EAAQ8C,gBAJhCwQ,MAAM,yBAA2BnB,IC1B3C,SAAqBoB,EAAKvU,OACX,KAARA,IAAiBA,KACtB,IAAIwU,GAAWxU,EAAIwU,QAEnB,IAAKD,GAA2B,mBAAb7d,UAAnB,CAEA,GAAI+d,GAAO/d,SAAS+d,MAAQ/d,SAASge,qBAAqB,QAAQ,GAC9DrU,EAAQ3J,SAASwU,cAAc,QACnC7K,GAAM3H,KAAO,WAEI,QAAb8b,GACEC,EAAKrI,WACPqI,EAAKvU,aAAaG,EAAOoU,EAAKrI,YAKhCqI,EAAK1U,YAAYM,GAGfA,EAAMsU,WACRtU,EAAMsU,WAAWC,QAAUL,EAE3BlU,EAAMN,YAAYrJ,SAASme,eAAeN,46IdT9C7U,GAAEwM,OAAS,SAACtM,EAAK3B,MACZpH,GAAUH,SAASwU,cAActL,OAEhC,GAAIzC,KAAKc,GAAG,IACZ6B,GAAM7B,EAAEd,MAEF,WAANA,IACD2C,GAAKC,YAAYlJ,OAEf,IAAU,WAANsG,EAAgB,IACpB6C,GAAMN,EAAEI,KACRG,WAAWC,aAAarJ,EAASmJ,KAC7BD,YAAYC,OAEJ,WAAN7C,EACQ,qBAAR2C,iBAAAA,YACFK,KAAKL,GAAKM,IAAI,cACZC,MAAMC,GAAQR,EAAIQ,KAGlBnD,IAAKtG,KACPsG,GAAK2C,IAGLS,aAAapD,EAAG2C,SAInBjJ,GCxBD,IAAMie,kBAEN,UACG,QACF,SACC,kBAGF,UACG,QACF,SACC,eAGI,gBACC,gBACC,iBAEC,IAyBHC,GAA4B,IAI5B3B,IAA4B,OAAQ,OAWpCpQ,GAA+B,EAS/BgR,GAAqB,EAI5BgB,IAAwB,aAAc,OAAQ,SAAU,MAAO,SACpE,SAAU,QAAS,cAAe,SAAU,UAAW,aAAc,aAKzDC,QACPD,QACCA,OACDA,cACOA,YARiB,UAAW,UAAW,UAAW,UAAW,iBAUlEA,IAIKja,GAAcX,KAAK8D,GAAK,IctGhBgX,oCAEnBlU,OAAAA,aAAS,WACTmU,OAAAA,kCAEKnU,OAASA,OACTmU,OAASA,OACTC,UAAY,QACZC,WAAa,QACbC,mBACAC,gBAAkB,OAElBrT,EAAI,OACJrG,EAAI,OAEJ7E,IAAM,OACNI,KAAO,OAEPoe,wDAIAC,qDAIAnb,YACAob,qEAIArJ,UAAY3M,EAAEwM,OAAO,cACjByJ,KAAK3U,iBACF,8JAKP4U,eAEAC,MAAQF,KAAKtJ,UAAU1V,cAAc,eACrCmf,cAAgBH,KAAKtJ,UAAU1V,cAAc,yBAE7CqK,OAAO+U,iBAAiB,aAAc,aACrCH,sDAKFC,QACDF,MAAKnQ,YACF6G,UAAU9L,aAAa,mBAAoBoV,KAAKnQ,SAEnDmQ,KAAKJ,2BACYI,KAAKN,uBAAsBM,KAAKP,UAExCO,KAAKP,qBAAoBO,KAAKN,4BAErCQ,MAAMvJ,UAAYuJ,OAClBC,cAAcxJ,UAAY,QAE1BgJ,WAAWlV,IAAI,SAAC4V,EAAK7Y,MACnB0B,GAAQoX,EAAKd,OAAOhY,IAAM,QAC5B0M,EAA0B,IAAlBmM,EAAIE,WAAmBF,EAAIE,UAAYF,EAAIE,UAAYF,EAAInM,MAEnEsM,EAAKzW,EAAEwM,OAAO,wCAEWrN,iDAE6B,IAAVgL,GAAeA,EAAQA,EAAQ,6BAC3EmM,EAAIH,MAAQG,EAAIH,MAAQ,QAGvBC,cAAc/V,YAAYoW,+CAK5BjV,GAAQyU,KAAKtJ,UAAU+J,iBAEtBpf,IAAM2e,KAAK9Z,EAAI8Z,KAAKtJ,UAAUgK,adIU,OcFxCjf,KAAOue,KAAKzT,EAAIhB,EAAM,KACvBoV,GAAUX,KAAK3U,OAAOoV,YAAclV,EAEpCqV,EAAUZ,KAAKtJ,UAAU1V,cAAc,mBAExCgf,KAAKve,KAAO,IACNiJ,MAAMjJ,oBAAsB,EAAIue,KAAKve,gBACxCA,KAAO,MACN,IAAGue,KAAKve,KAAOkf,EAAS,IAE1BE,kBADQb,KAAKve,KAAOkf,WAEhBjW,MAAMjJ,KAAOof,OAEhBpf,KAAOkf,SAEJjW,MAAMjJ,6CAIN8K,EAAGrG,MAAGga,6DAAYP,4DAAiB9P,0DAAS,OAChD4P,UAAYS,EAAM9G,UAClBsG,WAAaQ,EAAMhM,WACnByL,WAAaA,OACbpT,EAAIA,OACJrG,EAAIA,OACJ0Z,gBAAkBM,EAAMY,YAAc,OACtCjR,MAAQA,OACRkR,iDAIArK,UAAUhM,MAAMrJ,IAAM,WACtBqV,UAAUhM,MAAMjJ,KAAO,WACvBiV,UAAUhM,MAAMS,QAAU,2CAI1BuL,UAAUhM,MAAMrJ,IAAM2e,KAAK3e,IAAM,UACjCqV,UAAUhM,MAAMjJ,KAAOue,KAAKve,KAAO,UACnCiV,UAAUhM,MAAMS,QAAU,aX5H3B6V,iBACS,eACN,iBACE,cACH,iBACG,iBACA,gBACD,wBACM,iBACL,kBACC,gBACF,eACD,uBACM,sBACD,WA8BD3X,GAAW,SAACH,SAEpB,4BAA6BY,KAAKZ,iCACE+X,KAAK/X,GAC1CuB,IAAI,SAAC8B,EAAG/E,SAAa,KAANA,EAAU/B,OAAO8G,GAAG5C,SAAS,IAAM,MAClDlB,OAAO,SAACyY,EAAGC,YAAUD,EAAIC,IAErBH,GAAiB9X,IAAUA,GC9CtBmG,GAAmB,EAC1BT,GAAe,EACfjB,GAAkB,GACXE,GAAY,GACnBc,GAAkB,UAClBb,GAAY,UAkmBPsT,QACH,SAACtQ,MACHuQ,SACiB,UAAlBvQ,EAAKwB,aACUxB,EAAKqB,aAAa,eAC5BrB,EAAKmB,WAAW,OAEpBqP,GAAUxQ,EAAK+C,qBACXnJ,MAAM/F,KAAO,YACb+F,MAAMS,QAAU,MAErBkW,KACMzW,aAAa,YAAayW,GAE5BC,OAGD,SAACxQ,MACHuQ,SACiB,YAAlBvQ,EAAKwB,aACUxB,EAAKqB,aAAa,eAC5BrB,EAAKmB,WAAW,OAEpBqP,GAAUxQ,EAAK+C,YACf3O,EAAS4L,EAAKqB,aAAa,KAC3BxN,EAAOmM,EAAKqB,aAAa,iBACrBvH,aAAa,IAAKpB,SAAStE,GJ7jBA,KI8jB3B0F,aAAa,OAAQjG,KACrB+F,MAAMS,QAAU,MAErBkW,KACMzW,aAAa,YAAayW,GAE5BC,eAGO,SAACxQ,MACXuQ,SACiB,YAAlBvQ,EAAKwB,aACUxB,EAAKqB,aAAa,eAC5BrB,EAAKmB,WAAW,OAEpBqP,GAAUxQ,EAAK+C,YACf3O,EAAS4L,EAAKqB,aAAa,KAC3BxN,EAAOmM,EAAKqB,aAAa,iBACrBvH,aAAa,IAAKpB,SAAStE,GJhlBA,KIilB3B0F,aAAa,OAAQjG,KACrB+F,MAAMS,QAAU,MAErBkW,KACMzW,aAAa,YAAayW,GAE5BC,IAIEC,QACH,SAACzQ,EAAMwQ,MACTD,SACiB,UAAlBvQ,EAAKwB,aACUxB,EAAKqB,aAAa,eAC5BrB,EAAKmB,WAAW,OAEpBuP,IAAc,IAAK,IAAK,QAAS,iBAC9BrG,OAAOrK,EAAK0Q,YACjB/H,OAAO,kBAAQ+H,GAAW9H,SAAS+H,EAAKrI,OAASqI,EAAKC,YACtDjX,IAAI,cACIG,aAAa6W,EAAKrI,KAAMqI,EAAKE,aAGpCN,KACMzW,aAAa,YAAayW,QAI7B,SAACvQ,EAAMwQ,MACTD,SACiB,YAAlBvQ,EAAKwB,aACUxB,EAAKqB,aAAa,eAC5BrB,EAAKmB,WAAW,OAEpBuP,IAAc,KAAM,aACjBrG,OAAOrK,EAAK0Q,YACjB/H,OAAO,kBAAQ+H,GAAW9H,SAAS+H,EAAKrI,OAASqI,EAAKC,YACtDjX,IAAI,cACIG,aAAa6W,EAAKrI,KAAMqI,EAAKE,aAGpCN,KACMzW,aAAa,YAAayW,gBAIrB,SAACvQ,EAAMwQ,MACjBD,SACiB,YAAlBvQ,EAAKwB,aACUxB,EAAKqB,aAAa,eAC5BrB,EAAKmB,WAAW,OAEpBuP,IAAc,KAAM,aACjBrG,OAAOrK,EAAK0Q,YACjB/H,OAAO,kBAAQ+H,GAAW9H,SAAS+H,EAAKrI,OAASqI,EAAKC,YACtDjX,IAAI,cACIG,aAAa6W,EAAKrI,KAAMqI,EAAKE,aAGpCN,KACMzW,aAAa,YAAayW,KCrtBxB9O,GAAgB,IAChBU,GAAgB,IAChB1B,GAAuBgB,GACvB6C,GAAsB,IAEtBjE,GAAa,SCHpBiD,SACC,yBACE,iBAEA,wBACC,uBACE,iBSVCoC,GAAU,0sDCSFoL,yBACRvW,EAAQ8C,sBAEd9C,OAA2B,gBAAXA,GAClBtK,SAASC,cAAcqK,GACvBA,IAEG2U,KAAK3U,iBAAkBwW,mBACtB,IAAIC,OAAM,uDAGZC,aAAe5T,OAEf+R,MAAQ/R,EAAQ+R,OAAS,QACzBnd,KAAOoL,EAAQpL,MAAQ,QAEvB8a,SAAWmC,KAAKgC,YAAY7T,EAAQX,WACpCA,KAAOwS,KAAKiC,iBAAiBjC,KAAKnC,eAElC2B,OAASQ,KAAKkC,eAAe/T,EAAQqR,OAAQQ,KAAKjd,WAElD6W,oBACS,aACD,cACCzL,EAAQgU,aAAe,cACC,KAApBhU,EAAQiU,QAA2BjU,EAAQiU,QAAU,kBACrDjU,EAAQkU,iBAAmB,QAGxCC,SAAWC,KAAKC,MAAMD,KAAKE,UAAUtD,QACtC5b,GAAIyc,KAAKsC,cACRI,YAAYvU,GACb6R,KAAKE,MAAM5b,WAAYd,YAAc,GACrCwc,KAAKpG,OAAO+I,aAAYpf,EAAEM,aAAe,QACxC+e,UAAYzU,EAAQlI,QAAU1C,EAAEsf,gBAEhCC,cACA3U,gBAEA4U,YAAc3D,GAEhBY,KAAKpG,OAAOuI,mBACTa,kBAGDC,UAAU9U,kDAGJX,SACJA,4CAGSA,SACTA,0CAGOgS,EAAQzc,MAChBmgB,gBACI1D,OAAc5a,OAAO0a,GAAevc,KACvCogB,QAAQ,SAACre,MACToE,GAAQG,GAASvE,EACnB8E,GAAaV,KAGJzB,KAAKyB,WAFTka,KAAK,IAAMte,EAAS,6BAKvBoe,wFASHjd,EAAS+Z,KAAK4C,eACbC,WAAa5c,OACbA,OAASA,EAASrC,EAAeoc,KAAKsC,eAGtCe,YAAc,iBAAMC,GAAKC,MAAK,WAC5BnD,iBAAiB,SAAUJ,KAAKqD,oBAChCjD,iBAAiB,oBAAqBJ,KAAKqD,sDAI3CG,oBAAoB,SAAUxD,KAAKqD,oBACnCG,oBAAoB,oBAAqBxD,KAAKqD,kDAKhDI,qBACAC,mBACA5D,mBAEAyD,MAAK,GAAO,gDAKZlY,OAAOsL,UAAY,MAEpB/K,WACKoU,KAAK3U,iBACF,kBAGT2U,MAAK2D,qBACFphB,QAAWgJ,MAAOyU,KAAK2D,iBAAmB,YAG3CjN,UAAY3M,EAAEwM,OAAO,MAAO3K,8CAI5BgY,IAAM,GAAIrE,YACNS,KAAKtJ,iBACLsJ,KAAKR,cAETqE,+FAKDC,0DAAuBC,yDACvBD,IAAmBniB,EAASqe,KAAK3U,eAIhCqY,mBAEAM,KAAKF,QACLG,qBACAC,uBAEAC,WAAWhB,QAAQ,kBAAKjC,GAAErB,MAAMS,EAAK8D,iBAErCC,OAAOrE,KAAKmE,YAAY,GAE1BJ,SACGvW,KAAOwS,KAAKnC,oBACN,aAAYyG,OAAOhE,EAAK9S,OAASwS,KAAK+C,mBAG7CwB,oBAEAC,gBAAgBT,+EAMhBU,UAAYniB,EAAuB0d,KAAK3U,aACxCE,MAAQyU,KAAKyE,UAAY3gB,EAAckc,KAAKsC,kDAI9CtC,KAAK9J,UACFQ,UAAUvB,YAAY6K,KAAK9J,QAE7B3S,GAAIyc,KAAKsC,cAERpM,IAAM9K,EACV4U,KAAKtJ,UACL,qBACAsJ,KAAKyE,UACLzE,KAAK6C,iBAEDpS,QAAUjF,EAAYwU,KAAK9J,KAE7B8J,KAAKE,MAAM5b,cACRogB,QAAUzW,EACd,QACA1K,EAAEE,QAAQhC,KACV8B,EAAEE,QAAQpC,IACV2e,KAAKE,gBAEM3c,EAAEohB,mBACN,aACFphB,EAAEohB,oBAKLtjB,GAAMiC,EAAaC,QAClB6gB,SAAW1Y,EACfsU,KAAKjd,KAAO,sCACCY,EAAcJ,QAAOlC,OAGhC2e,KAAKpG,OAAO+I,gBACP3C,KAAK/Z,OAAS1C,EAAEG,SAAS3B,YAC3B6iB,WAAalZ,EACjB,4BACa/H,EAAcJ,QAAOlC,QAIjC2e,KAAKE,MAAM5b,aAAe4R,IAAI9L,YAAY4V,KAAK0E,cAC7CxO,IAAI9L,YAAY4V,KAAKoE,UACvBpE,KAAKpG,OAAO+I,iBAAmBzM,IAAI9L,YAAY4V,KAAK4E,iBAElDC,gBAAgBlhB,EAAcJ,GAAID,EAAaC,4CAGrCgJ,EAAGrG,QACb0d,IAAI1Y,UACLqB,IACArG,kDAIoBie,WAAa,GAAIW,oCAEnCtX,GACFA,WACKmR,MAAM,2BAEVnR,KAAOwS,KAAKgC,YAAYxU,QACxBwW,YACAK,OAAOrE,KAAKmE,WAAYnE,KAAKpG,OAAOwI,qDAGnC+B,yDAAWnE,KAAKmE,WAAY/B,4DAC/BpC,MAAKpG,OAAOuI,kBAETa,SAASvY,IAAI,kBAAKnC,GAAEgC,WAAW6K,YAAY7M,QAG7C2M,QAEOkO,QAAQ,cACElO,EAAkBrQ,OAAOsc,EAAEoD,OAAOlC,MAEpDnN,EAAkB3Q,OAAS,MACZ0b,KAAKtJ,UAAWsJ,KAAK9J,IAAKjB,cAChC,aACCkO,QAAQ,kBAAKjC,GAAE6D,WACrBC,ahB3LiC,SgB8L5B7B,QAAQ,kBAAKjC,GAAE6D,cACrBC,iDAKHhF,KAAKpG,OAAOuI,mBACTf,mBACA6D,0GAMSlB,yDACX/D,MAAKpG,OAAOuI,aAEb4B,SACGmB,mBAEAC,eACEnF,KAAKoF,WAAWC,KAAKrF,SACrBA,KAAKsF,YAAYD,KAAKrF,SACtBA,KAAKuF,UAAUF,KAAKrF,SACpBA,KAAKwF,aAAaH,KAAKrF,SACvBA,KAAKyF,YAAYJ,KAAKrF,gBAGpBI,iBAAiB,UAAW,SAACsF,GAClC5jB,EAAoB6jB,EAAKjP,eACvBgP,GAAK1jB,OAAO4jB,MACbD,EAAKR,WAAWO,EAAEG,YACfV,WAAWO,EAAEG,mmBA2BlBC,GAAW7P,GAAiB+J,KAAK9J,QACxB8J,KAAKE,OAAS,SAAU4F,aClTlBC,0BACR1a,EAAQO,+EACbP,EAAQO,yDAGLA,4FACOA,QAEXgO,OAAOoM,gBAAkBpa,EAAKqa,oBAAsBD,oBACpDpM,OAAOsM,UAAYta,EAAKsa,WAAa,QACrCtM,OAAOuM,gBAAkBva,EAAKua,iBAAmB,6CAIlDC,EAAIpG,KAAK8C,MACToD,EAAYlG,KAAKpG,OAAOsM,YAC1BG,kBAEEC,GAAYtG,KAAKxS,KAAK2P,OAAO1S,IAAI,SAAC7D,EAAOY,MACxC+e,GAAQ,WACP/Y,KAAK6P,SAAS5S,IAAI,eACbib,EAAEvK,OAAO3T,MAEX+e,EAAO3f,KACb6S,OAAO,kBAAczV,GAAE,IAAM,IAE5BwiB,EAASF,KACVA,EAAUhiB,OAAS4hB,EAAW,GAEtBO,KAAK,SAAC7d,EAAGa,SAAeA,GAAE,GAAKb,EAAE,OAElC0d,EAAU5f,MAAM,EAAGwf,EAAU,MAGlCQ,GAAiB,CAFLJ,GAAU5f,MAAMwf,EAAU,GAGhCzb,IAAI,eAAwBzG,EAAE,OACjCyD,MAAMif,EAAgB,cACxBlH,OAAO0G,EAAU,GAAK,SAG1B/I,YACK1S,IAAI,cACR4b,YAAY5e,KAAK5B,EAAM7B,EAAE,OACzBmZ,OAAO1V,KAAKzD,EAAE,QAGf2iB,WAAaP,EAAEC,YAAY5d,OAAO,SAACG,EAAGa,SAAMb,GAAIa,GAAG,QAEhD0C,UACD6T,KAAKzU,MAAQ,IACbyU,KAAK/Z,OAAS,qDAKdmgB,EAAIpG,KAAK8C,WACR8B,WAAWgC,YAAc,QACzBC,aAAeT,EAAEC,YAAY3f,MAAM,EAAGsZ,KAAKpG,OAAOuM,oBAEnD/hB,GAAQ,EACR8B,EAAI,OACH2gB,aAAapc,IAAI,SAACzG,EAAGwD,MACrBsf,GAAW,IACXC,EAAUtiB,KAAKsC,OACjBigB,EAAKzb,MAAQzH,EAAckjB,EAAK1E,WAAWwE,EAEzCE,GAAKH,aAAaviB,OAASyiB,MACnBC,EAAKzb,MAAMyb,EAAKH,aAAaviB,QAEtCF,EAAQ2iB,MACF,KACH,OAEFxa,GAAIua,EAAW1iB,EAAQ,EACvBwC,EAAQogB,EAAKpN,OAAOyI,gBAAkB9b,EAAe6f,EAAEjJ,OAAO3V,GAAIsf,EAAS,IAAMV,EAAEjJ,OAAO3V,GAC1F+Y,EAAYyG,EAAKpN,OAAOoM,eAAiBgB,EAAKpN,OAAOoM,eAAehiB,GAAKA,EACzEiM,EAAMjC,EACTzB,EACArG,EACA,EACA8gB,EAAKxH,OAAOhY,GACTZ,OAAU2Z,GACb,KAEIqE,WAAWxa,YAAY6F,gBApFe2R,ITHjC3J,GAAqB,EAErBG,GAAe,IACfD,GAAa,MAEbM,IAAe,UAAW,WAAY,QAAS,QAAS,MACpE,OAAQ,OAAQ,SAAU,YAAa,UAAW,WAAY,YAIlDwO,IAAmB,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OCNpEnN,oCAEJoN,WAAAA,aAAa,SACbC,eAAAA,aAAiB,KACjB9N,IAAAA,UAEAC,IAAAA,QACA8N,IAAAA,aACAC,IAAAA,gCAEKF,eAAiBA,OACjB9N,UAAYA,OAEZ+N,aAAeA,OACf9N,QAAUA,OAEV+N,gBAAkBA,OAElBC,cACAnK,eAEA+J,WAAaA,OACbA,WAAyC,kBAArBlH,MAAKkH,WAC3BlH,KAAKkH,aAAelH,KAAKkH,gBAEvBnG,qDAGEvT,QACFA,KAAOA,GAAQwS,KAAK1G,wCAGpBjO,QACAkc,MAAQ7b,EAAasU,KAAKkH,WAAYlH,KAAKmH,eAAgB9b,uCAI3DgZ,OAAOrE,KAAKxS,WACZga,QAAUxH,KAAKxS,oCAGdA,mBACD8Z,MAAQtH,KAAKoH,aAAa5Z,QAE1B+Z,MAAMX,YAAc,QACpBU,MAAMnE,QAAQ,cACboE,MAAMnd,YAAYlJ,UAEnBic,OAAOgG,QAAQ,cACdoE,MAAMnd,YAAYlJ,yCAIlBkhB,mEACDrB,aACDsG,YACDjF,OACgBpC,KAAKqH,gBAAgBrH,KAAKxS,WAEtC6Z,WAIL7N,4BAEU,qCACChM,SACLA,GAAKia,aAAahd,IAAI,SAAC2b,EAAG5e,MAC5Bd,GAAQoF,EAASsa,EAAG,aAAc5Y,EAAKgS,OAAOhY,GAAI,OAAQgG,EAAKka,sBAC7Dhd,MAAMid,WAAa,iBAClBjhB,8BAIOkhB,SACR5H,MAAKsH,MAAM7c,IAAI,SAAC/D,EAAOc,SAAM6L,IAAe3M,EAAOkhB,EAAQH,aAAajgB,8BAIpE,mCACCgG,SACLA,GAAKia,aAAahd,IAAI,SAAC2b,EAAG5e,MAC5Bd,GAAQoF,EAASsa,EAAG,WAAY,OAAQ5Y,EAAKgS,OAAOhY,aAClDkD,MAAMid,WAAa,iBAClBjhB,8BAIOkhB,SACR5H,MAAKsH,MAAM7c,IAAI,SAAC/D,EAAOc,SAC7B6L,IAAe3M,EAAOkhB,EAAQH,aAAajgB,mCAKjC,wCACCgG,oBACLA,GAAKqa,WAAWpd,IAAI,SAAC8B,EAAG/E,SAEpB2F,GAAcZ,EADhB,EACsBiB,EAAKsa,OAAOtgB,GACzC8Y,EAAKjH,UAAU0O,UAAWzH,EAAKjH,UAAU2O,SAAUxa,EAAKgS,OAAOhY,gCAKlDogB,MACZA,EAAS,6BAID,+BACCpa,oBACLA,GAAKya,UAAUxd,IAAI,SAACyd,EAAU1gB,SACpC0H,GAAMgZ,EAAU1a,EAAK2P,OAAO3V,GAAIwf,EAAK3N,UAAU9N,OAC7C6D,KAAM4X,EAAK3N,UAAUjK,KAAMD,IAAK6X,EAAK3N,UAAUlK,IAAKF,eAAgB+X,EAAK3N,UAAUpK,6CAIvE2Y,MACXO,GAASP,EAAQK,UACjBG,EAAYR,EAAQzK,OACpBkL,EAASrI,KAAKwH,QAAQS,UACtBK,EAAYtI,KAAKwH,QAAQrK,SAEVhX,EAAqBkiB,EAAQF,iCACvBhiB,EAAqBmiB,EAAWF,uCAEpD/D,kBACOgE,SACHD,IAGFpI,KAAKsH,MAAM7c,IAAI,SAAC/C,EAAMF,SACrBgK,GACN9J,EAAMygB,EAAO3gB,GAAI6gB,EAAO7gB,0BAOf,+BACCgG,oBACLA,GAAKya,UAAUxd,IAAI,SAACyd,EAAU1gB,SACpC8H,GAAM4Y,EAAU1a,EAAK+a,WAAW/gB,GAAIme,EAAKtM,UAAUpT,QACjDmJ,KAAMuW,EAAKtM,UAAUjK,KAAMD,IAAKwW,EAAKtM,UAAUlK,kCAInCyY,MACXO,GAASP,EAAQK,UACjBG,EAAYR,EAAQW,WACpBF,EAASrI,KAAKwH,QAAQS,UACtBK,EAAYtI,KAAKwH,QAAQe,aAEVpiB,EAAqBkiB,EAAQF,iCACvBhiB,EAAqBmiB,EAAWF,uCAEpD/D,kBACOgE,aACCD,IAGNpI,KAAKsH,MAAM7c,IAAI,SAAC/C,EAAMF,SACrB4J,GACN1J,EAAMygB,EAAO3gB,GAAI6gB,EAAO7gB,6BAOf,kCACCgG,oBACLA,GAAK/C,IAAI,kBACf8E,GAAQhM,EAAE2kB,SAAU3kB,EAAEqD,MAAO4hB,EAAKnP,UAAU9N,OAC1CiE,SAAUjM,EAAE4K,QAAQqB,SAAUJ,KAAM,OAAQJ,SAAU,uCAG1C4Y,SACWzhB,EAAqB6Z,KAAKwH,QAASI,kBAAvDJ,gBAEFW,YAAiB1d,IAAI,kBAAKzG,GAAEkkB,WAC5BE,EAAYR,EAAQnd,IAAI,kBAAKzG,GAAE4C,QAC/B6hB,EAAab,EAAQnd,IAAI,kBAAKzG,GAAEmK,UAEhCka,EAASrI,KAAKwH,QAAQ/c,IAAI,kBAAKzG,GAAEkkB,uBAEhC7D,OAAOgE,EAAO5d,IAAI,SAAC0E,EAAK3H,mBAEjB6gB,EAAO7gB,SACV4gB,EAAU5gB,WACRihB,EAAWjhB,OAIfwY,KAAKsH,MAAM7c,IAAI,SAAC/C,EAAMF,SACrBgK,GACN9J,EAAMygB,EAAO3gB,GAAI6gB,EAAO7gB,6BAOf,kCACCgG,oBACLA,GAAK/C,IAAI,kBACfiF,GAAQ1G,EAAE0f,SAAU1f,EAAE2f,OAAQC,EAAKvP,UAAU9N,MAC5CvC,EAAEpC,OAAQ4I,SAAUxG,EAAEmF,QAAQqB,uCAGjBoY,SACWzhB,EAAqB6Z,KAAKwH,QAASI,kBAAvDJ,gBAEFW,YAAiB1d,IAAI,kBAAKzG,GAAE2kB,SAC5BP,EAAYR,EAAQnd,IAAI,kBAAKzG,GAAE4C,QAC/BiiB,EAAYjB,EAAQnd,IAAI,kBAAKzG,GAAE0kB,WAC/BD,EAAab,EAAQnd,IAAI,kBAAKzG,GAAEmK,UAEhCka,EAASrI,KAAKwH,QAAQ/c,IAAI,kBAAKzG,GAAE2kB,SACjCG,EAAY9I,KAAKwH,QAAQ/c,IAAI,kBAAKzG,GAAE0kB,gBAEnCrE,OAAOgE,EAAO5d,IAAI,SAAC0E,EAAK3H,mBAEjBshB,EAAUthB,UACZ6gB,EAAO7gB,SACR4gB,EAAU5gB,WACRihB,EAAWjhB,UAIlB6f,kBAECC,MAAM7c,IAAI,SAACmH,EAAWpK,KACR6f,EAAgBziB,OAAO+M,GACxCC,EAAWiX,EAAUrhB,GAAI2gB,EAAO3gB,GAAI6gB,EAAO7gB,OAItC6f,2BAKI,iBAAoB,sBAAwBrH,KAAK3G,UAAUxJ,6BAC1DrC,gBACuDwS,KAAK3G,UAAnExJ,IAAAA,MAAOkZ,IAAAA,SAAUC,IAAAA,UAAWC,IAAAA,WAAY/jB,IAAAA,OAEzCqH,IAFiD2c,WAEjChjB,EAAI,cAEnBijB,0BAEAC,KAAK3e,IAAI,SAAC4e,EAAMC,GACN,IAAXA,KACGnM,OAAO1V,KACXwG,EAAS,cAAe1B,GARL,GAQyB+L,GAAazI,GAAO,GAAM0Z,wBAE1D,OAKT9e,IAAI,SAACqO,EAAKtR,MACXsR,EAAInU,KAAM,IACR6I,gBACUsL,EAAI0Q,sBACH1Q,EAAI2Q,qBACNjiB,GAETkiB,EAASpc,EAAW,MAAOf,EAAGrG,EAAG+iB,EAAY/jB,EAAQ4T,EAAInU,KAAM6I,KAC9D2b,qBAAqB1hB,KAAKiiB,MAE3BV,MAEF,KACCD,IAGC/I,KAAKmJ,+CAGGvB,MACZA,EAAS,gCAKD,iBAAoB,sCAAwC5H,KAAK3G,UAAUxJ,6BAC1ErC,MACR0T,GAAIlB,KAAK3G,sBACRsQ,SAAW,WACXC,MAAQpc,EAAKqc,WAAWpf,IAAI,SAACvE,EAAG9C,SAC7BwM,GACNpC,EAAKqa,WAAWzkB,GAChB8C,EACAsH,EAAKsZ,SACL5F,EAAEhY,MACFsE,EAAK2P,OAAO/Z,GACZA,EACAoK,EAAKsc,QAAQ1mB,aAEFoK,EAAKxH,mBACJwH,EAAKuc,oBACL7I,EAAEnR,cAITiQ,KAAK4J,gCAEGhC,MACXoC,GAAUpC,EAAQC,WAClBoC,EAAUrC,EAAQiC,WAClBK,EAAatC,EAAQkC,QACrB1B,EAAYR,EAAQzK,OAEpBgN,EAAUnK,KAAKwH,QAAQK,WACvBuC,EAAUpK,KAAKwH,QAAQqC,WACvBQ,EAAarK,KAAKwH,QAAQsC,QAC1BxB,EAAYtI,KAAKwH,QAAQrK,SAERhX,EAAqBgkB,EAASH,iCAC9B7jB,EAAqBikB,EAASH,iCACxB9jB,EAAqBkkB,EAAYH,iCACnC/jB,EAAqBmiB,EAAWF,gCAEpD/D,mBACQ8F,aACAC,UACHC,SACDjC,WAEEpI,KAAKwH,QAAQxhB,mBACZga,KAAKwH,QAAQuC,mBACd/J,KAAKwH,QAAQV,cAGpBO,kBAECC,MAAM7c,IAAI,SAAC4H,EAAK7K,KACF6f,EAAgBziB,OAAOwN,GACxCC,EAAK2X,EAAQxiB,GAAIyiB,EAAQziB,GAAIogB,EAAQd,SAAUoD,EAAW1iB,IACzDxB,SAAU4hB,EAAQ5hB,cAIdqhB,0BAKI,iBAAoB,sCAAwCrH,KAAK3G,UAAUxJ,6BAC1ErC,MACR0T,GAAIlB,KAAK3G,sBACRsQ,SAAW,WACXjZ,SACDwQ,EAAEoJ,gBACA5Z,MAAQR,EACZ1C,EAAKqa,WACLra,EAAKqc,WACL3I,EAAEhY,gBAESgY,EAAE3Q,oBACA2Q,EAAEvQ,kBACNuQ,EAAE7Q,iBAGD6Q,EAAEzQ,iBACDjD,EAAKxH,iBAKb4jB,SACD1I,EAAEqJ,gBACAX,MAAQpc,EAAKqc,WAAWpf,IAAI,SAACvE,EAAG9C,SAC7B4M,GACNxC,EAAKqa,WAAWzkB,GAChB8C,EACAsH,EAAKtI,OACLgc,EAAEhY,MACDgY,EAAEsJ,iBAAmBhd,EAAK2N,OAAO/X,GAAK,GACvCA,MAKImW,OAAO4B,OAAO6E,KAAKtP,OAAO9L,OAAOob,KAAK4J,iCAE9BhC,MACXoC,GAAUpC,EAAQC,WAClBoC,EAAUrC,EAAQiC,WAClBY,EAAY7C,EAAQzM,OAEpBgP,EAAUnK,KAAKwH,QAAQK,WACvBuC,EAAUpK,KAAKwH,QAAQqC,WACvBlW,EAAYqM,KAAKwH,QAAQrM,SAERhV,EAAqBgkB,EAASH,iCAC9B7jB,EAAqBikB,EAASH,iCAC1B9jB,EAAqBwN,EAAW8W,gCAEpDpG,mBACQ8F,aACAC,SACJK,WAEEzK,KAAKwH,QAAQxhB,gBACfga,KAAKwH,QAAQtiB,YAGlBmiB,YAED9N,QAAO/O,KAAKwV,KAAKtP,OAAOpM,WACR+iB,EAAgBziB,OAAOgO,GACxCoN,KAAKtP,MAAOsZ,EAASC,EAASrC,EAAQ5hB,SAAUga,KAAK3G,UAAUhJ,UAG9D2P,KAAK4J,MAAMtlB,aACRslB,MAAMnf,IAAI,SAACwF,EAAKzI,KACF6f,EAAgBziB,OAAO6N,GACxCxC,EAAK+Z,EAAQxiB,GAAIyiB,EAAQziB,OAIrB6f,KS3aWqD,0BACRrf,EAAQO,8EACbP,EAAQO,aACT7I,KAAO,eACP8c,iEAGM1R,MACP5K,GAAIyc,KAAKsC,cACRqI,WAAaxc,EAAQwc,kBAEtBlhB,GAAIuW,KAAK2K,aACX1kB,OAASwD,EAAExD,QlB0D8B,KkBzDzCmH,MAAQ3D,EAAE2D,OAASC,KAEnB3J,SAASvB,MAAQ,KACjB0B,aAAe,KACfgf,WAA0C,GAA5BpZ,EAAExD,OAAmB,GAAVwD,EAAE2D,oDAIzBgZ,GAAIpG,KAAK8C,MAETtJ,IAEF,4BAEYwG,KAAK2K,WAAW1kB,gBACjB+Z,KAAK2K,WAAWvd,OAE3B,6BAEcgZ,EAAEyB,kBACNzB,EAAE0B,cACF9H,KAAKR,SAEb6F,KAAKrF,aAIJmE,WAAa,GAAIW,KAAItL,EACxB/O,IAAI,eACAmgB,GAAYzR,mBAAgBvN,WACxBA,EAAK,GAAIgf,wIAMfxE,GAAIpG,KAAK8C,QAEX+E,gBACAC,aAEE+C,GAAO,IACTxE,YAAY5b,IAAI,SAACyJ,MACd3I,GAAQ+U,EAAK/U,MAAQ2I,EAAQkS,EAAEO,aACjCmB,OAAOrgB,KAAK8D,KACZsc,WAAWpgB,KAAKojB,MACVtf,gGAOL6a,EAAIpG,KAAK8C,WACRpM,UAAU0J,iBAAiB,YAAa,SAACsF,MACzCoF,GAAO9D,EAAK7C,WAAW4G,IAAI,kBAAkBzD,MAC7CjV,EAAMqT,EAAE5iB,UACTgoB,EAAKpR,SAASrH,GAAM,IAElB7K,GAAIsjB,EAAK/O,QAAQ1J,GACjB2Y,EAAO/pB,EAAU+lB,EAAKtQ,WAAYuU,EAAOhqB,EAAUoR,GAEnD9F,EAAI0e,EAAKxpB,KAAOupB,EAAKvpB,KAAO+H,SAAS6I,EAAIF,aAAa,UAAU,EAChEjM,EAAI+kB,EAAK5pB,IAAM2pB,EAAK3pB,IACpB6e,GAAS8G,EAAKkE,iBAAmBlE,EAAKkE,gBAAgB5mB,OAAO,EAC9D0iB,EAAKkE,gBAAgB1jB,GAAKwf,EAAKlE,MAAM3F,OAAO3V,IAAM,KACjD2jB,EAAW/E,EAAEC,YAAY7e,GAAG4e,EAAEO,aAE7B/C,IAAIwH,UAAU7e,EAAGrG,GAAIkT,KAAM8G,EAAOhM,OAAiB,IAATiX,GAAclnB,QAAQ,GAAK,QACrE2f,IAAIyH,oBAlFgCtF,ICIxBuF,0BACRjgB,EAAQO,8EACbP,EAAQO,aACT7I,KAAO,QACPggB,YAAc,IACdgB,KAAO,IAEPlE,+DAGIjU,4FACOA,QACX2f,UAAYvL,KAAKuL,UAAUlG,KAAKrF,WAChCwL,WAAaxL,KAAKwL,WAAWnG,KAAKrF,WAElCyL,WAAa7f,EAAK6f,YAAc,QAChC7R,OAAO8R,WAAa9f,EAAK8f,YAAc,OAEvCtf,UAAYR,EAAKQ,YAAa,oIAK/Bga,GAAIpG,KAAK8C,WACR5d,OAAU8a,KAAK/Z,OAAS+Z,KAAKzU,MAAQyU,KAAK7T,OAAOI,EAAIyT,KAAK7T,OAAOjG,KAE9DhB,GAAsB8a,KAAtB9a,OAAQkH,EAAc4T,KAAd5T,UAEVuf,EAAuBvF,EAAEwF,uBAC7BnE,kBACAmE,uBACEC,GAAW,IAAM7L,KAAKpG,OAAO8R,aAC/BrF,YAAY5b,IAAI,SAAC8b,EAAO/e,MACnBkkB,GAAaG,EACbC,EAAmBvF,EAAQH,EAAEO,WnB+DZ,ImB9DjBta,EAAWyf,EAAkB,IAAM,EAAG,EACtCC,EAAY3f,GAAa0f,EAAkBA,EAC3CE,EAAWH,GAAsBE,EACjC9f,EAAgBjH,EAAmB0mB,EAAYxmB,GAC/CgH,EAAclH,EAAmBgnB,EAAU9mB,GAE3C+mB,EAAe3L,EAAKyD,MAAQ4H,EAAqBnkB,GAEnD0kB,SAASC,QACV7L,GAAKyD,QACIkI,EAAeA,EAAahgB,cAAgBA,IAC9CggB,EAAeA,EAAa/f,YAAcD,MAExCA,IACFC,MAEJkgB,GACe,MAApBN,EACGnf,EAAcuf,EAAUC,EAAQ7L,EAAKnU,OAAQmU,EAAKpb,OAAQkH,EAAWC,GACrEL,EAAekgB,EAAUC,EAAQ7L,EAAKnU,OAAQmU,EAAKpb,OAAQkH,EAAWC,KAExEob,aAAahgB,KAAK2kB,KAClBR,iBAAiBnkB,0CAGX8e,QACAH,EAAEO,yCAGFoF,WAIJhI,KAAO,+CAIRqC,GAAIpG,KAAK8C,MAETtJ,IAEF,eAEA,+BAEgB4M,EAAEqB,oBACRzH,KAAKR,SAEb6F,KAAKrF,aAIJmE,WAAa,GAAIW,KAAItL,EACxB/O,IAAI,eACAmgB,GAAYzR,mBAAgBvN,WACxBA,EAAK,GAAIgf,kDAIAyB,MACbnnB,GAAqB8a,KAArB9a,OAAOumB,EAAczL,KAAdyL,WACPvD,EAAWljB,EAAmBqnB,EAASX,WAAYW,EAASpnB,MAAQ,EAAGC,wBACtDgjB,EAAS3b,EAAKkf,QAAiBvD,EAAShiB,EAAKulB,6CAG1Dnb,EAAK9I,EAAE8kB,EAAK5G,MAClBpV,MACEpH,GAAQ8W,KAAKR,OAAOhY,MACvB8kB,EAAM,IACEhc,EAAM0P,KAAKuM,oBAAoBvM,KAAK8C,MAAM8I,iBAAiBpkB,OAChEkD,MAAM/F,KAAOsE,EAAmBC,EAAO,OACxCsjB,GAAQvrB,EAAU+e,KAAK9J,KACvB3J,EAAImZ,EAAE+G,MAAQD,EAAM/qB,KAAO,GAC3ByE,EAAIwf,EAAEgH,MAAQF,EAAMnrB,IAAM,GAC1B6e,GAASF,KAAK2M,kBAAoB3M,KAAK2M,iBAAiBroB,OAAS,EAClE0b,KAAK2M,iBAAiBnlB,GAAKwY,KAAK8C,MAAM3F,OAAO3V,IAAM,KAClDolB,GAAuC,IAA5B5M,KAAK8C,MAAMuD,YAAY7e,GAAWwY,KAAK8C,MAAM6D,YAAY1iB,QAAQ,QAC3E2f,IAAIwH,UAAU7e,EAAGrG,GAAIkT,KAAM8G,EAAOhM,MAAO0Y,EAAU,WACnDhJ,IAAIyH,kBAEC/a,EAAK,2BACVsT,IAAI3D,YACJvV,MAAM/F,KAAOuE,8CAKdwN,UAAU0J,iBAAiB,YAAaJ,KAAKuL,gBAC7C7U,UAAU0J,iBAAiB,aAAcJ,KAAKwL,8CAG1C9F,MACH5iB,GAAS4iB,EAAE5iB,OACb+pB,EAAS7M,KAAKmE,WAAW4G,IAAI,aAAazD,MAC1CwF,EAAY9M,KAAK+M,oBACjBC,EAAahN,KAAKiN,kBACnBJ,EAAOnT,SAAS5W,GAAS,IACvB0E,GAAIqlB,EAAO9Q,QAAQjZ,QAClBoqB,WAAWF,EAAYF,GAAU,QACjCG,eAAiBnqB,OACjBiqB,oBAAsBvlB,OACtB0lB,WAAWpqB,EAAQ0E,GAAG,EAAMke,aAE5B8F,uDAKD0B,WAAWlN,KAAKiN,eAAejN,KAAK+M,qBAAoB,UA/IzBhH,ICIjBoH,0BACR9hB,EAAQ8C,8EACb9C,EAAQ8C,MACTpL,KAAO,YAEPqqB,WAAajf,EAAQif,YAAc,MAEpCC,IAAe,SAAU,UACzBC,EAAiBD,EAAY3T,SAASvL,EAAQmf,gBAC/Cnf,EAAQmf,eAAiB,kBACvBC,oBAAsBF,EAAYtR,QAAQuR,KAE1CzN,iEAGM1R,MACP5K,GAAIyc,KAAKsC,cACRkL,gBAA8C,IAA5Brf,EAAQqf,gBAAwB,EAAI,IAEzD9pB,SAASrC,IAAMosB,KACf/pB,SAAS3B,OAAS,IAClB8B,aAAe4pB,KACf5K,WA1Bc6K,GA0BYzV,GACzBrU,EAAeL,MAEdS,GAAIgc,KAAKxS,KACTmgB,EAAU3N,KAAKwN,gBZrCY,GYqC0B,OACpD7J,iBA/BW+J,IA+BShW,GAAgB1T,EAAEK,MAAOL,EAAE2Z,KACjDgQ,GAAuB7pB,EAAcP,4CAIpCoqB,GAAU3N,KAAKwN,gBZ3CY,GY2C0B,EACrDI,EAAY5N,KAAK8C,MAAM8K,UAAY5N,KAAK8C,MAAM8K,UAAY,QACzDnJ,UAtCWiJ,IAsCEE,EAAYD,GAC3B7pB,EAAckc,KAAKsC,mDAGX9U,0DAAKwS,KAAKxS,QAClBA,EAAKnJ,OAASmJ,EAAKmQ,KAAOnQ,EAAKnJ,MAAQmJ,EAAKmQ,SACxC,IAAImE,OAAM,kDAGbtU,EAAKnJ,UACHA,MAAQ,GAAI0S,QACZ1S,MAAMwpB,YAAargB,EAAKnJ,MAAMmT,cAAgB,IAEhDhK,EAAKmQ,QAAYA,IAAM,GAAI5G,SAC1B+W,WAAatgB,EAAKsgB,eAEpBtkB,SAAS+P,OAAO/O,KAAKgD,EAAKsgB,YAAY,IAAM,IAAQ,IAClDvmB,aACGiD,KAAKgD,EAAKsgB,YAAY3K,QAAQ,eAChCtM,GAAO,GAAIE,MAAKgX,EAAe3V,MAC5BjB,GAAYN,IAASrJ,EAAKsgB,WAAWC,OAExCD,WAAavmB,QAGZiG,qCAIH4Y,GAAIpG,KAAK8C,QAEXze,MAAQ8R,GAAM6J,KAAKxS,KAAKnJ,SACxBsZ,IAAMxH,GAAM6J,KAAKxS,KAAKmQ,OAEtBqQ,eAAiB7X,GAAMiQ,EAAE/hB,SACzBupB,UAAYlW,GAAgB0O,EAAE/hB,MAAO+hB,EAAEzI,OACvCZ,aAAeJ,GAChBpD,OAAO4B,OAAO6E,KAAKxS,KAAKsgB,YpBJc,KoBMrCG,cAAgBjO,KAAKkO,kEAInB9H,EAAIpG,KAAK8C,MACTqL,EAAUnO,KAAKwN,gBAAkB,EAAI,EAErChU,EAAmB4M,EAAE6H,cAAcxjB,IAAI,SAACmP,EAAQpS,UACnD,oBAEQoS,EAAO/J,eAvFA6d,aAAAA,cpByEiB,UoBkBvBpN,EAAKyB,aAAa7c,QAAU,aA3FtBwoB,GA4FFtH,EAAE6H,cACZxU,OAAO,SAACG,EAAQxW,SAAMA,GAAIoE,IAC1BiD,IAAI,kBAAUmP,GAAOwP,KAAK9kB,OAAS6pB,IACnC1lB,OAAO,SAACG,EAAGa,SAAMb,GAAIa,GAAG,IAG3B,iBACQ2c,GAAE6H,cAAczmB,IACtB6d,KAAK/E,WAIH6D,WAAa,GAAIW,KAAItL,EACxB/O,IAAI,SAACmB,EAAMpE,MACPojB,GAAYzR,mBAAgBvN,WACxBA,EAAK,GAAK,IAAMpE,EAAGojB,SAIzB1kB,GAAI,KACQid,QAAQ,SAACiL,EAAS5mB,OAC7B,EAAG,EAAG,GAAGkS,SAASlS,GAAI,IACrB6mB,GAAUpgB,EAAS,kBAAkB,EAAc/H,EAAGkoB,YpBzC3B,MoB4CzB,aACQ,UAGThK,SAASha,YAAYikB,MAzHZX,oCA+HVlgB,GACFA,WACKmR,MAAM,2BAGVnR,KAAOwS,KAAKgC,YAAYxU,QACxB+V,YACAM,oEAIAnN,UAAU0J,iBAAiB,YAAa,SAACsF,KACxCvB,WAAWhB,QAAQ,eACnBmL,GAAaC,EAAKjH,MAClBkH,EAAY9I,EAAE5iB,UACfwrB,EAAW5U,SAAS8U,GAAY,IAE9BpqB,GAAQoqB,EAAUrc,aAAa,cAC/Bsc,EAAYD,EAAUrc,aAAa,aAAaK,MAAM,KAEtDmG,EAAQL,GAAa9O,SAASilB,EAAU,IAAI,GAAG,GAE/CzD,EAAOhE,EAAKtQ,UAAUtV,wBAAyB6pB,EAAOuD,EAAUptB,wBAEhEmK,EAAQ/B,SAASkc,EAAE5iB,OAAOqP,aAAa,UACvC5F,EAAI0e,EAAKxpB,KAAOupB,EAAKvpB,KAAO8J,EAAM,EAClCrF,EAAI+kB,EAAK5pB,IAAM2pB,EAAK3pB,IACpB6S,EAAQ9P,EAAQ,IAAM4iB,EAAKoG,WAC3BhU,EAAO,OAAST,EAAQ,IAAM8V,EAAU,GAAK,KAAOA,EAAU,KAE7D7K,IAAIwH,UAAU7e,EAAGrG,GAAIkT,KAAMA,EAAMlF,MAAOA,EAAO4M,WAAY,SAC3D8C,IAAIyH,sEAOPzG,WAAWgC,YAAc,MAC1Bra,GAAI,EAEJrH,EAAS8a,KAAK+B,aAAa7c,QAAU,EAErCwpB,EAAWzgB,EAAS,iBAAkB1B,EA1K1BmhB,GA0KgC,iBAEpCA,MACN,MAGDiB,QACA/J,WAAWxa,YAAYskB,QAEvBlP,OAAO9Y,MAAM,EpB5GqB,GoB4GS+D,IAAI,SAACvB,EAAO1B,MACrDkiB,GAASpc,EAAW,sBAAuBf,EAAI,GAAkB/E,EApLxDkmB,GpByEiB,GoB4GPxoB,EAAQgE,KAC5B0b,WAAWxa,YAAYsf,QAIzBkF,GAAW3gB,EAAS,iBADR1B,EAAIsiB,GAA8CF,EAzLlDjB,GA0LwC,iBAE5CA,MACN,SAGD9I,WAAWxa,YAAYwkB,4CAaxB,GATAxI,GAAIpG,KAAK8C,SACoBsD,EAAE/hB,MAAMkT,WAAY6O,EAAE/hB,MAAMmT,eAAtDsX,OAAYC,UACU3I,EAAEzI,IAAIpG,WAAY6O,EAAEzI,IAAInG,eAE/CwX,OAAyBF,EAAa,EAA6B,SAAbC,GAExDd,KAEAgB,EAAe9Y,GAAMiQ,EAAE/hB,OACnBmD,EAAI,EAAGA,EAAIwnB,EAAYxnB,IAAK,IAC/BoQ,GAAUwO,EAAEzI,QACZtF,GAAe4W,EAAc7I,EAAEzI,KAAM,QACnBsR,EAAa1X,WAAY0X,EAAazX,iBACjDkB,gBAEGjR,KAAKuY,KAAKkP,gBAAgBD,EAAcrX,OAE9CA,EAAS,KACFA,QAGTqW,2CAGQtW,MAAWC,0DAAQ,MACbD,EAAUJ,WAAYI,EAAUH,eAAhDmB,OAAOC,OACRuW,EAAcrX,GAAeH,GAG7ByX,SACIzW,gBAHExC,GAAMyB,IAAYc,GAAmBC,EAAOC,GAOrC,OAIb,GAHAyW,GAAiB3X,GAAgByX,EAAavX,GAE9CwR,KAAWhgB,SACP5B,EAAI,EAAGA,EAAI6nB,EAAgB7nB,MAC5BwY,KAAKsP,OAAOH,EAAaxW,KAC1BlR,KAAK2B,QAEI,GAAI2N,MAAK3N,EAAI6O,GAAqB,GAAGuR,UAC9B,cAGuB7jB,KAA1CyD,EAAI6O,GAAqB,GAAGwR,eACtB0F,EAAa,KAChB1nB,KAAKuY,KAAKsP,OAAOH,EAAaxW,GAAO,OAG9ByQ,KAAOA,EAEbgG,iCAGDzX,EAAWgB,OAOb,GAPoB4W,2DACpBnJ,EAAIpG,KAAK8C,MAGT0M,EAAcrZ,GAAMwB,GACpBvO,KAEI5B,EAAI,EAAGA,EAAIyQ,GAAoBzQ,IAAKwR,GAAQwW,EAAa,GAAI,IAChE5V,MAGA6V,EAAwBD,GAAepJ,EAAE/hB,OAASmrB,GAAepJ,EAAEzI,GAEpE4R,IAASC,EAAYjY,aAAeoB,IAAU8W,IACzCjG,SAAWrS,GAAYqY,KAErBxP,KAAK0P,mBAAmBF,KAE9B/nB,KAAKmS,SAGHxQ,8CAGWyN,MACd2S,GAAWrS,GAAYN,GACvB4S,EAAYzJ,KAAKxS,KAAKsgB,WAAWtE,mBAE1BA,YACCC,GAAa,OAClBzJ,KAAKR,OAAOvC,GAAiBwM,EAAWzJ,KAAK8C,MAAM/F,uBAtRvB6E,ICFhBnD,0BACRpT,EAAQO,8EACbP,EAAQO,aAET+e,WAAa/e,EAAK+e,iBAClBgF,YAAc/jB,EAAK+jB,kBAEnB5sB,KAAO6I,EAAK7I,MAAQ,SACpBghB,KAAO,IAEPlE,mEAIFG,KAAKxS,KAAK6P,SAAS/Y,QAAU,SAC1BsV,OAAO+I,WAAa,OACpBL,SAAS5e,SAAS3B,OAAS,sCAIxBoM,4FACOA,KAERyhB,YAAczhB,EAAQyhB,kBACtB3J,eAAiB9X,EAAQ8X,wBAE5BrM,OAAOiW,UAAY1hB,EAAQyhB,YAAYC,WAAa,YACpDjW,OAAOkW,UAAY3hB,EAAQyhB,YAAYE,WAAa,YACpDlW,OAAOmW,UAAY5hB,EAAQyhB,YAAYG,WAAa,OACpDnW,OAAOoW,oBAAsB7hB,EAAQyhB,YAAYI,qBAAuB,OAExEpW,OAAOqW,eAAiB9hB,EAAQ8X,eAAegK,oBAC/CrW,OAAOoM,eAAiB7X,EAAQ8X,eAAeD,oBAE/CpM,OAAO4Q,iBAAmBrc,EAAQqc,6DAIhCtN,2DADS8C,KAAKxS,KACCwS,KAAKjd,uDAIpB6a,2DADcoC,KAAKxS,wCAItBsW,gEACCoM,iBACDpM,QACEqM,oBAAoBnQ,KAAKoQ,gBAA+B,SAAdpQ,KAAKjd,WAEhDstB,8DAIDjK,GAAIpG,KAAK8C,MACT3F,EAAS6C,KAAKxS,KAAK2P,SACrBC,cAAgBD,EAAO7Y,SAEvBgsB,UAAYtQ,KAAKzU,MAAO6a,EAAEhJ,gBAE1BmT,QAAUnK,EAAEkK,UAAU,IAMtBE,cACOrT,YACGA,EAAO1S,IAAI,SAACzG,EAAGwD,SACzBzD,GAASqiB,EAAEmK,QAAU/oB,EAAI4e,EAAEkK,0DAKVG,MACb7U,GAAOV,GAAmBuV,yDADa,SAEvCrU,EAAkB4D,KAAK/Z,OAASgW,GAAcL,GAC9C8U,EAAiB5U,GAAgBF,GAAQQ,EACzCpW,EAAWga,KAAK/Z,OAAU0V,GAAaC,GAAQ8U,OAEhD5N,MAAM3G,cACFP,YACGA,EAAKnR,IAAI,kBAAKzE,GAAWhC,EAAIoY,oBACvBA,WACPpW,QAIN2qB,yBACAC,qBACAC,8DAIDzK,GAAIpG,KAAK8C,MACTgO,EAAW,kBAAU3V,GAAO1Q,IAAI,kBAAOyR,IAAM/R,EAAKic,EAAEjK,YAEtDkB,SAAW2C,KAAKxS,KAAK6P,SAAS5S,IAAI,SAACzG,EAAGwD,MACnC2T,GAASnX,EAAEmX,OACX4V,EAAe/sB,EAAE+sB,6BAEd/sB,EAAEoV,MAAQpV,EAAEoV,KAAK4X,QAAQ,SAAU,SAACC,SAAiB,KAARA,EAAc,QAAkB,KAARA,EAAc,OAAS,eAC3FzpB,YACIxD,EAAEwZ,iBAELrC,aACI2V,EAAS3V,gBAEP4V,iBACED,EAASC,iDAMvB3K,GAAIpG,KAAK8C,SACV9C,KAAK2K,WAAWuG,sBAChBC,UAAY/K,EAAE/I,SAAS+I,EAAE/I,SAAS/Y,OAAS,GAAG8sB,kBAG/CD,UAAY,GAAI3sB,OAAM4hB,EAAEhJ,eAAezY,KAAK,QAC5C0Y,SAAS5S,IAAI,cACZof,WAAWpf,IAAI,SAAC0E,EAAK/L,GACnB+L,EAAMiX,EAAE+K,UAAU/tB,OAClB+tB,UAAU/tB,GAAK+L,iDAOhBiX,GAAIpG,KAAK8C,KACV9C,MAAKxS,KAAKuQ,gBACP+E,MAAM/E,SAAWiC,KAAKxS,KAAKuQ,SAAStT,IAAI,qBAC1Cyd,SAAWhM,GAAMlY,EAAEkQ,MAAOkS,EAAEjK,OAC1BnY,EAAEmK,UAASnK,EAAEmK,YAIVnK,KAGNgc,KAAKxS,KAAKkQ,gBACPoF,MAAMpF,SAAWsC,KAAKxS,KAAKkQ,SAASjT,IAAI,qBAC1Cie,SAAWxM,GAAMlY,EAAEK,MAAO+hB,EAAEjK,SAC5BwM,OAASzM,GAAMlY,EAAE2Z,IAAKyI,EAAEjK,OACtBnY,EAAEmK,UAASnK,EAAEmK,YACVnK,0DAMLyJ,EAAM,YAEPuS,KAAK2K,WAAWuG,QAAS,GACrB,kBACFG,GAAa,GAAI7sB,OAAMwb,KAAK8C,MAAM1F,eAAezY,KAAK,QACrD6I,KAAK6P,SAAS5S,IAAI,SAACzG,EAAGwD,MACtB2T,GAASmF,EAAK9S,KAAK6P,SAAS7V,GAAG2T,SACjC1N,GAAO4jB,EAAaA,EAAW5mB,IAAI,SAACyW,EAAG1Z,SAAM0Z,GAAI/F,EAAO3T,UAIxD8pB,GAAgBtR,KAAKxS,KAAK6P,SAAS5S,IAAI,kBAAKzG,GAAEyJ,WAC/CuS,MAAKxS,KAAKuQ,YACEtW,KAAKuY,KAAKxS,KAAKuQ,SAAStT,IAAI,kBAAKzG,GAAEkQ,SAE/C8L,KAAKxS,KAAKkQ,eACPlQ,KAAKkQ,SAASjT,IAAI,cACRhD,MAAMzD,EAAE2Z,IAAK3Z,EAAEK,iBAIrBO,kBAAU0sB,yDAIhB9X,IAEF,cAEOwG,KAAKpG,OAAOkW,gBACX9P,KAAKzU,qBACIyU,KAAKpG,OAAOoW,qBAG7B,iBACQhQ,MAAK8C,MAAM3G,OACjBkJ,KAAKrF,QAIP,cAEOA,KAAKpG,OAAOiW,iBACV7P,KAAK/Z,QAGd,cACKmgB,GAAIpG,KAAK8C,eACX0N,MAAMjI,WAAavK,GAAmBgC,KAAKzU,MAC5C6a,EAAEoK,MAAMrT,OAAQ6C,KAAKpG,OAAOmW,WAEtB3J,EAAEoK,OACRnL,KAAKrF,QAIP,kBAEQA,KAAKzU,UACP,SAEN,iBACQyU,MAAK8C,MAAMpF,UACjB2H,KAAKrF,QAILuR,EAAcvR,KAAK8C,MAAMzF,SAAS5D,OAAO,kBAAqB,QAAhBzV,EAAEwZ,YAChDgU,EAAexR,KAAK8C,MAAMzF,SAAS5D,OAAO,kBAAqB,SAAhBzV,EAAEwZ,YAEjDiU,EAAcF,EAAY9mB,IAAI,eAC7BoF,GAAQ7L,EAAE6L,aAEb,YAAmB7L,EAAE6L,aAEbA,QACAmX,EAAKxH,OAAO3P,WACVmX,EAAK2D,WAAWuG,yBAGPlK,EAAKpN,OAAO4Q,2BrB9KG,EqB+KtBxD,EAAK/gB,QAEjB,cACKmgB,GAAIpG,KAAK8C,MACT9e,EAAIoiB,EAAE/I,SAASxN,GACfqhB,EAAUlR,KAAK2K,WAAWuG,QAE1BQ,EAAa1R,KAAK2K,WAAW+G,YrBvLD,GqBwL5B3H,EAAY3D,EAAEkK,WAAa,EAAIoB,GAC/B5K,EAAWiD,GAAWmH,EAAU,EAAIK,EAAYjtB,QAEhDujB,EAAazB,EAAEoK,MAAMvI,UAAUxd,IAAI,kBAAK8B,GAAIwd,EAAU,GACtDmH,OACUrJ,EAAWpd,IAAI,kBAAK3D,GAAIggB,EAAWjX,QAG7CsN,GAAS,GAAI3Y,OAAM4hB,EAAEhJ,eAAezY,KAAK,GAC1Cqb,MAAKpG,OAAO4Q,qBACX0G,GAAWltB,EAAE6L,QAAUuW,EAAE/I,SAAS/Y,OAAS,EACpCN,EAAE+sB,aAEF/sB,EAAEmX,WAIT2O,GAAU,GAAItlB,OAAM4hB,EAAEhJ,eAAezY,KAAK,SAC3CusB,OACQltB,EAAE6lB,WAAWpf,IAAI,SAACvE,EAAG9C,SAAM8C,GAAIlC,EAAEotB,eAAehuB,kBAI9CykB,aACA7jB,EAAE6lB,mBACLC,SAED3M,WAEEiJ,EAAEjK,MAAMnW,mBACP+jB,WACDjD,IAEVzB,KAAK2B,MAIL2K,EAAcH,EAAa/mB,IAAI,eAC9BoF,GAAQ7L,EAAE6L,aAEb,aAAoB7L,EAAE6L,aAEdA,QACAmX,EAAKxH,OAAO3P,WACVmX,EAAKvW,iBACJuW,EAAK2I,YAAYpf,oBACfyW,EAAK2I,YAAYhf,kBACrBqW,EAAK2I,YAAYtf,gBACf2W,EAAK2I,YAAYpF,kBACjBvD,EAAK2I,YAAYrF,0BAGTtD,EAAKpN,OAAO4Q,kBAE/B,cACKpE,GAAIpG,KAAK8C,MACT9e,EAAIoiB,EAAE/I,SAASxN,GACf+hB,EAAUxL,EAAEjK,MAAM8L,UAAU,GAAK7B,EAAEjK,MAAMnW,SAC1CogB,EAAEjK,MAAM8L,UAAU,GAAK7B,EAAEjK,MAAMnW,2BAGrBogB,EAAEoK,MAAMvI,qBACRjkB,EAAE6lB,kBAEN7lB,EAAEmX,gBAEAyW,SACF5R,KAAK2P,YAAYkC,SrBxPI,IqB0P7BxM,KAAK2B,MAIL8K,IAEF,kBAEQ9R,KAAKzU,UACP,SAEN,iBACQyU,MAAK8C,MAAM/E,UACjBsH,KAAKrF,UAIUxG,EAAiB5U,OAAO6sB,EAAaE,EAAaG,MAEjEC,IAAa,WAAY,iBACxBC,2BAEA7N,WAAa,GAAIW,KAAItL,EACxBC,OAAO,mBAASsY,EAAUrY,SAAS9N,EAAK,KAAOob,EAAKlE,MAAMlX,EAAK,MAC/DnB,IAAI,eACAmgB,GAAYzR,mBAAgBvN,WAC7BA,EAAK,GAAG8N,SAAS,cAAgB9N,EAAK,GAAG8N,SAAS,gBAC/CsY,mBAAmBvqB,KAAKmjB,IAEtBhf,EAAK,GAAIgf,gEAKdqH,kBAED7L,GAAIpG,KAAK8C,MACToP,EAAUlS,KAAKpG,OAAOqW,eACtBkC,EAAUnS,KAAKpG,OAAOoM,cACbI,GAAEoK,MAAMrT,OAEd1S,IAAI,SAAC7D,EAAOiJ,MACdsL,GAASwK,EAAK7C,MAAMzF,SAAS5S,IAAI,SAAC4V,EAAK7Y,MACtC0M,GAAQmM,EAAIlF,OAAOtL,gBAEfwQ,EAAIjH,WACJlF,OACDmM,EAAIwJ,WAAWha,SACd8V,EAAKnG,OAAOhY,aACR2qB,EAAUA,EAAQje,GAASA,OAInC+d,YAAYpiB,UACTjJ,iBACSsrB,EAAUA,EAAQtrB,GAASA,OACrCwf,EAAEoK,MAAMvI,UAAUpY,UAChBsL,WACEiL,EAAE+K,UAAUthB,4DAOnB6G,UAAU0J,iBAAiB,YAAa,SAACsF,MACzCniB,GAAIilB,EAAKlG,SACTha,EAAIrH,EAAUunB,EAAK9R,WACnB0b,EAAO1M,EAAE+G,MAAQnkB,EAAE7G,KAAOkC,EAAcJ,GACxC8uB,EAAO3M,EAAEgH,MAAQpkB,EAAEjH,GAEpBgxB,GAAO7J,EAAKviB,OAAS3C,EAAaC,IACjC8uB,EAAQ/uB,EAAaC,KACnB+uB,oBAAoBF,KAEpBxO,IAAI3D,wDAKQmS,MACfhM,GAAIpG,KAAK8C,SACTsD,EAAE+K,cAEFthB,GAAQwM,GAAkB+V,EAAMhM,EAAEoK,MAAMvI,WAAW,MACnDpY,GAAS,EAAG,IACX0iB,GAAMvS,KAAKiS,YAAYpiB,QAEtB+T,IAAIwH,UACRmH,EAAI1H,KAAO7K,KAAK4D,IAAI1Y,OAAOqB,EAC3BgmB,EAAIC,SAAWxS,KAAK4D,IAAI1Y,OAAOhF,GAC9BkT,KAAMmZ,EAAIE,eAAgBve,MAAO,IAClCqe,EAAIpX,OACJtL,QAGI+T,IAAIyH,8DAKNjF,EAAIpG,KAAKxS,IACV4Y,GAAE/I,SAAS/Y,OAAS,SACjBsgB,WAAWgC,YAAc,KAC5BvJ,SAAS5S,IAAI,SAACzG,EAAGwD,MAIdrG,GAAOuM,ErB3WqB,IqB6WpBlG,EACX,IrB9W+B,IqBgX/BohB,EAAKpJ,OAAOhY,GACZxD,EAAEoV,KACFwP,EAAKhP,OAAOyI,mBACRuC,WAAWxa,YAAYjJ,0DAS3B6e,KAAK+D,sBACFA,KAAO,EAGV/D,MAAK0S,oBACFA,cAAcvP,QAAQ,eACtB7a,GAAIoB,EAAE4X,UACRhX,WAAW6K,YAAY7M,UAItBoqB,cAAgB1S,KAAKgS,mBAAmBvnB,IAAI,wBAEzCyW,EAAEyI,qBACChkB,SACFub,EAAE0I,aAIoBjkB,KAA5Bqa,KAAK8C,MAAM6P,oBACR7P,MAAM6P,aAAe3S,KAAK8C,MAAM1F,cAAgB,QAIjDsV,cAAcjoB,IAAI,eAClBmoB,GAAc5uB,EAAE4lB,MAAMiJ,EAAK/P,MAAM6P,gBAEnCrR,QAAUF,GAAYpd,EAAEjB,MAAM6vB,KAC3BxO,SAASha,YAAYpG,EAAEsd,yDAK1BtB,KAAK0S,oBACFA,cAAcvP,QAAQ,eACtB7a,GAAIoB,EAAE4X,UACRhX,WAAW6K,YAAY7M,2DAMtB+C,OAAO+U,iBAAiB,cAAe,aACtCmB,sEAKDyQ,mBAAmBvnB,IAAI,cACzBmf,MAAMnf,IAAI,cACN2V,iBAAiB,QAAS,cAC1BvQ,GAAQiB,EAAKqB,aAAa,sBACzB2gB,oBAAoBjjB,cAMvB+T,IAAIlN,UAAU0J,iBAAiB,QAAS,cACxCvQ,GAAQkjB,EAAKnP,IAAIlN,UAAUvE,aAAa,sBACvC2gB,oBAAoBjjB,6DAKrB6iB,cAAcjoB,IAAI,eAClBmoB,GAAc5uB,EAAE4lB,MAAMoJ,EAAKlQ,MAAM6P,iBACvB3uB,EAAEjB,MAAM6vB,EAAa5uB,EAAEsd,sDAKjCwR,oBAAoB9S,KAAK8C,MAAM6P,aAAe,+CAI9CG,oBAAoB9S,KAAK8C,MAAM6P,aAAe,6CAGvC9iB,0DAAMmQ,KAAK8C,MAAM6P,aACzBvM,EAAIpG,KAAK8C,mBAELjT,QACAuW,EAAEoK,MAAMrT,OAAOtN,UACduW,EAAE/I,SAAS5S,IAAI,kBAAKzG,GAAEmX,OAAOtL,kDAKnBA,MACfuW,GAAIpG,KAAK8C,SACLtZ,SAASqG,IACN,IAAGA,EAAQ,GACnBA,GAASuW,EAAEoK,MAAMrT,OAAO7Y,SAAQuL,EAAQuW,EAAEoK,MAAMrT,OAAO7Y,OAAS,GAChEuL,IAAUuW,EAAEuM,iBACbA,aAAe9iB,IACZmQ,KAAK3U,OAAQ,cAAe2U,KAAKiT,sDAM1BrsB,EAAOssB,MAAerjB,0DAAMmQ,KAAK8C,MAAM1F,0GAChCxW,EAAOssB,EAAerjB,QACpCrC,KAAK2P,OAAOgW,OAAOtjB,EAAO,EAAGjJ,QAC7B4G,KAAK6P,SAAS5S,IAAI,SAACzG,EAAGwD,KACxB2T,OAAOgY,OAAOtjB,EAAO,EAAGqjB,EAAc1rB,WAEpC8c,OAAOtE,KAAKxS,mDAGFqC,0DAAQmQ,KAAK8C,MAAM1F,cAAc,CAC5C4C,MAAKxS,KAAK2P,OAAO7Y,QAAU,mGAGTuL,QACjBrC,KAAK2P,OAAOgW,OAAOtjB,EAAO,QAC1BrC,KAAK6P,SAAS5S,IAAI,cACpB0Q,OAAOgY,OAAOtjB,EAAO,UAEnByU,OAAOtE,KAAKxS,6CAGJ0lB,MAAerjB,0DAAM,OAC7BrC,KAAK6P,SAASxN,GAAOsL,OAAS+X,OAC9B5O,OAAOtE,KAAKxS,6CAKH6P,QACT7P,KAAK6P,SAAS5S,IAAI,SAACzG,EAAGwD,GACvB6V,EAAS7V,OACT2T,OAASkC,EAAS7V,WAGjB8c,OAAOtE,KAAKxS,aA5jBoBoU,ICFlBwR,0BACR/nB,EAAQO,8EACbP,EAAQO,aACT7I,KAAO,UACPggB,YAAc,IACdgB,KAAO,IAEPlE,+DAGIjU,4FACOA,QACX2f,UAAYvL,KAAKuL,UAAUlG,KAAKrF,WAChCwL,WAAaxL,KAAKwL,WAAWnG,KAAKrF,WAElCyL,WAAa7f,EAAK6f,YAAc,QAChC7R,OAAO8R,WAAa9f,EAAK8f,YAAc,OAEvCtf,UAAYR,EAAKQ,YAAa,OAC9Bsb,YAAc9b,EAAK8b,aAAe,qIAKnCtB,GAAIpG,KAAK8C,WACR5d,OACJ8a,KAAK/Z,OAAS+Z,KAAKzU,MAChByU,KAAK7T,OAAOI,EAAIyT,KAAK0H,YAAc,EACnC1H,KAAK7T,OAAOjG,EAAI8Z,KAAK0H,YAAc,KAE/BxiB,GAAsB8a,KAAtB9a,OAAQkH,EAAc4T,KAAd5T,UAEVuf,EAAuBvF,EAAEwF,uBAC7BnE,kBACAmE,uBACEC,GAAW,IAAM7L,KAAKpG,OAAO8R,aAE/BrF,YAAY5b,IAAI,SAAC8b,EAAO/e,MACnBkkB,GAAaG,EACbC,EAAmBvF,EAAQH,EAAEO,WtB0DZ,IsBzDjBta,EAAWyf,EAAkB,IAAM,EAAG,EACtCC,EAAY3f,GAAa0f,EAAkBA,EAC3CE,EAAWH,GAAsBE,EACjC9f,EAAgBjH,EAAmB0mB,EAAYxmB,GAC/CgH,EAAclH,EAAmBgnB,EAAU9mB,GAE3C+mB,EAAe3L,EAAKyD,MAAQ4H,EAAqBnkB,GAEnD0kB,SAASC,QACV7L,GAAKyD,QACIkI,EAAeA,EAAahgB,cAAgBA,IAC9CggB,EAAeA,EAAa/f,YAAcD,MAExCA,IACFC,MAEJkgB,GACe,MAApBN,EACGhf,EAAoBof,EAAUC,EAAQ7L,EAAKnU,OAAQmU,EAAKpb,OAAQob,EAAKlU,UAAWC,GAChFQ,EAAqBqf,EAAUC,EAAQ7L,EAAKnU,OAAQmU,EAAKpb,OAAQob,EAAKlU,UAAWC,KAEnFob,aAAahgB,KAAK2kB,KAClBR,iBAAiBnkB,0CAGX8e,QACAH,EAAEO,yCAGFoF,WAIJhI,KAAO,+CAIRqC,GAAIpG,KAAK8C,MAETtJ,IAEF,iBAEA,+BAEgB4M,EAAEqB,oBACRzH,KAAKR,mBACAQ,KAAK0H,cAElBrC,KAAKrF,aAIJmE,WAAa,GAAIW,KAAItL,EACxB/O,IAAI,eACAmgB,GAAYzR,mBAAgBvN,WACxBA,EAAK,GAAIgf,kDAIAyB,MACZnnB,GAAuB8a,KAAvB9a,OAAQumB,EAAezL,KAAfyL,WACTvD,EAAWljB,EAAmBqnB,EAASX,WAAYW,EAASpnB,MAAQ,EAAGC,wBACtDgjB,EAAS3b,EAAKkf,QAAiBvD,EAAShiB,EAAKulB,6CAG1Dnb,EAAK9I,EAAE8kB,EAAK5G,MAClBpV,MACEpH,GAAQ8W,KAAKR,OAAOhY,MACvB8kB,EAAM,IACEhc,EAAM0P,KAAKuM,oBAAoBvM,KAAK8C,MAAM8I,iBAAiBpkB,OAChEkD,MAAMgE,OAASzF,EAAmBC,EAAO,OAC1CsjB,GAAQvrB,EAAU+e,KAAK9J,KACvB3J,EAAImZ,EAAE+G,MAAQD,EAAM/qB,KAAO,GAC3ByE,EAAIwf,EAAEgH,MAAQF,EAAMnrB,IAAM,GAC1B6e,GAASF,KAAK2M,kBAAoB3M,KAAK2M,iBAAiBroB,OAAS,EAClE0b,KAAK2M,iBAAiBnlB,GAAKwY,KAAK8C,MAAM3F,OAAO3V,IAAM,KAClDolB,GAAuC,IAA5B5M,KAAK8C,MAAMuD,YAAY7e,GAAWwY,KAAK8C,MAAM6D,YAAY1iB,QAAQ,QAC3E2f,IAAIwH,UAAU7e,EAAGrG,GAAIkT,KAAM8G,EAAOhM,MAAO0Y,EAAU,WACnDhJ,IAAIyH,kBAEC/a,EAAK,2BACVsT,IAAI3D,YACJvV,MAAMgE,OAASxF,8CAKhBwN,UAAU0J,iBAAiB,YAAaJ,KAAKuL,gBAC7C7U,UAAU0J,iBAAiB,aAAcJ,KAAKwL,8CAG1C9F,MACH5iB,GAAS4iB,EAAE5iB,OACb+pB,EAAS7M,KAAKmE,WAAW4G,IAAI,eAAezD,MAC5CwF,EAAY9M,KAAK+M,oBACjBC,EAAahN,KAAKiN,kBACnBJ,EAAOnT,SAAS5W,GAAS,IACvB0E,GAAIqlB,EAAO9Q,QAAQjZ,QAClBoqB,WAAWF,EAAYF,GAAU,QACjCG,eAAiBnqB,OACjBiqB,oBAAsBvlB,OACtB0lB,WAAWpqB,EAAQ0E,GAAG,EAAMke,aAE5B8F,uDAKD0B,WAAWlN,KAAKiN,eAAejN,KAAK+M,qBAAoB,UArJvBhH,IVAlCrH,QACAD,QACCA,cAEMiM,WACHyC,OACJ7B,SACE8H,IAiBFC,GACL,WAAYhoB,EAAQ8C,qBACZqQ,GAAerQ,EAAQpL,KAAMsI,EAAQ8C,wFWjC1CmlB,YAEJA,IAAOC,KAAU,gBACjBD,GAAOE,QAAU,QAEjBF,GAAiB/Z,OAAOM,UAAYyZ,GAAQG"}