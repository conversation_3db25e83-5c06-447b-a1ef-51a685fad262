@charset "UTF-8";
body {
  font-family: "Open Sans", sans-serif;
  color: #535353;
  font-weight: 300;
  font-size: 0.9375rem;
  line-height: 1.9;
  -webkit-transition: margin-left .5s;
  transition: margin-left .5s;
}

::-moz-selection {
  background: #31b8f5;
  color: #fff;
}

::selection {
  background: #31b8f5;
  color: #fff;
}

::-moz-selection {
  background: #31b8f5;
  color: #fff;
}

button, input, optgroup, select, textarea {
  font-family: "Open Sans", sans-serif;
  font-weight: 300;
}

a {
  color: #0facf3;
  -webkit-transition: .2s linear;
  transition: .2s linear;
}

a:hover, a:focus {
  color: #0facf3;
  text-decoration: none;
  outline: none;
}

.link-color-default a {
  color: #535353;
}

.link-color-default a:hover, .link-color-default a:focus {
  color: #0facf3;
}

b,
strong {
  font-weight: 600;
}

del {
  opacity: .6;
}

.semibold {
  font-weight: 400;
}

.lead, .pricing-4 .plan-price p {
  font-size: 1.0625rem;
}

small,
.small {
  display: inline-block;
  color: #b5b9bf;
}

.display-1,
.display-2,
.display-3,
.display-4 {
  font-weight: 600;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: "Dosis", "Open Sans", sans-serif;
  font-weight: 600;
  color: #37404d;
  letter-spacing: 1px;
  line-height: 1.6;
}

h1 small, h2 small, h3 small, h4 small, h5 small, h6 small,
.h1 small, .h2 small, .h3 small, .h4 small, .h5 small, .h6 small {
  font-size: 65%;
}

h1 strong,
h1 b, h2 strong,
h2 b, h3 strong,
h3 b, h4 strong,
h4 b, h5 strong,
h5 b, h6 strong,
h6 b,
.h1 strong,
.h1 b, .h2 strong,
.h2 b, .h3 strong,
.h3 b, .h4 strong,
.h4 b, .h5 strong,
.h5 b, .h6 strong,
.h6 b {
  font-weight: 700;
}

h1 a, h2 a, h3 a, h4 a, h5 a, h6 a,
.h1 a, .h2 a, .h3 a, .h4 a, .h5 a, .h6 a {
  color: #37404d;
}

h1 a:hover, h2 a:hover, h3 a:hover, h4 a:hover, h5 a:hover, h6 a:hover,
.h1 a:hover, .h2 a:hover, .h3 a:hover, .h4 a:hover, .h5 a:hover, .h6 a:hover {
  color: #0facf3;
}

h6, .h6 {
  font-family: "Open Sans", sans-serif;
  letter-spacing: 0;
  font-weight: 400;
  font-size: 0.9375rem;
}

.heading-alt {
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  letter-spacing: 0;
}

.blockquote {
  margin: 2rem 4rem;
  border: none;
  text-align: center;
  font-size: 1.25rem;
  line-height: 1.875rem;
  color: #535353;
}

.blockquote .quote-sign::before {
  content: '“';
  display: block;
  font-size: 9rem;
  font-family: Georgia, Verdana, Roboto, Serif;
  line-height: .2;
  font-weight: 600;
  padding-top: 4rem;
  opacity: .1;
}

.blockquote p::before,
.blockquote p::after {
  font-size: 1.5rem;
  opacity: .5;
}

.blockquote p::before {
  content: '“';
  margin-left: -13px;
  padding-right: 4px;
}

.blockquote p::after {
  content: '”';
  margin-right: -13px;
  padding-left: 4px;
}

.blockquote footer {
  color: #535353;
  font-size: 0.875rem;
  margin-top: 2rem;
}

.blockquote footer::before {
  content: '\2014 \00A0';
}

.blockquote .lead, .blockquote .pricing-4 .plan-price p, .pricing-4 .plan-price .blockquote p {
  font-size: 1.75rem;
  line-height: 2.5rem;
  font-weight: 200;
}

.blockquote .small {
  font-size: 1rem;
  line-height: 1.5rem;
  color: #535353;
}

@media (max-width: 991px) {
  .blockquote {
    margin: 2rem 1rem;
  }
}

.text-quoted::before, .text-quoted::after {
  opacity: .7;
}

.text-quoted::before {
  content: '“';
  padding-right: 2px;
}

.text-quoted::after {
  content: '”';
  padding-left: 2px;
}

hr {
  border-top-color: rgba(83, 83, 83, 0.07);
  margin: 2rem auto;
}

.mark-border {
  padding-bottom: 2px;
  border-bottom: 1px dashed white;
}

.divider {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-flex: 0;
          flex: 0 1 0%;
  color: #b5b9bf;
  font-size: 0.75rem;
  letter-spacing: .5px;
  margin: 2rem auto;
  width: 100%;
}

.divider::before, .divider::after {
  content: '';
  -webkit-box-flex: 1;
          flex-grow: 1;
  border-top: 1px solid #ebebeb;
}

.divider::before {
  margin-right: 1rem;
}

.divider::after {
  margin-left: 1rem;
}

pre {
  background-color: #fafafa;
  padding: 12px;
  border: 1px solid #f1f2f3;
  border-left: 3px solid #0facf3;
  color: #37404d;
  -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, 0.02);
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.02);
}

.pre-scrollable {
  max-height: 350px;
  overflow-y: auto;
}

.typed-cursor {
  font-family: "Open Sans", sans-serif;
  font-weight: 300;
  vertical-align: text-bottom;
  opacity: 1;
  -webkit-animation: blink 0.7s infinite;
          animation: blink 0.7s infinite;
}

[data-type].text-primary + .typed-cursor {
  color: #0facf3;
}

[data-type].text-secondary + .typed-cursor {
  color: #e4e7ea;
}

[data-type].text-success + .typed-cursor {
  color: #46da60;
}

[data-type].text-info + .typed-cursor {
  color: #0e97ff;
}

[data-type].text-warning + .typed-cursor {
  color: #ffbe00;
}

[data-type].text-danger + .typed-cursor {
  color: #ff4954;
}

[data-type].text-dark + .typed-cursor {
  color: #000000;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-webkit-keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@media (max-width: 1199px) {
  body {
    font-size: 0.875rem;
  }
  .lead, .pricing-4 .plan-price p {
    font-size: 1rem;
  }
  .h1, h1 {
    font-size: 2rem;
  }
  .h2, h2 {
    font-size: 1.75rem;
  }
  .h3, h3 {
    font-size: 1.5rem;
  }
  .h4, h4 {
    font-size: 1.25rem;
  }
  .h5, h5 {
    font-size: 1.125rem;
  }
  .h6, h6 {
    font-size: 1rem;
  }
  .display-1 {
    font-size: 4rem;
  }
  .display-2 {
    font-size: 3.5rem;
  }
  .display-3 {
    font-size: 3rem;
  }
  .display-4 {
    font-size: 2.25rem;
  }
  .blockquote p {
    font-size: 1rem;
    line-height: 1.675rem;
  }
  .blockquote footer {
    font-size: 0.75rem;
  }
}

@media (max-width: 767px) {
  body {
    font-size: 0.8125rem;
  }
  .lead, .pricing-4 .plan-price p {
    font-size: 0.9375rem;
  }
  .h1, h1 {
    font-size: 1.75rem;
  }
  .h2, h2 {
    font-size: 1.5rem;
  }
  .h3, h3 {
    font-size: 1.25rem;
  }
  .h4, h4 {
    font-size: 1.125rem;
  }
  .h5, h5 {
    font-size: 1rem;
  }
  .h6, h6 {
    font-size: 0.9375rem;
  }
  .display-1 {
    font-size: 2.5rem;
  }
  .display-2 {
    font-size: 2.25rem;
  }
  .display-3 {
    font-size: 2rem;
  }
  .display-4 {
    font-size: 1.825rem;
  }
}

img {
  max-width: 100%;
}

.img-thumbnail {
  padding: 0.25rem;
  border-color: #f3f3f3;
  border-radius: 3px;
}

.img-shadow {
  -webkit-box-shadow: 0 0 25px 5px rgba(0, 0, 0, 0.2);
          box-shadow: 0 0 25px 5px rgba(0, 0, 0, 0.2);
}

.img-outside-right {
  overflow: hidden;
}

.img-outside-right img {
  width: 400px;
  -webkit-transform: translateX(15%);
          transform: translateX(15%);
}

.avatar {
  border-radius: 10rem;
  width: 48px;
  height: 48px;
}

.avatar-xs {
  width: 34px;
  height: 34px;
}

.avatar-sm {
  width: 40px;
  height: 40px;
}

.avatar-lg {
  width: 56px;
  height: 56px;
}

.avatar-xl {
  width: 64px;
  height: 64px;
}

.img-fadein {
  opacity: .75;
  -webkit-transition: .5s;
  transition: .5s;
}

.img-fadein:hover {
  opacity: 1;
}

.table th {
  border-top: 0;
  font-weight: 400;
}

.table tbody th {
  border-top: 1px solid #eceeef;
}

.table thead th {
  border-bottom: 1px solid #ebebeb;
}

.thead-default th {
  background-color: #fcfdfe;
}

.table-hover tbody tr {
  -webkit-transition: background-color 0.2s linear;
  transition: background-color 0.2s linear;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: #fcfdfe;
}

.table-hover tbody tr:hover {
  background-color: #f9fafb;
}

.table-sm th,
.table-sm td {
  padding: .5rem;
}

.table-lg th,
.table-lg td {
  padding: 1rem;
}

.media-list .media {
  border-bottom: 1px solid #f1f2f3;
}

.media-list .media:last-child {
  border-bottom: none;
}

.media {
  padding: 24px 0;
  -webkit-transition: background-color .2s linear;
  transition: background-color .2s linear;
}

.media > * {
  margin: 0 8px;
}

.media.flex-column > * {
  margin: 0;
}

.media.active {
  background-color: #f9fafb;
}

.media.bordered {
  border: 1px solid #ebebeb;
}

.media.items-center {
  -webkit-box-align: center;
          align-items: center;
}

.media .media {
  margin-top: 1.25rem;
}

.media .lead, .media .pricing-4 .plan-price p, .pricing-4 .plan-price .media p {
  line-height: 1.875rem;
}

.media .title {
  -webkit-box-flex: 1;
          flex: 1 1 0%;
}

.media .avatar {
  flex-shrink: 0;
}

.media .align-center {
  -ms-grid-row-align: center;
      align-self: center;
}

.media .nav-link {
  line-height: 24px;
  font-size: 90%;
}

.media-body {
  min-width: 0;
}

.media-body > * {
  margin-bottom: 0;
}

.media-body .media > *:first-child {
  margin-left: 0;
}

.media-body .media > *:last-child {
  margin-right: 0;
}

.media-left {
  padding-right: 0;
}

.media-thumb {
  color: #535353;
  display: -webkit-box;
  display: flex;
  -webkit-box-align: start;
          align-items: flex-start;
  margin: 20px 0;
}

.media-thumb img {
  width: 64px;
  margin-right: 12px;
}

.media-thumb .media-body {
  font-size: .85em;
  font-weight: 400;
  line-height: 1.8;
  margin-bottom: 0;
}

video {
  max-width: 100%;
}

.video-wrapper {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}

.video-wrapper::before {
  display: block;
  content: "";
  z-index: 0;
}

.video-wrapper.ratio-21x9::before {
  padding-top: 42.8571428571%;
}

.video-wrapper.ratio-16x9::before {
  padding-top: 56.25%;
}

.video-wrapper.ratio-4x3::before {
  padding-top: 75%;
}

.video-wrapper.ratio-1x1::before {
  padding-top: 100%;
}

.video-wrapper .poster {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-background-size: cover;
          background-size: cover;
  background-position: 50% 50%;
  z-index: 1;
  -webkit-transition: .3s;
  transition: .3s;
}

.video-wrapper .btn {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  z-index: 2;
  -webkit-transition: .3s;
  transition: .3s;
}

.video-wrapper .btn:hover {
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.video-wrapper iframe {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.video-wrapper.reveal .poster,
.video-wrapper.reveal .btn {
  opacity: 0;
  visibility: hidden;
}

.video-btn-wrapper {
  position: relative;
}

.video-btn-wrapper .btn {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  -webkit-transition: .3s;
  transition: .3s;
}

.bg-video {
  position: absolute;
  object-fit: cover;
  width: 100%;
  height: 100%;
  z-index: -100;
}

.card {
  border: 0;
  border-radius: 0;
  -webkit-transition: .5s;
  transition: .5s;
}

.card > .alert,
.card .card-body .alert {
  border-radius: 0;
  margin-bottom: 0;
}

.card > .callout,
.card .card-body .callout {
  margin-bottom: 0;
}

.card > .nav-tabs,
.card .card-body .nav-tabs {
  margin-bottom: 0;
}

.card > a {
  height: 100%;
}

.card > .table-responsive .table,
.card > .table {
  margin-bottom: 0;
}

.card > .table-responsive .table tr td:first-child,
.card > .table-responsive .table tr th:first-child,
.card > .table tr td:first-child,
.card > .table tr th:first-child {
  padding-left: 20px;
}

.card > .table-responsive .table tr td:last-child,
.card > .table-responsive .table tr th:last-child,
.card > .table tr td:last-child,
.card > .table tr th:last-child {
  padding-right: 20px;
}

.card .card-hover-show {
  opacity: 0;
  -webkit-transition: .3s linear;
  transition: .3s linear;
}

.card:hover .card-hover-show {
  opacity: 1;
}

.card-block {
  padding-top: 30px;
  padding-bottom: 30px;
}

.card-title {
  font-weight: 600;
  line-height: 1.5;
  letter-spacing: .5px;
  border-bottom: #f1f2f3;
}

.card-title strong,
.card-title b {
  font-weight: 600;
}

.h6.card-title, h6.card-title {
  font-size: 13.5px;
}

.h5.card-title, h5.card-title {
  font-size: 15px;
}

.h4.card-title, h4.card-title {
  font-size: 17px;
}

.h3.card-title, h3.card-title {
  font-size: 19px;
}

.h2.card-title, h2.card-title {
  font-size: 22px;
}

.h1.card-title, h1.card-title {
  font-size: 26px;
}

.card-bordered {
  border: 1px solid #f1f2f3;
}

.card-shadowed {
  -webkit-box-shadow: 0 1px 25px rgba(0, 0, 0, 0.05);
          box-shadow: 0 1px 25px rgba(0, 0, 0, 0.05);
}

.card-hover-shadow:hover {
  -webkit-box-shadow: 0 1px 35px rgba(0, 0, 0, 0.07);
          box-shadow: 0 1px 35px rgba(0, 0, 0, 0.07);
}

.card-inverse,
.card-outline,
.map-dialog-inverse,
.text-inverse {
  background-color: #191919;
}

.card-inverse h1, .card-inverse h2, .card-inverse h3, .card-inverse h4, .card-inverse h5, .card-inverse h6,
.card-inverse .h1, .card-inverse .h2, .card-inverse .h3, .card-inverse .h4, .card-inverse .h5, .card-inverse .h6,
.card-outline h1,
.card-outline h2,
.card-outline h3,
.card-outline h4,
.card-outline h5,
.card-outline h6,
.card-outline .h1,
.card-outline .h2,
.card-outline .h3,
.card-outline .h4,
.card-outline .h5,
.card-outline .h6,
.map-dialog-inverse h1,
.map-dialog-inverse h2,
.map-dialog-inverse h3,
.map-dialog-inverse h4,
.map-dialog-inverse h5,
.map-dialog-inverse h6,
.map-dialog-inverse .h1,
.map-dialog-inverse .h2,
.map-dialog-inverse .h3,
.map-dialog-inverse .h4,
.map-dialog-inverse .h5,
.map-dialog-inverse .h6,
.text-inverse h1,
.text-inverse h2,
.text-inverse h3,
.text-inverse h4,
.text-inverse h5,
.text-inverse h6,
.text-inverse .h1,
.text-inverse .h2,
.text-inverse .h3,
.text-inverse .h4,
.text-inverse .h5,
.text-inverse .h6 {
  color: #fff;
}

.card-inverse .card-title a,
.card-outline .card-title a,
.map-dialog-inverse .card-title a,
.text-inverse .card-title a {
  color: #fff;
}

.card-inverse small,
.card-inverse .small,
.card-outline small,
.card-outline .small,
.map-dialog-inverse small,
.map-dialog-inverse .small,
.text-inverse small,
.text-inverse .small {
  color: #fff;
  opacity: .6;
}

.card-inverse p,
.card-outline p,
.map-dialog-inverse p,
.text-inverse p {
  color: rgba(255, 255, 255, 0.75);
}

.card-outline {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-hover-inverse:hover {
  background-color: #191919;
  border-color: #191919;
}

.text-inverse {
  background-color: transparent;
}

@media (max-width: 991px) {
  .h6.card-title, h6.card-title {
    font-size: 12px;
  }
  .h5.card-title, h5.card-title {
    font-size: 13px;
  }
  .h4.card-title, h4.card-title {
    font-size: 15px;
  }
  .h3.card-title, h3.card-title {
    font-size: 18px;
  }
  .h2.card-title, h2.card-title {
    font-size: 20px;
  }
  .h1.card-title, h1.card-title {
    font-size: 23px;
  }
}

.iconbox {
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-pack: center;
          justify-content: center;
  -webkit-box-align: center;
          align-items: center;
  width: 38px;
  height: 38px;
  line-height: 38px;
  background-color: #f5f6f7;
  color: #b5b9bf;
  border-radius: 10rem;
}

.iconbox.iconbox-xs {
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: .6875rem;
}

.iconbox.iconbox-sm {
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: .75rem;
}

.iconbox.iconbox-lg {
  width: 48px;
  height: 48px;
  line-height: 48px;
  font-size: 1.25rem;
}

.iconbox.iconbox-xl {
  width: 64px;
  height: 64px;
  line-height: 64px;
  font-size: 1.5rem;
}

.iconbox.iconbox-xxl {
  width: 96px;
  height: 96px;
  line-height: 96px;
  font-size: 2rem;
}

.iconbox-sq {
  border-radius: 0;
}

.iconbox-outline {
  border: 1px solid #f1f2f3;
  background-color: transparent;
}

.accordion .card {
  background-color: #fff;
  border: 1px solid #f1f2f3;
  margin-bottom: 16px;
}

.accordion .card:last-child {
  margin-bottom: 0;
}

.accordion .card-title {
  margin-bottom: 0;
  padding-left: 20px;
  padding-right: 20px;
  background-color: #fcfdfe;
  font-family: "Open Sans", sans-serif;
  letter-spacing: 0;
}

.accordion .card-title a {
  display: block;
  padding: 16px 33px;
  color: #000;
}

.accordion .card-title a::before {
  content: "\e648";
  display: inline-block;
  font-family: themify;
  font-size: 12px;
  margin-right: 20px;
  margin-left: -33px;
  -webkit-transition: .2s linear;
  transition: .2s linear;
}

.accordion .card-title a.collapsed::before {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.accordion {
  -webkit-box-shadow: 0 0px 15px rgba(0, 0, 0, 0.06);
          box-shadow: 0 0px 15px rgba(0, 0, 0, 0.06);
}

.accordion .card {
  margin-bottom: 0;
  border: none;
  border-bottom: 1px solid #f1f2f3;
}

.accordion .card:last-child {
  border-bottom: 0;
}

.accordion .card-title {
  border-bottom: 0;
  background-color: #fff;
}

.accordion .card-title a {
  padding-top: 20px;
  padding-bottom: 20px;
}

.alert {
  font-size: 0.875rem;
  border: none;
  border-radius: 3px;
}

.alert-link {
  font-weight: 500;
}

.close {
  font-weight: 400;
  outline: none;
  -webkit-transition: .2s linear;
  transition: .2s linear;
}

.badge {
  border-radius: 3px;
  font-weight: 400;
  line-height: 1.3;
  font-size: 85%;
}

.badge:empty {
  display: inline-block;
  vertical-align: inherit;
}

.badge-pill {
  border-radius: 10rem;
  padding: 6px 16px 7px;
}

.badge-primary {
  background-color: #0facf3;
}

.badge-primary[href]:focus, .badge-primary[href]:hover {
  background-color: #0b9cdd;
}

.badge-secondary {
  background-color: #e4e7ea;
}

.badge-secondary[href]:focus, .badge-secondary[href]:hover {
  background-color: #d6dadf;
}

.badge-success {
  background-color: #46da60;
}

.badge-success[href]:focus, .badge-success[href]:hover {
  background-color: #31d64e;
}

.badge-info {
  background-color: #0e97ff;
}

.badge-info[href]:focus, .badge-info[href]:hover {
  background-color: #008af4;
}

.badge-warning {
  background-color: #ffbe00;
}

.badge-warning[href]:focus, .badge-warning[href]:hover {
  background-color: #e6ab00;
}

.badge-danger {
  background-color: #ff4954;
}

.badge-danger[href]:focus, .badge-danger[href]:hover {
  background-color: #ff303c;
}

.badge-dark {
  background-color: #000000;
}

.badge-dark[href]:focus, .badge-dark[href]:hover {
  background-color: black;
}

.badge-default {
  color: #788394;
  background-color: #f5f6f7;
}

.badge-default[href]:focus, .badge-default[href]:hover {
  color: #788394;
  background-color: #edeef0;
}

.badge-secondary {
  color: #788394;
  background-color: #e4e7ea;
}

.badge-secondary[href]:focus, .badge-secondary[href]:hover {
  color: #788394;
  background-color: #dbdfe3;
}

.badge-sm {
  line-height: 1.2;
  padding-top: 1px;
  padding-bottom: 2px;
  font-size: 75%;
}

.badge-lg {
  line-height: 1.5;
  padding-top: 5px;
  padding-bottom: 5px;
  font-size: 95%;
}

.badge-xl {
  line-height: 1.7;
  padding-top: 7px;
  padding-bottom: 7px;
  font-size: 100%;
}

.btn {
  font-weight: 600;
  font-size: 11px;
  padding: 7px 26px;
  line-height: inherit;
  color: #b5b9bf;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  border-radius: 2px;
  background-color: #fff;
  border-color: #ebebeb;
  outline: none;
  -webkit-transition: 0.15s linear;
  transition: 0.15s linear;
}

.btn:hover {
  cursor: pointer;
}

.btn:focus, .btn.focus, .btn:active, .btn.active {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.btn:disabled, .btn.disabled {
  cursor: not-allowed;
}

button:focus {
  outline: none;
}

.btn-group-xs > .btn,
.btn-xs {
  font-size: 10px;
  padding: 2px 16px;
}

.btn-group-sm > .btn,
.btn-sm {
  font-size: 11px;
  padding: 4px 20px;
}

.btn-group-lg > .btn,
.btn-lg {
  font-size: 12px;
  padding: 9px 34px;
}

.btn-group-xl > .btn,
.btn-xl {
  font-size: 14px;
  padding: 12px 40px;
}

.btn-w-xs {
  width: 85px;
}

.btn-w-sm {
  width: 100px;
}

.btn-w-md {
  width: 120px;
}

.btn-w-lg {
  width: 145px;
}

.btn-w-xl {
  width: 180px;
}

.btn-round {
  border-radius: 10rem;
}

.btn-primary, .btn-primary:hover {
  background-color: #0facf3;
  border-color: #0facf3;
  color: #fff;
}

.btn-primary:hover {
  -webkit-box-shadow: 0 2px 10px rgba(15, 172, 243, 0.4);
          box-shadow: 0 2px 10px rgba(15, 172, 243, 0.4);
}

.btn-primary:focus, .btn-primary.focus {
  color: #fff;
}

.btn-primary.disabled, .btn-primary:disabled {
  background-color: #0facf3;
  border-color: #0facf3;
  opacity: 0.5;
}

.btn-primary:active, .btn-primary.active,
.show > .btn-primary.dropdown-toggle {
  background-color: #0a92cf;
  border-color: #0a92cf;
  color: #fff;
}

.btn-success, .btn-success:hover {
  background-color: #46da60;
  border-color: #46da60;
  color: #fff;
}

.btn-success:hover {
  -webkit-box-shadow: 0 2px 10px rgba(70, 218, 96, 0.4);
          box-shadow: 0 2px 10px rgba(70, 218, 96, 0.4);
}

.btn-success:focus, .btn-success.focus {
  color: #fff;
}

.btn-success.disabled, .btn-success:disabled {
  background-color: #46da60;
  border-color: #46da60;
  opacity: 0.5;
}

.btn-success:active, .btn-success.active,
.show > .btn-success.dropdown-toggle {
  background-color: #2dd54a;
  border-color: #2dd54a;
  color: #fff;
}

.btn-info, .btn-info:hover {
  background-color: #0e97ff;
  border-color: #0e97ff;
  color: #fff;
}

.btn-info:hover {
  -webkit-box-shadow: 0 2px 10px rgba(14, 151, 255, 0.4);
          box-shadow: 0 2px 10px rgba(14, 151, 255, 0.4);
}

.btn-info:focus, .btn-info.focus {
  color: #fff;
}

.btn-info.disabled, .btn-info:disabled {
  background-color: #0e97ff;
  border-color: #0e97ff;
  opacity: 0.5;
}

.btn-info:active, .btn-info.active,
.show > .btn-info.dropdown-toggle {
  background-color: #0088ee;
  border-color: #0088ee;
  color: #fff;
}

.btn-warning, .btn-warning:hover {
  background-color: #ffbe00;
  border-color: #ffbe00;
  color: #fff;
}

.btn-warning:hover {
  -webkit-box-shadow: 0 2px 10px rgba(255, 190, 0, 0.4);
          box-shadow: 0 2px 10px rgba(255, 190, 0, 0.4);
}

.btn-warning:focus, .btn-warning.focus {
  color: #fff;
}

.btn-warning.disabled, .btn-warning:disabled {
  background-color: #ffbe00;
  border-color: #ffbe00;
  opacity: 0.5;
}

.btn-warning:active, .btn-warning.active,
.show > .btn-warning.dropdown-toggle {
  background-color: #e6ab00;
  border-color: #e6ab00;
  color: #fff;
}

.btn-danger, .btn-danger:hover {
  background-color: #ff4954;
  border-color: #ff4954;
  color: #fff;
}

.btn-danger:hover {
  -webkit-box-shadow: 0 2px 10px rgba(255, 73, 84, 0.4);
          box-shadow: 0 2px 10px rgba(255, 73, 84, 0.4);
}

.btn-danger:focus, .btn-danger.focus {
  color: #fff;
}

.btn-danger.disabled, .btn-danger:disabled {
  background-color: #ff4954;
  border-color: #ff4954;
  opacity: 0.5;
}

.btn-danger:active, .btn-danger.active,
.show > .btn-danger.dropdown-toggle {
  background-color: #ff303c;
  border-color: #ff303c;
  color: #fff;
}

.btn-dark, .btn-dark:hover {
  background-color: #000000;
  border-color: #000000;
  color: #fff;
}

.btn-dark:hover {
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
}

.btn-dark:focus, .btn-dark.focus {
  color: #fff;
}

.btn-dark.disabled, .btn-dark:disabled {
  background-color: #000000;
  border-color: #000000;
  opacity: 0.5;
}

.btn-dark:active, .btn-dark.active,
.show > .btn-dark.dropdown-toggle {
  background-color: black;
  border-color: black;
  color: #fff;
}

.btn-secondary, .btn-secondary:hover {
  background-color: #e4e7ea;
  border-color: #e4e7ea;
  color: #535353;
}

.btn-secondary:hover {
  -webkit-box-shadow: 0 2px 10px rgba(228, 231, 234, 0.4);
          box-shadow: 0 2px 10px rgba(228, 231, 234, 0.4);
}

.btn-secondary:focus, .btn-secondary.focus {
  color: #535353;
}

.btn-secondary.disabled, .btn-secondary:disabled {
  background-color: #e4e7ea;
  border-color: #e4e7ea;
  opacity: 0.5;
}

.btn-secondary:active, .btn-secondary.active,
.show > .btn-secondary.dropdown-toggle {
  background-color: #dbdfe3;
  border-color: #dbdfe3;
  color: #535353;
}

.btn-link {
  color: #535353;
  font-weight: 500;
}

.btn-link:hover, .btn-link:focus {
  text-decoration: none;
  color: #0facf3;
}

.btn-white {
  background-color: #fff;
  border-color: #f1f2f3;
  color: #b5b9bf;
}

.btn-white:hover, .btn-white:focus {
  background-color: #fff;
  color: #535353;
}

.btn-facebook, .btn-facebook:hover {
  background-color: #3b5998;
  border-color: #3b5998;
  color: #fff;
}

.btn-facebook:hover {
  -webkit-box-shadow: 0 2px 10px rgba(59, 89, 152, 0.4);
          box-shadow: 0 2px 10px rgba(59, 89, 152, 0.4);
}

.btn-facebook:focus, .btn-facebook.focus {
  color: #fff;
}

.btn-facebook.disabled, .btn-facebook:disabled {
  background-color: #3b5998;
  border-color: #3b5998;
  opacity: 0.5;
}

.btn-facebook:active, .btn-facebook.active,
.show > .btn-facebook.dropdown-toggle {
  background-color: #344e86;
  border-color: #344e86;
  color: #fff;
}

.btn-google, .btn-google:hover {
  background-color: #dd4b39;
  border-color: #dd4b39;
  color: #fff;
}

.btn-google:hover {
  -webkit-box-shadow: 0 2px 10px rgba(221, 75, 57, 0.4);
          box-shadow: 0 2px 10px rgba(221, 75, 57, 0.4);
}

.btn-google:focus, .btn-google.focus {
  color: #fff;
}

.btn-google.disabled, .btn-google:disabled {
  background-color: #dd4b39;
  border-color: #dd4b39;
  opacity: 0.5;
}

.btn-google:active, .btn-google.active,
.show > .btn-google.dropdown-toggle {
  background-color: #d73925;
  border-color: #d73925;
  color: #fff;
}

.btn-twitter, .btn-twitter:hover {
  background-color: #00aced;
  border-color: #00aced;
  color: #fff;
}

.btn-twitter:hover {
  -webkit-box-shadow: 0 2px 10px rgba(0, 172, 237, 0.4);
          box-shadow: 0 2px 10px rgba(0, 172, 237, 0.4);
}

.btn-twitter:focus, .btn-twitter.focus {
  color: #fff;
}

.btn-twitter.disabled, .btn-twitter:disabled {
  background-color: #00aced;
  border-color: #00aced;
  opacity: 0.5;
}

.btn-twitter:active, .btn-twitter.active,
.show > .btn-twitter.dropdown-toggle {
  background-color: #0099d4;
  border-color: #0099d4;
  color: #fff;
}

.btn-outline {
  -webkit-transition: .5s;
  transition: .5s;
}

.btn-primary-outline {
  color: #0facf3;
  background-color: transparent;
  border-color: #0facf3;
}

.btn-primary-outline:hover {
  color: #fff;
  background-color: #0facf3;
  border-color: #0facf3;
}

.btn-primary-outline:active, .btn-primary-outline.active,
.show > .btn-primary-outline.dropdown-toggle {
  background-color: #0a92cf;
  border-color: #0a92cf;
}

.btn-success-outline {
  color: #46da60;
  background-color: transparent;
  border-color: #46da60;
}

.btn-success-outline:hover {
  color: #fff;
  background-color: #46da60;
  border-color: #46da60;
}

.btn-success-outline:active, .btn-success-outline.active,
.show > .btn-success-outline.dropdown-toggle {
  background-color: #2dd54a;
  border-color: #2dd54a;
}

.btn-info-outline {
  color: #0e97ff;
  background-color: transparent;
  border-color: #0e97ff;
}

.btn-info-outline:hover {
  color: #fff;
  background-color: #0e97ff;
  border-color: #0e97ff;
}

.btn-info-outline:active, .btn-info-outline.active,
.show > .btn-info-outline.dropdown-toggle {
  background-color: #0088ee;
  border-color: #0088ee;
}

.btn-warning-outline {
  color: #ffbe00;
  background-color: transparent;
  border-color: #ffbe00;
}

.btn-warning-outline:hover {
  color: #fff;
  background-color: #ffbe00;
  border-color: #ffbe00;
}

.btn-warning-outline:active, .btn-warning-outline.active,
.show > .btn-warning-outline.dropdown-toggle {
  background-color: #e6ab00;
  border-color: #e6ab00;
}

.btn-danger-outline {
  color: #ff4954;
  background-color: transparent;
  border-color: #ff4954;
}

.btn-danger-outline:hover {
  color: #fff;
  background-color: #ff4954;
  border-color: #ff4954;
}

.btn-danger-outline:active, .btn-danger-outline.active,
.show > .btn-danger-outline.dropdown-toggle {
  background-color: #ff303c;
  border-color: #ff303c;
}

.btn-secondary-outline {
  color: #e4e7ea;
  background-color: transparent;
  border-color: #e4e7ea;
}

.btn-secondary-outline:hover {
  color: #535353;
  background-color: #e4e7ea;
  border-color: #e4e7ea;
}

.btn-secondary-outline:active, .btn-secondary-outline.active,
.show > .btn-secondary-outline.dropdown-toggle {
  background-color: #dbdfe3;
  border-color: #dbdfe3;
}

.btn-outline.btn-primary {
  color: #0facf3;
  background-color: transparent;
  border-color: #0facf3;
}

.btn-outline.btn-primary:hover {
  color: #fff;
  background-color: #0facf3;
  border-color: #0facf3;
}

.btn-outline.btn-primary:active, .btn-outline.btn-primary.active,
.show > .btn-outline.btn-primary.dropdown-toggle {
  background-color: #0a92cf;
  border-color: #0a92cf;
}

.btn-outline.btn-success {
  color: #46da60;
  background-color: transparent;
  border-color: #46da60;
}

.btn-outline.btn-success:hover {
  color: #fff;
  background-color: #46da60;
  border-color: #46da60;
}

.btn-outline.btn-success:active, .btn-outline.btn-success.active,
.show > .btn-outline.btn-success.dropdown-toggle {
  background-color: #2dd54a;
  border-color: #2dd54a;
}

.btn-outline.btn-info {
  color: #0e97ff;
  background-color: transparent;
  border-color: #0e97ff;
}

.btn-outline.btn-info:hover {
  color: #fff;
  background-color: #0e97ff;
  border-color: #0e97ff;
}

.btn-outline.btn-info:active, .btn-outline.btn-info.active,
.show > .btn-outline.btn-info.dropdown-toggle {
  background-color: #0088ee;
  border-color: #0088ee;
}

.btn-outline.btn-warning {
  color: #ffbe00;
  background-color: transparent;
  border-color: #ffbe00;
}

.btn-outline.btn-warning:hover {
  color: #fff;
  background-color: #ffbe00;
  border-color: #ffbe00;
}

.btn-outline.btn-warning:active, .btn-outline.btn-warning.active,
.show > .btn-outline.btn-warning.dropdown-toggle {
  background-color: #e6ab00;
  border-color: #e6ab00;
}

.btn-outline.btn-danger {
  color: #ff4954;
  background-color: transparent;
  border-color: #ff4954;
}

.btn-outline.btn-danger:hover {
  color: #fff;
  background-color: #ff4954;
  border-color: #ff4954;
}

.btn-outline.btn-danger:active, .btn-outline.btn-danger.active,
.show > .btn-outline.btn-danger.dropdown-toggle {
  background-color: #ff303c;
  border-color: #ff303c;
}

.btn-outline.btn-secondary {
  color: #e4e7ea;
  background-color: transparent;
  border-color: #e4e7ea;
  color: #b5b9bf;
}

.btn-outline.btn-secondary:hover {
  color: #535353;
  background-color: #e4e7ea;
  border-color: #e4e7ea;
}

.btn-outline.btn-secondary:active, .btn-outline.btn-secondary.active,
.show > .btn-outline.btn-secondary.dropdown-toggle {
  background-color: #dbdfe3;
  border-color: #dbdfe3;
}

.btn-outline.btn-white {
  color: #fff;
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline.btn-white:hover {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.btn-outline.btn-white:active, .btn-outline.btn-white.active,
.show > .btn-outline.btn-white.dropdown-toggle {
  background-color: #f2f2f2;
  border-color: #f2f2f2;
}

.btn-outline.btn-dark {
  color: #000000;
  background-color: transparent;
  border-color: #000000;
}

.btn-outline.btn-dark:hover {
  color: #fff;
  background-color: #000000;
  border-color: #000000;
}

.btn-outline.btn-dark:active, .btn-outline.btn-dark.active,
.show > .btn-outline.btn-dark.dropdown-toggle {
  background-color: black;
  border-color: black;
}

.btn-outline.btn-white:hover,
.btn-outline.btn-white:focus {
  color: #535353;
}

.btn-outline.active {
  color: #fff;
}

.btn-outline.btn-secondary.active {
  color: #535353;
}

.btn-circular {
  width: 54px;
  height: 54px;
  padding: 0;
  border-radius: 50%;
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-pack: center;
          justify-content: center;
}

.btn-circular i {
  font-size: 20px;
}

.btn-circular .fa-play {
  margin-left: 4px;
  font-size: 14px;
}

.btn-circular.btn-xs {
  width: 32px;
  height: 32px;
}

.btn-circular.btn-xs i {
  font-size: 12px;
}

.btn-circular.btn-xs .fa-play {
  font-size: 8px;
}

.btn-circular.btn-sm {
  width: 44px;
  height: 44px;
}

.btn-circular.btn-sm i {
  font-size: 16px;
}

.btn-circular.btn-sm .fa-play {
  font-size: 12px;
}

.btn-circular.btn-lg {
  width: 68px;
  height: 68px;
}

.btn-circular.btn-lg i {
  font-size: 24px;
}

.btn-circular.btn-lg .fa-play {
  font-size: 16px;
}

.btn-circular.btn-xl {
  width: 88px;
  height: 88px;
}

.btn-circular.btn-xl i {
  font-size: 28px;
}

.btn-circular.btn-xl .fa-play {
  font-size: 18px;
}

.countdown {
  text-align: center;
  padding: 0 15px;
}

.countdown .col {
  padding: 0;
}

.countdown h5 {
  font-weight: 600;
  font-size: 50px;
  margin-bottom: 0;
}

.countdown small {
  font-size: 13px;
}

@media (max-width: 767px) {
  .countdown h5 {
    font-size: 33px;
  }
  .countdown small {
    font-size: 11px;
  }
}

.countdown-uppercase small {
  text-transform: uppercase;
}

.countdown-sm h5 {
  font-size: 40px;
}

.countdown-sm small {
  font-size: 12px;
}

@media (max-width: 767px) {
  .countdown-sm h5 {
    font-size: 28px;
  }
  .countdown-sm small {
    font-size: 10px;
  }
}

.countdown-lg h5 {
  font-size: 60px;
}

.countdown-lg small {
  font-size: 14px;
}

@media (max-width: 767px) {
  .countdown-lg h5 {
    font-size: 38px;
  }
  .countdown-lg small {
    font-size: 12px;
  }
}

.countdown-outline .col {
  border: 1px solid #535353;
}

.countdown-outline h5 {
  border-bottom: 1px solid #535353;
}

.countdown-inverse .col {
  border-color: rgba(255, 255, 255, 0.5);
}

.countdown-inverse h5,
.countdown-inverse small {
  color: #fff;
  border-color: rgba(255, 255, 255, 0.5);
}

.countdown-inverse small {
  opacity: .5;
}

.constellation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.dropdown-toggle {
  cursor: pointer;
}

.dropdown-toggle::after {
  content: "\f107";
  font-family: FontAwesome;
  margin-left: 8px;
  border: none;
  vertical-align: initial;
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
}

.dropup .dropdown-toggle::after {
  content: "\f106";
  -webkit-transform: translateY(0);
          transform: translateY(0);
}

.dropdown-toggle.no-caret::after {
  display: none;
}

.dropdown-menu {
  padding: 10px 16px;
  font-size: 14px;
  margin-top: 5px;
  border: none;
  -webkit-box-shadow: 0 2px 25px rgba(0, 0, 0, 0.07);
          box-shadow: 0 2px 25px rgba(0, 0, 0, 0.07);
}

.dropdown-menu *:last-child {
  margin-bottom: 0;
}

.dropdown-item {
  padding-left: 0;
  padding-right: 0;
  color: #b5b9bf;
  background-color: transparent;
  opacity: .8;
}

.dropdown-item:hover {
  color: #535353;
  opacity: 1;
}

.dropdown-item:active, .dropdown-item:hover, .dropdown-item:focus {
  background-color: transparent;
}

.dropdown-header {
  padding-left: 0;
  padding-right: 0;
  font-weight: 600;
}

.dropdown-divider {
  background-color: #f1f2f3;
  margin-left: -20px;
  margin-right: -20px;
}

.open-on-hover:hover .dropdown-menu {
  display: block;
  margin-top: 0;
}

.nav-item {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
          align-items: center;
}

.nav-item i {
  width: 1.28571429em;
  text-align: center;
}

.nav-link {
  -webkit-box-flex: 1;
          flex-grow: 1;
  font-weight: 400;
  color: #535353;
  padding: 2px 0;
  white-space: nowrap;
}

.nav-link:hover, .nav-link:focus, .nav-link.active {
  color: #0facf3;
}

.nav-link.disabled {
  color: #b5b9bf;
  opacity: .7;
}

.nav-primary .nav-link:not(.disabled):hover, .nav-primary .nav-link:not(.disabled):focus {
  color: #0facf3;
}

.nav-action {
  color: #b5b9bf;
  font-size: 14px;
  opacity: 0;
  margin: 0 4px;
}

.nav-action:hover {
  color: #535353;
}

.nav-item:hover .nav-action {
  opacity: 1;
}

.nav-actions-visible .nav-action {
  opacity: 1;
}

.nav-hero {
  -webkit-box-pack: center;
          justify-content: center;
}

.nav-hero .nav-item {
  padding-left: 20px;
  padding-right: 20px;
}

.nav-hero .nav-link {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}

.nav-hero.flex-column .nav-item {
  padding: 4px 0;
}

.nav-pills .nav-link {
  border-radius: 2px;
}

.nav-pills .nav-link:not(.disabled):hover {
  background-color: #f9fafb;
}

.nav-pills .nav-item.open .nav-link,
.nav-pills .nav-item.open .nav-link:focus,
.nav-pills .nav-item.open .nav-link:hover,
.nav-pills .nav-link.active,
.nav-pills .nav-link.active:focus,
.nav-pills .nav-link.active:hover {
  color: #535353;
  background-color: #f9fafb;
}

.nav-outline {
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-pack: center;
          justify-content: center;
}

.nav-outline .nav-link {
  -webkit-box-pack: center;
          justify-content: center;
  padding: 4px 20px;
  border: 1px solid #535353;
  color: #535353;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 1px;
}

.nav-outline .nav-link:hover, .nav-outline .nav-link.active {
  color: #fff;
  background-color: #535353;
}

.nav-outline .nav-item + .nav-item .nav-link {
  border-left: 0;
}

.nav-round .nav-item:first-child .nav-link {
  border-top-left-radius: 10rem;
  border-bottom-left-radius: 10rem;
}

.nav-round .nav-item:last-child .nav-link {
  border-top-right-radius: 10rem;
  border-bottom-right-radius: 10rem;
}

.nav-dot-separated .nav-item,
.nav-dot-separated > .nav-link {
  margin-left: 0;
}

.nav-dot-separated .nav-item::after,
.nav-dot-separated > .nav-link::after {
  content: '•';
  vertical-align: middle;
  padding: 0 8px;
  color: #788394;
  opacity: 0.4;
  cursor: default;
}

.nav-dot-separated .nav-item:last-child::after,
.nav-dot-separated > .nav-link:last-child::after {
  display: none;
}

.nav-dotted .nav-link {
  line-height: normal;
  border-bottom: 1px dotted #b1b7c1;
}

.nav-header {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #b5b9bf;
}

@media (max-width: 991px) {
  .nav-round.nav-outline .nav-item {
    margin: 4px;
  }
  .nav-round.nav-outline .nav-link {
    border-radius: 10rem;
  }
}

.page-link {
  color: #535353;
  font-weight: 400;
  border-color: #ebebeb;
  padding: 0;
  margin: 0 3px;
  min-width: 34px;
  line-height: 32px;
  text-align: center;
  border-radius: 2px;
}

.page-link:hover, .page-link:focus {
  background-color: #f8f8f8;
  color: #535353;
}

.page-link span {
  font-size: 75%;
}

.page-item:last-child .page-link,
.page-item:first-child .page-link {
  border-radius: 2px;
}

.page-item.disabled .page-link, .page-item.disabled .page-link:focus, .page-item.disabled .page-link:hover {
  opacity: 0.6;
  cursor: default;
}

.page-item.active .page-link, .page-item.active .page-link:focus, .page-item.active .page-link:hover {
  background-color: #0facf3;
  border-color: #0facf3;
  font-weight: 500;
}

.pagination.no-border .page-link {
  border: none;
}

.pagination.no-border .page-link:hover, .pagination.no-border .page-link:focus {
  border-color: #f8f8f8;
}

.pagination-sm .page-link {
  padding: 0;
  min-width: 26px;
  line-height: 24px;
}

.pagination-lg .page-link {
  padding: 0;
  min-width: 40px;
  line-height: 38px;
}

.price-unit {
  display: inline-block;
  font-size: 16px;
  vertical-align: text-top;
  margin-right: -10px;
  margin-top: 16px;
}

.plan-period {
  display: inline-block;
  font-size: 12px;
  vertical-align: text-bottom;
  margin-left: -10px;
  margin-bottom: 14px;
  color: #b5b9bf;
}

.pricing-1 {
  background-color: #fff;
  border: 1px solid #f1f2f3;
  border-radius: 5px;
  -webkit-transition: .5s;
  transition: .5s;
}

.pricing-1.popular, .pricing-1:hover {
  -webkit-box-shadow: 0 0 25px rgba(0, 0, 0, 0.08);
          box-shadow: 0 0 25px rgba(0, 0, 0, 0.08);
}

.pricing-1 .plan-name {
  padding: 16px;
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 600;
  border-bottom: 1px solid #f1f2f3;
  background-color: #fcfdfe;
}

.pricing-1 .price {
  font-size: 60px;
  font-weight: 600;
}

.pricing-1 .btn {
  border-color: transparent;
}

.pricing-2 {
  text-align: center;
}

.pricing-2 .price {
  font-size: 70px;
  font-weight: 100;
  opacity: .9;
}

.pricing-2 .plan-name {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 13px;
  opacity: .9;
}

.pricing-2 .plan-description {
  opacity: .7;
}

.pricing-2 .btn {
  opacity: .9;
}

.pricing-3 {
  text-align: center;
  padding: 30px;
  border-radius: 10px;
}

.pricing-3.popular {
  background-color: #f8f8f8;
  position: relative;
}

.pricing-3 .popular-tag {
  position: absolute;
  top: 0;
  left: 40px;
  background-color: #46da60;
  color: #fff;
  padding: 1px 12px;
  padding-right: 24px;
  text-transform: uppercase;
  font-size: 11px;
  letter-spacing: 1.5px;
  font-weight: 400;
  border-top-right-radius: 5rem;
  border-bottom-right-radius: 5rem;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  -webkit-transform-origin: top left;
          transform-origin: top left;
}

.pricing-3 .price {
  font-size: 60px;
  font-weight: 800;
  margin-bottom: 16px;
}

.pricing-3 .plan-name {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 13px;
  opacity: .9;
}

.pricing-3 ul {
  list-style: none;
  padding-left: 0;
}

.pricing-4 {
  -webkit-box-align: center;
          align-items: center;
  color: #535353;
  border: 1px solid #f1f2f3;
}

.pricing-4.popular, .pricing-4:hover, .pricing-4:focus {
  -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, 0.06);
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.06);
  color: #535353;
}

.pricing-4 .plan-description {
  background-color: #f8f8f8;
  padding: 30px;
}

.pricing-4 .plan-description h5 {
  font-weight: 600;
}

.pricing-4 .plan-price {
  text-align: center;
}

.pricing-4 .plan-price h3 {
  font-weight: 300;
  font-size: 35px;
  padding-top: 1rem;
}

.pricing-4 .plan-price h3 small {
  font-weight: 300;
  font-size: 13px;
}

.scroll-down-1 span {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin: 0 5px;
  border-left: 1px solid #000;
  border-bottom: 1px solid #000;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  -webkit-animation: scrollDown1 2s infinite;
          animation: scrollDown1 2s infinite;
}

@-webkit-keyframes scrollDown1 {
  0% {
    -webkit-transform: rotate(-45deg) translate(0, 0);
            transform: rotate(-45deg) translate(0, 0);
  }
  20% {
    -webkit-transform: rotate(-45deg) translate(-16px, 16px);
            transform: rotate(-45deg) translate(-16px, 16px);
  }
  40% {
    -webkit-transform: rotate(-45deg) translate(0, 0);
            transform: rotate(-45deg) translate(0, 0);
  }
}

@keyframes scrollDown1 {
  0% {
    -webkit-transform: rotate(-45deg) translate(0, 0);
            transform: rotate(-45deg) translate(0, 0);
  }
  20% {
    -webkit-transform: rotate(-45deg) translate(-16px, 16px);
            transform: rotate(-45deg) translate(-16px, 16px);
  }
  40% {
    -webkit-transform: rotate(-45deg) translate(0, 0);
            transform: rotate(-45deg) translate(0, 0);
  }
}

.scroll-down-2 span {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin: 0 5px;
  border-left: 1px solid #000;
  border-bottom: 1px solid #000;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  -webkit-animation: scrollDown2 2s infinite;
          animation: scrollDown2 2s infinite;
}

@-webkit-keyframes scrollDown2 {
  0% {
    -webkit-transform: rotate(-45deg) translate(0, 0);
            transform: rotate(-45deg) translate(0, 0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    -webkit-transform: rotate(-45deg) translate(-20px, 20px);
            transform: rotate(-45deg) translate(-20px, 20px);
    opacity: 0;
  }
}

@keyframes scrollDown2 {
  0% {
    -webkit-transform: rotate(-45deg) translate(0, 0);
            transform: rotate(-45deg) translate(0, 0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    -webkit-transform: rotate(-45deg) translate(-20px, 20px);
            transform: rotate(-45deg) translate(-20px, 20px);
    opacity: 0;
  }
}

.scroll-down-3 {
  position: relative;
}

.scroll-down-3 span {
  position: absolute;
  display: inline-block;
  width: 24px;
  height: 24px;
  margin: 0 5px;
  border-left: 1px solid #000;
  border-bottom: 1px solid #000;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  -webkit-animation: scrollDown3 2s infinite;
          animation: scrollDown3 2s infinite;
  opacity: 0;
}

.scroll-down-3 span:nth-of-type(1) {
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}

.scroll-down-3 span:nth-of-type(2) {
  top: 16px;
  -webkit-animation-delay: .15s;
          animation-delay: .15s;
}

.scroll-down-3 span:nth-of-type(3) {
  top: 32px;
  -webkit-animation-delay: .3s;
          animation-delay: .3s;
}

@-webkit-keyframes scrollDown3 {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes scrollDown3 {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.scroll-down-4 span {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 50px;
  border: 2px solid #000;
  border-radius: 50px;
  opacity: .7;
}

.scroll-down-4 span::before {
  position: absolute;
  top: 10px;
  left: 50%;
  content: '';
  width: 6px;
  height: 6px;
  margin-left: -3px;
  background-color: #000;
  border-radius: 100%;
}

.scroll-down-5 span {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 50px;
  border: 2px solid #000;
  border-radius: 50px;
  opacity: .7;
}

.scroll-down-5 span::before {
  position: absolute;
  top: 10px;
  left: 50%;
  content: '';
  width: 6px;
  height: 6px;
  margin-left: -3px;
  background-color: #000;
  border-radius: 100%;
  -webkit-animation: scrollDown5 2s infinite;
          animation: scrollDown5 2s infinite;
}

@-webkit-keyframes scrollDown5 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
  80% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px);
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes scrollDown5 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
  80% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px);
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

.scroll-down-inverse span {
  border-color: #fff;
}

.scroll-down-inverse span::before {
  background-color: #fff;
}

.scroll-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 44px;
  font-size: 26px;
  text-align: center;
  background-color: #0facf3;
  color: #fff;
  border-radius: 50%;
  opacity: 0;
  z-index: 980;
  -webkit-box-shadow: 0 3px 15px rgba(15, 172, 243, 0.4);
          box-shadow: 0 3px 15px rgba(15, 172, 243, 0.4);
  -webkit-transition: .5s ease-in-out;
  transition: .5s ease-in-out;
}

.body-scrolled .scroll-top {
  opacity: .6;
}

.body-scrolled .scroll-top:hover, .body-scrolled .scroll-top:focus {
  opacity: 1;
  color: #fff;
  -webkit-box-shadow: 0 3px 20px rgba(15, 172, 243, 0.6);
          box-shadow: 0 3px 20px rgba(15, 172, 243, 0.6);
  -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
  -webkit-transition: .2s ease-in-out;
  transition: .2s ease-in-out;
}

@media (max-width: 991px) {
  .scroll-top {
    right: 15px;
    bottom: 10px;
    width: 34px;
    height: 34px;
    line-height: 30px;
    font-size: 18px;
  }
}

.social a {
  display: inline-block;
  margin-right: 20px;
  text-align: center;
  color: inherit;
  font-size: 0.9325rem;
  border-radius: 3px;
  opacity: 0.8;
  -webkit-transition: .4s;
  transition: .4s;
}

.social a:hover {
  opacity: 1;
}

.social a:last-child {
  margin-right: 0;
}

.social a.social-facebook:hover {
  color: #3b5998;
}

.social a.social-google:hover {
  color: #dd4b39;
}

.social a.social-gplus:hover {
  color: #dd4b39;
}

.social a.social-google-plus:hover {
  color: #dd4b39;
}

.social a.social-twitter:hover {
  color: #00aced;
}

.social a.social-linkedin:hover {
  color: #007bb6;
}

.social a.social-pinterest:hover {
  color: #cb2027;
}

.social a.social-git:hover {
  color: #666666;
}

.social a.social-tumblr:hover {
  color: #32506d;
}

.social a.social-vimeo:hover {
  color: #aad450;
}

.social a.social-youtube:hover {
  color: #bb0000;
}

.social a.social-flickr:hover {
  color: #ff0084;
}

.social a.social-reddit:hover {
  color: #ff4500;
}

.social a.social-dribbble:hover {
  color: #ea4c89;
}

.social a.social-skype:hover {
  color: #00aff0;
}

.social a.social-instagram:hover {
  color: #517fa4;
}

.social a.social-lastfm:hover {
  color: #c3000d;
}

.social a.social-behance:hover {
  color: #1769ff;
}

.social a.social-rss:hover {
  color: #f26522;
}

.social.social-sm a {
  font-size: 0.8125rem;
  margin-right: 16px;
}

.social.social-lg a {
  font-size: 1.25rem;
  margin-right: 24px;
}

.social-hover-primary a:hover {
  color: #0facf3 !important;
}

.social-color-brand a.social-facebook {
  color: #3b5998;
}

.social-color-brand a.social-google {
  color: #dd4b39;
}

.social-color-brand a.social-gplus {
  color: #dd4b39;
}

.social-color-brand a.social-google-plus {
  color: #dd4b39;
}

.social-color-brand a.social-twitter {
  color: #00aced;
}

.social-color-brand a.social-linkedin {
  color: #007bb6;
}

.social-color-brand a.social-pinterest {
  color: #cb2027;
}

.social-color-brand a.social-git {
  color: #666666;
}

.social-color-brand a.social-tumblr {
  color: #32506d;
}

.social-color-brand a.social-vimeo {
  color: #aad450;
}

.social-color-brand a.social-youtube {
  color: #bb0000;
}

.social-color-brand a.social-flickr {
  color: #ff0084;
}

.social-color-brand a.social-reddit {
  color: #ff4500;
}

.social-color-brand a.social-dribbble {
  color: #ea4c89;
}

.social-color-brand a.social-skype {
  color: #00aff0;
}

.social-color-brand a.social-instagram {
  color: #517fa4;
}

.social-color-brand a.social-lastfm {
  color: #c3000d;
}

.social-color-brand a.social-behance {
  color: #1769ff;
}

.social-color-brand a.social-rss {
  color: #f26522;
}

.social-gray a {
  color: #a5b3c7;
}

.social-gray a:hover {
  color: #b5b9bf !important;
}

.social-rounded a {
  border-radius: 50%;
  border: 1px solid #ebebeb;
}

.social-rounded.social-gray a {
  border-color: #f5f6f7;
}

.social-rounded.social-inverse a {
  border-color: rgba(255, 255, 255, 0.5);
  font-size: 0.8125rem;
}

.social-rounded.social-inverse.social-sm a {
  font-size: 0.6875rem;
}

.social-rounded.social-inverse.social-lg a {
  font-size: 0.9375rem;
}

.social-boxed a {
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-right: 4px;
  margin-bottom: 4px;
}

.social-boxed a:hover {
  color: #fff !important;
}

.social-boxed a.social-facebook:hover {
  background-color: #3b5998;
}

.social-boxed a.social-google:hover {
  background-color: #dd4b39;
}

.social-boxed a.social-gplus:hover {
  background-color: #dd4b39;
}

.social-boxed a.social-google-plus:hover {
  background-color: #dd4b39;
}

.social-boxed a.social-twitter:hover {
  background-color: #00aced;
}

.social-boxed a.social-linkedin:hover {
  background-color: #007bb6;
}

.social-boxed a.social-pinterest:hover {
  background-color: #cb2027;
}

.social-boxed a.social-git:hover {
  background-color: #666666;
}

.social-boxed a.social-tumblr:hover {
  background-color: #32506d;
}

.social-boxed a.social-vimeo:hover {
  background-color: #aad450;
}

.social-boxed a.social-youtube:hover {
  background-color: #bb0000;
}

.social-boxed a.social-flickr:hover {
  background-color: #ff0084;
}

.social-boxed a.social-reddit:hover {
  background-color: #ff4500;
}

.social-boxed a.social-dribbble:hover {
  background-color: #ea4c89;
}

.social-boxed a.social-skype:hover {
  background-color: #00aff0;
}

.social-boxed a.social-instagram:hover {
  background-color: #517fa4;
}

.social-boxed a.social-lastfm:hover {
  background-color: #c3000d;
}

.social-boxed a.social-behance:hover {
  background-color: #1769ff;
}

.social-boxed a.social-rss:hover {
  background-color: #f26522;
}

.social-boxed.social-sm a {
  width: 28px;
  height: 28px;
  line-height: 28px;
  margin-right: 2px;
  margin-bottom: 2px;
}

.social-boxed.social-lg a {
  width: 38px;
  height: 38px;
  line-height: 36px;
  margin-right: 6px;
  margin-bottom: 6px;
}

.social-boxed.social-gray a {
  background-color: #f5f6f7;
}

.social-boxed.social-gray a:hover {
  color: #788394 !important;
  background-color: #f2f3f5 !important;
}

.social-bordered a {
  border: 1px solid #ebebeb;
}

.social-bordered.social-gray a {
  border-color: #f5f6f7;
}

.social-bordered.social-inverse a {
  border-color: rgba(255, 255, 255, 0.6);
}

.social-inverse a {
  color: #fff;
}

.social-inverse a:hover {
  color: #fff !important;
}

.social-inverse.social-boxed a:hover {
  color: #535353 !important;
  background-color: #fff !important;
  border-color: #fff;
}

.social-colored a {
  color: #fff;
  opacity: 1;
}

.social-colored .social-facebook {
  background-color: #3b5998;
}

.social-colored .social-google {
  background-color: #dd4b39;
}

.social-colored .social-gplus {
  background-color: #dd4b39;
}

.social-colored .social-google-plus {
  background-color: #dd4b39;
}

.social-colored .social-twitter {
  background-color: #00aced;
}

.social-colored .social-linkedin {
  background-color: #007bb6;
}

.social-colored .social-pinterest {
  background-color: #cb2027;
}

.social-colored .social-git {
  background-color: #666666;
}

.social-colored .social-tumblr {
  background-color: #32506d;
}

.social-colored .social-vimeo {
  background-color: #aad450;
}

.social-colored .social-youtube {
  background-color: #bb0000;
}

.social-colored .social-flickr {
  background-color: #ff0084;
}

.social-colored .social-reddit {
  background-color: #ff4500;
}

.social-colored .social-dribbble {
  background-color: #ea4c89;
}

.social-colored .social-skype {
  background-color: #00aff0;
}

.social-colored .social-instagram {
  background-color: #517fa4;
}

.social-colored .social-lastfm {
  background-color: #c3000d;
}

.social-colored .social-behance {
  background-color: #1769ff;
}

.social-colored .social-rss {
  background-color: #f26522;
}

.social-cycling a:hover i {
  -webkit-animation: cycle .4s forwards;
          animation: cycle .4s forwards;
}

@-webkit-keyframes cycle {
  49% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  50% {
    opacity: 0;
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  51% {
    opacity: 1;
  }
}

@keyframes cycle {
  49% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  50% {
    opacity: 0;
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  51% {
    opacity: 1;
  }
}

.nav-vertical {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
}

.nav-vertical .nav-item {
  margin-bottom: 10px;
}

.nav-vertical .nav-link {
  padding: 20px;
  border-radius: 5px;
  white-space: normal;
  border: 1px solid transparent;
  opacity: .7;
}

.nav-vertical .nav-link.active {
  border: 1px solid #f1f2f3;
  background-color: #fff;
  opacity: 1;
  -webkit-box-shadow: 0 1px 25px rgba(0, 0, 0, 0.04);
          box-shadow: 0 1px 25px rgba(0, 0, 0, 0.04);
}

.nav-vertical .nav-link:hover {
  opacity: 1;
}

.nav-vertical .nav-link h6 {
  font-weight: 400;
}

.nav-vertical .nav-link p {
  margin-bottom: 0;
  color: #b5b9bf;
  font-size: 13px;
}

.rating {
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
  list-style: none;
  margin: 0;
  padding: 0;
}

.rating label {
  color: #ebebeb;
  cursor: default;
}

.rating label::before {
  margin-right: 5px;
  display: inline-block;
}

.rating label.active {
  color: #ffba00;
}

.rating-xs label::before {
  margin-right: 1px;
  font-size: 11px;
}

.rating-sm label::before {
  margin-right: 2px;
  font-size: 13px;
}

.rating-lg label::before {
  font-size: 18px;
}

.swiper-container {
  width: 100%;
  height: 100%;
}

.swiper-container .container,
.swiper-container [class^="col-"] {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.swiper-slide {
  padding-top: 20px;
  padding-bottom: 20px;
}

.swiper-container.no-padding .swiper-slide {
  padding-top: 0;
  padding-bottom: 0;
}

.swiper-slide .card-shadowed {
  margin-left: 10px;
  margin-right: 10px;
}

.swiper-button-next,
.swiper-button-prev {
  background-image: none;
  color: #b5b9bf;
  -webkit-transition: .4s;
  transition: .4s;
}

.swiper-button-next::before,
.swiper-button-prev::before {
  font-family: themify;
  font-size: 1.75rem;
  opacity: .7;
  -webkit-transition: .4s;
  transition: .4s;
}

.swiper-button-next:hover::before,
.swiper-button-prev:hover::before {
  opacity: 1;
}

.swiper-button-next::before {
  content: "\e649";
}

.swiper-button-prev::before {
  content: "\e64a";
}

.swiper-button-next {
  right: 0;
}

.swiper-button-prev {
  left: 0;
}

.swiper-button-hidden .swiper-button-next {
  right: -44px;
}

.swiper-button-hidden .swiper-button-prev {
  left: -44px;
}

.swiper-button-hidden:hover .swiper-button-next {
  right: 24px;
}

.swiper-button-hidden:hover .swiper-button-prev {
  left: 24px;
}

.swiper-button-box .swiper-button-next,
.swiper-button-box .swiper-button-prev {
  width: 44px;
  height: 64px;
  margin-top: -48px;
  line-height: 64px;
  text-align: center;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  opacity: .6;
  -webkit-transition: .4s;
  transition: .4s;
}

.swiper-button-box .swiper-button-next::before,
.swiper-button-box .swiper-button-prev::before {
  font-family: themify;
  font-size: 1.75rem;
  opacity: 1;
}

.swiper-button-box .swiper-button-next:hover,
.swiper-button-box .swiper-button-prev:hover {
  opacity: 1;
}

@media (max-width: 991px) {
  .swiper-button-box .swiper-button-next,
  .swiper-button-box .swiper-button-prev {
    width: 24px;
    height: 30px;
    margin-top: -25px;
    line-height: 30px;
  }
  .swiper-button-box .swiper-button-next::before,
  .swiper-button-box .swiper-button-prev::before {
    font-size: 1rem;
  }
}

.swiper-button-box .swiper-button-next {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.swiper-button-box .swiper-button-prev {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

.swiper-button-box.swiper-button-hidden:hover .swiper-button-next {
  right: 0;
}

.swiper-button-box.swiper-button-hidden:hover .swiper-button-prev {
  left: 0;
}

.swiper-button-circular .swiper-button-next,
.swiper-button-circular .swiper-button-prev {
  width: 40px;
  height: 40px;
  line-height: 38px;
  text-align: center;
  border: 1px solid #fff;
  border-radius: 50%;
  background-color: #fff;
  color: #788394;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.swiper-button-circular .swiper-button-next:hover,
.swiper-button-circular .swiper-button-prev:hover {
  color: #535353;
  -webkit-box-shadow: 0 0 25px rgba(0, 0, 0, 0.06);
          box-shadow: 0 0 25px rgba(0, 0, 0, 0.06);
}

.swiper-button-circular .swiper-button-next::before,
.swiper-button-circular .swiper-button-prev::before {
  font-size: 14px;
}

.swiper-button-circular .swiper-button-prev {
  left: 5%;
}

.swiper-button-circular .swiper-button-next {
  right: 5%;
}

.swiper-button-circular .swiper-button-next::before {
  content: "\e649";
}

.swiper-button-circular .swiper-button-prev::before {
  content: "\e64a";
}

.swiper-pagination-outline .swiper-pagination-bullet:not(.swiper-pagination-bullet-active) {
  border: 1px solid rgba(0, 0, 0, 0.5);
  background: transparent;
}

.swiper-pagination-bullet {
  background: #ccc;
  opacity: 1;
  width: 6px;
  height: 6px;
  -webkit-transition: .3s;
  transition: .3s;
}

.swiper-pagination-bullet:hover {
  background: #aaa;
}

.swiper-pagination-bullet::before, .swiper-pagination-bullet::after {
  position: absolute;
  left: -7px;
  display: inline-block;
  width: 20px;
  height: 13px;
  content: "";
  cursor: pointer;
}

.swiper-pagination-bullet::before {
  top: -10px;
}

.swiper-pagination-bullet::after {
  bottom: -10px;
}

.swiper-pagination-bullet-active {
  background: #777;
}

.swiper-wrapper {
  padding-bottom: 40px;
}

.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 7px;
}

.swiper-container[data-centered-slides="true"] .swiper-slide {
  opacity: .1;
  -webkit-transition: opacity 1s;
  transition: opacity 1s;
}

.swiper-container[data-centered-slides="true"] .swiper-slide-active,
.swiper-container[data-centered-slides="true"] .swiper-slide-duplicate-active {
  opacity: 1;
}

@media (max-width: 767px) {
  .swiper-button-next,
  .swiper-button-prev {
    display: none;
  }
}

.lity-close {
  font-family: "Open Sans", sans-serif;
  font-weight: 100;
  opacity: 0.6;
  padding: 0.5rem 1rem;
  width: auto;
  height: auto;
  -webkit-transition: 0.3s ease;
  transition: 0.3s ease;
}

.lity-close:hover, .lity-close:focus, .lity-close:active, .lity-close:visited {
  font-family: "Open Sans", sans-serif;
  font-weight: 100;
  opacity: 1;
  padding: 0.5rem 1rem;
}

.lity-close:active {
  top: 0;
}

.form-control {
  border-color: #e8e8e8;
  border-radius: 2px;
  color: #b5b9bf;
  padding: 5px 12px;
  font-size: 14px;
  line-height: inherit;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
}

.form-control:disabled, .form-control[readonly] {
  color: #b5b9bf;
}

.form-control[readonly] {
  background-color: #fff;
}

.form-control:focus {
  border-color: #70cef8;
  color: #535353;
}

.form-control:focus[readonly] {
  border-color: #ebebeb;
}

.form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #b7bbbd;
}

.form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #b7bbbd;
}

.form-control:-ms-input-placeholder {
  /* IE 10+ */
  color: #b7bbbd;
}

.form-control:-moz-placeholder {
  /* Firefox 18- */
  color: #b7bbbd;
}

.form-control option {
  font-weight: 300;
}

label {
  font-weight: 400;
  font-size: 13px;
  letter-spacing: .5px;
  margin-bottom: 4px;
}

label.require::after {
  content: '*';
  color: #ff4954;
  font-weight: 500;
  margin-left: 8px;
}

select.form-control:not([size]):not([multiple]) {
  height: 38px;
}

.form-control-lg,
.input-group-lg .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  line-height: 32px;
  font-size: 14px;
  padding: 7px 20px;
}

.form-control-sm,
.input-group-sm .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn {
  line-height: 20px;
  font-size: 12px;
  padding: 5px 10px;
}

.input-group-sm > .input-group-btn > select.btn:not([size]):not([multiple]),
.input-group-sm > select.form-control:not([size]):not([multiple]),
.input-group-sm > select.input-group-addon:not([size]):not([multiple]),
select.form-control-sm:not([size]):not([multiple]) {
  height: 32px;
}

.input-group-lg > .input-group-btn > select.btn:not([size]):not([multiple]),
.input-group-lg > select.form-control:not([size]):not([multiple]),
.input-group-lg > select.input-group-addon:not([size]):not([multiple]),
select.form-control-lg:not([size]):not([multiple]) {
  height: 48px;
}

textarea.form-control-lg,
textarea.form-control-sm {
  height: auto;
}

.input-group {
  border: 1px solid #ebebeb;
}

.input-group .form-control {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: none;
}

.input-group-addon {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-pack: center;
          justify-content: center;
  padding-top: 0;
  padding-bottom: 0;
  background-color: #fff;
  border: none;
  color: #b7bbbd;
  line-height: 1.5;
  font-weight: 300;
  border-radius: 2px;
}

.input-group-addon .custom-control {
  margin-bottom: 0;
}

.input-group-addon + .form-control {
  padding-left: 0;
}

.input-group-btn > .btn {
  height: 100%;
}

.input-group .form-control:not(:last-child),
.input-group-addon:not(:last-child) {
  border-right: 0;
}

.input-group .form-control:not(:first-child),
.input-group-addon:not(:first-child) {
  border-left: 0;
}

.input-group-btn:not(:first-child) > .btn,
.input-group-btn:not(:first-child) > .btn-group {
  margin-left: 0;
}

.input-group-btn:not(:last-child) > .btn,
.input-group-btn:not(:last-child) > .btn-group {
  margin-right: 0;
}

.input-group-addon:last-child,
.input-group-btn:last-child > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group-addon:first-child,
.input-group-btn:first-child > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.has-success .form-control {
  border-color: #46da60;
}

.has-success .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.has-success .checkbox,
.has-success .checkbox-inline,
.has-success .custom-control,
.has-success .form-control-feedback,
.has-success .form-control-label,
.has-success .radio,
.has-success .radio-inline,
.has-success.checkbox label,
.has-success.checkbox-inline label,
.has-success.radio label,
.has-success.radio-inline label {
  color: #46da60;
}

.has-warning .form-control {
  border-color: #ffbe00;
}

.has-warning .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.has-warning .checkbox,
.has-warning .checkbox-inline,
.has-warning .custom-control,
.has-warning .form-control-feedback,
.has-warning .form-control-label,
.has-warning .radio,
.has-warning .radio-inline,
.has-warning.checkbox label,
.has-warning.checkbox-inline label,
.has-warning.radio label,
.has-warning.radio-inline label {
  color: #ffbe00;
}

.has-danger .form-control {
  border-color: #ff4954;
}

.has-danger .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.has-danger .checkbox,
.has-danger .checkbox-inline,
.has-danger .custom-control,
.has-danger .form-control-feedback,
.has-danger .form-control-label,
.has-danger .radio,
.has-danger .radio-inline,
.has-danger.checkbox label,
.has-danger.checkbox-inline label,
.has-danger.radio label,
.has-danger.radio-inline label {
  color: #ff4954;
}

.form-group .form-control-feedback {
  display: none;
}

.form-group .form-control-feedback ul {
  margin-bottom: .5rem;
}

.form-group.has-success .form-control-feedback, .form-group.has-warning .form-control-feedback, .form-group.has-danger .form-control-feedback {
  display: block;
}

.form-round .form-control {
  border-radius: 10rem;
  padding-left: 16px;
  padding-right: 16px;
}

.form-round textarea.form-control {
  border-radius: 2px;
}

.form-round .input-group-addon {
  border-radius: 10rem;
  opacity: .7;
}

.form-round .input-group-addon + .form-control {
  padding-left: 0;
}

.form-round .input-group {
  background-color: #fff;
  border-radius: 10rem;
  border: 1px solid #ebebeb;
}

.form-round .input-group .input-group-btn .btn {
  border-radius: 10rem;
}

.form-glass .form-control {
  border: none;
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
}

.form-glass .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(255, 255, 255, 0.6);
}

.form-glass .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.6);
}

.form-glass .form-control:-ms-input-placeholder {
  /* IE 10+ */
  color: rgba(255, 255, 255, 0.6);
}

.form-glass .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(255, 255, 255, 0.6);
}

.form-glass .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #fff;
}

.form-glass select option {
  color: #37404d;
}

.form-glass label {
  opacity: .8;
}

.form-glass .input-group {
  border: none;
}

.form-glass .input-group-addon {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  color: rgba(255, 255, 255, 0.8);
}

.form-glass .input-group-btn .btn-outline {
  background-color: rgba(255, 255, 255, 0.4);
  border-color: transparent;
}

.form-glass .input-group-btn .btn-outline:hover {
  background-color: #fff;
}

.form-glass.form-round .form-control,
.form-glass.form-round .input-group-addon {
  background-color: transparent;
}

.form-glass.form-round .input-group {
  background-color: rgba(255, 255, 255, 0.2);
}

.form-transparent .form-control {
  border-color: rgba(255, 255, 255, 0.3);
  background-color: transparent;
  color: rgba(255, 255, 255, 0.8);
}

.form-transparent .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(255, 255, 255, 0.6);
}

.form-transparent .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.6);
}

.form-transparent .form-control:-ms-input-placeholder {
  /* IE 10+ */
  color: rgba(255, 255, 255, 0.6);
}

.form-transparent .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(255, 255, 255, 0.6);
}

.form-transparent .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: rgba(255, 255, 255, 0.5);
  color: #fff;
}

.form-transparent select option {
  color: #37404d;
}

.form-transparent label {
  opacity: .8;
}

.form-transparent .input-group {
  border-color: rgba(255, 255, 255, 0.3);
}

.form-transparent .input-group-addon {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.6);
}

.file-group {
  position: relative;
  overflow: hidden;
}

.file-group input[type="file"] {
  position: absolute;
  opacity: 0;
  z-index: -1;
  width: 20px;
}

.file-group-inline {
  display: inline-block;
}

[data-form="mailer"] .alert {
  display: none;
}

.form-check-input {
  outline: none;
}

.custom-control {
  color: #535353;
  font-weight: 300;
  letter-spacing: 0;
  margin-bottom: 0;
  outline: none;
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-align: center;
          align-items: center;
  padding-left: 0;
}

.custom-controls-stacked::after {
  content: "";
  display: block;
  clear: both;
}

.custom-controls-stacked .custom-control {
  display: -webkit-inline-box;
  display: inline-flex;
  margin-bottom: 6px;
}

.custom-control-input:checked ~ .custom-control-indicator,
.custom-control-input:active:not(:disabled) ~ .custom-control-indicator {
  background-image: none;
  background-color: #fcfdfe;
}

.custom-control-input:focus ~ .custom-control-indicator {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.custom-control-input:checked ~ .custom-control-indicator::before, .custom-control-input:checked ~ .custom-control-indicator::after {
  -webkit-transform: scale(1);
          transform: scale(1);
}

.custom-control-input:disabled ~ .custom-control-indicator {
  opacity: .5;
}

.custom-control-input:checked ~ .custom-control-description.strike-on-check {
  text-decoration: line-through;
  opacity: .6;
}

.custom-control-indicator {
  width: 18px;
  height: 18px;
  line-height: 18px;
  color: #fff;
  background-color: #fcfdfe;
  border: 1px solid #ebebeb;
  position: static;
  flex-shrink: 0;
  -webkit-box-flex: 0;
          flex-grow: 0;
  text-align: center;
}

.custom-control-indicator::after {
  content: '';
  display: inline-block;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s;
}

.custom-control-description {
  padding-left: 8px;
  -webkit-box-flex: 1;
          flex-grow: 1;
}

.custom-radio .custom-control-indicator::after {
  border-radius: 50%;
  vertical-align: top;
  margin-top: 4px;
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #0facf3;
}

.custom-checkbox .custom-control-indicator {
  border-radius: 0;
}

.custom-checkbox .custom-control-indicator::after {
  content: "\e64c";
  font-family: themify;
  font-size: 11px;
  font-weight: bold;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #0facf3;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-indicator::before, .custom-checkbox .custom-control-input:checked ~ .custom-control-indicator::after {
  border-radius: 0;
}

.custom-control.no-border .custom-control-indicator {
  border-color: transparent;
  background-color: transparent;
}

.custom-control.no-border .custom-control-indicator::before {
  background-color: transparent;
}

.custom-control.no-border.custom-checkbox .custom-control-indicator::after {
  font-size: 14px;
}

.custom-control.no-border.custom-radio .custom-control-indicator::after {
  margin-top: 3px;
  display: inline-block;
  width: 9px;
  height: 9px;
}

.custom-control-sm .custom-control-indicator {
  width: 14px;
  height: 14px;
  line-height: 12px;
}

.custom-control-sm.custom-radio .custom-control-indicator::after {
  margin-top: 3px;
  width: 6px;
  height: 6px;
}

.custom-control-sm.custom-radio.no-border .custom-control-indicator::after {
  margin-top: 2px;
  width: 7px;
  height: 7px;
}

.custom-control-sm.custom-checkbox .custom-control-indicator::after {
  font-size: 9px;
}

.custom-control-sm.custom-checkbox.no-border .custom-control-indicator::after {
  font-size: 11px;
}

.custom-control-lg .custom-control-indicator {
  width: 22px;
  height: 22px;
  line-height: 22px;
}

.custom-control-lg.custom-radio .custom-control-indicator::after {
  margin-top: 5px;
  width: 10px;
  height: 10px;
}

.custom-control-lg.custom-radio.no-border .custom-control-indicator::after {
  margin-top: 5px;
  width: 10px;
  height: 10px;
}

.custom-control-lg.custom-checkbox .custom-control-indicator::after {
  font-size: 14px;
}

.custom-control-lg.custom-checkbox.no-border .custom-control-indicator::after {
  font-size: 16px;
}

.feature-1 {
  text-align: center;
}

.feature-1 .feature-icon {
  font-size: 50px;
  line-height: 1;
  margin-bottom: 25px;
  color: #b5b9bf;
}

.feature-1 h5 {
  font-weight: 400;
}

.feature-2 .feature-icon {
  font-size: 35px;
  line-height: 1;
  margin-bottom: 25px;
  color: #b5b9bf;
}

.feature-2 h5, .feature-2 h6 {
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 2px;
}

.feature-3 {
  display: -webkit-box;
  display: flex;
}

.feature-3 .feature-icon {
  font-size: 40px;
  line-height: 1;
  padding-right: 30px;
  color: #b5b9bf;
}

.partner {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-pack: center;
          justify-content: center;
  text-align: center;
  flex-wrap: wrap;
}

.partner img {
  margin: 16px 24px;
  max-width: 100%;
}

.partner [class*="col-"] img {
  margin: 0;
}

.partner-sm img {
  height: 20px;
}

.portfolio-1 {
  position: relative;
  overflow: hidden;
  display: block;
}

.portfolio-1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #0facf3;
  opacity: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: 1;
}

.portfolio-1::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid #fff;
  opacity: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: 2;
}

.portfolio-1:hover::before {
  opacity: .8;
}

.portfolio-1:hover::after {
  opacity: .8;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
}

.portfolio-1:hover img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}

.portfolio-1:hover .portfolio-details {
  opacity: 1;
}

.portfolio-1 img {
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.portfolio-1 .portfolio-details {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  text-align: center;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  opacity: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  color: #fff;
  z-index: 3;
}

.portfolio-1 h5 {
  color: #fff;
  margin-bottom: 0;
}

.portfolio-1 p {
  font-size: 13px;
}

.project-details {
  list-style: none;
  padding-left: 0;
  margin-top: 20px;
}

.project-details li:not(:last-child) {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #ebebeb;
}

.project-details strong {
  display: block;
  font-weight: 300;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #b5b9bf;
}

.press-kit {
  position: relative;
  overflow: hidden;
  display: block;
  min-height: 160px;
  text-align: center;
  border: 1px solid #f1f2f3;
  border-radius: 3px;
}

.press-kit::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000000;
  opacity: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: 1;
}

.press-kit:hover::before {
  opacity: .5;
}

.press-kit:hover .asset-details {
  opacity: 1;
}

.press-kit img {
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.press-kit .asset-details {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  text-align: center;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  opacity: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  color: #fff;
  z-index: 3;
}

.press-kit h5 {
  color: #fff;
  margin-bottom: 0;
}

.press-kit p {
  font-size: 13px;
}

.shop-item {
  display: block;
  overflow: hidden;
  border: 1px solid #ebebeb;
  border-radius: 3px;
  padding: 16px 20px;
  background-color: #fcfdfe;
  color: #535353;
}

.shop-item:hover {
  background-color: #f9fafb;
  color: #535353;
}

.shop-item:hover img {
  -webkit-transform: scale(1.03);
          transform: scale(1.03);
}

.shop-item img {
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.shop-item .item-details {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
  -webkit-box-pack: justify;
          justify-content: space-between;
}

.shop-item .item-details h5 {
  font-size: 16px;
  margin-bottom: 0;
}

.shop-item .item-details p {
  font-size: 13px;
  color: #b5b9bf;
  line-height: 1.4;
}

.shop-item .item-details .item-price {
  font-family: "Dosis", "Open Sans", sans-serif;
  font-weight: 100;
  font-size: 25px;
  letter-spacing: 1px;
  padding-left: 16px;
  white-space: nowrap;
}

.shop-item .item-details .item-price .unit {
  font-size: 12px;
  vertical-align: text-top;
}

.table-cart {
  border: 1px solid #ebebeb;
  border-top: none;
}

.table-cart td {
  vertical-align: middle;
  border-top-color: #ebebeb;
  padding-left: 12px;
  padding-right: 12px;
}

.table-cart tr td:first-child {
  padding-left: 30px;
}

.table-cart tr td:last-child {
  padding-right: 30px;
  text-align: right;
}

.table-cart h5 {
  font-size: 16px;
  margin-bottom: 0;
}

.table-cart p {
  font-size: 13px;
  color: #b5b9bf;
  line-height: 1.4;
  margin-bottom: 0;
}

.table-cart img {
  max-height: 80px;
}

.table-cart label {
  font-size: 12px;
  display: inline-block;
  padding-right: 8px;
}

.table-cart input {
  max-width: 60px;
  display: inline-block;
}

.table-cart .item-remove {
  color: #b5b9bf;
}

.table-cart .item-remove:hover {
  color: #ff4954;
}

.table-cart .price {
  font-weight: 100;
}

.cart-price {
  border: 1px solid #ebebeb;
  border-radius: 3px;
  margin-bottom: 16px;
  padding: 20px;
  background-color: #fcfdfe;
}

.cart-price .flexbox p {
  margin-bottom: 4px;
}

.cart-price .flexbox strong {
  font-weight: 400;
  font-size: .9em;
}

.team-1 {
  text-align: center;
}

.team-1 img {
  border-radius: .25rem;
  margin-bottom: 2rem;
}

.team-1 h6 {
  font-size: 15px;
  letter-spacing: 1px;
}

.team-1 h6 small {
  text-transform: uppercase;
  margin-top: .5rem;
}

.team-1 small {
  display: block;
  letter-spacing: 1px;
  margin-bottom: 1rem;
}

.team-1 p {
  opacity: .9;
}

.team-2 {
  text-align: center;
}

.team-2 img {
  border-radius: 10rem;
  margin-bottom: 2rem;
}

.team-2 small {
  display: block;
  letter-spacing: 1px;
  margin-bottom: 1rem;
}

.team-3 {
  display: -webkit-box;
  display: flex;
}

.team-3 img {
  border-radius: .25rem;
}

.team-3 .team-img {
  flex-shrink: 0;
}

.team-3 .team-body {
  padding-left: 2rem;
}

.team-3 small {
  display: block;
  letter-spacing: 1px;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.team-3 p {
  opacity: .9;
}

body {
  overflow-x: hidden;
}

.topbar + .main-content {
  padding-top: 90px;
}

/*
body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
}
*/
.row.no-gap {
  margin-left: 0;
  margin-right: 0;
}

.row.no-gap > .col,
.row.no-gap > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.row.gap-1 {
  margin-left: -4px;
  margin-right: -4px;
}

.row.gap-1 > .col,
.row.gap-1 > [class*="col-"] {
  padding-left: 4px;
  padding-right: 4px;
}

.row.gap-2 {
  margin-left: -8px;
  margin-right: -8px;
}

.row.gap-2 > .col,
.row.gap-2 > [class*="col-"] {
  padding-left: 8px;
  padding-right: 8px;
}

.row.gap-3 {
  margin-left: -15px;
  margin-right: -15px;
}

.row.gap-3 > .col,
.row.gap-3 > [class*="col-"] {
  padding-left: 15px;
  padding-right: 15px;
}

.row.gap-4 {
  margin-left: -15px;
  margin-right: -15px;
}

.row.gap-4 > .col,
.row.gap-4 > [class*="col-"] {
  padding-left: 15px;
  padding-right: 15px;
}

.row.gap-5 {
  margin-left: -15px;
  margin-right: -15px;
}

.row.gap-5 > .col,
.row.gap-5 > [class*="col-"] {
  padding-left: 15px;
  padding-right: 15px;
}

.row.gap-y {
  margin-top: -15px;
  margin-bottom: -15px;
}

.row.gap-y > .col,
.row.gap-y > [class*="col-"] {
  padding-top: 15px;
  padding-bottom: 15px;
}

.row.gap-y.gap-1 {
  margin-top: -4px;
  margin-bottom: -4px;
}

.row.gap-y.gap-1 > .col,
.row.gap-y.gap-1 > [class*="col-"] {
  padding-top: 4px;
  padding-bottom: 4px;
}

.row.gap-y.gap-2 {
  margin-top: -8px;
  margin-bottom: -8px;
}

.row.gap-y.gap-2 > .col,
.row.gap-y.gap-2 > [class*="col-"] {
  padding-top: 8px;
  padding-bottom: 8px;
}

.row.gap-y.gap-3 {
  margin-top: -15px;
  margin-bottom: -15px;
}

.row.gap-y.gap-3 > .col,
.row.gap-y.gap-3 > [class*="col-"] {
  padding-top: 15px;
  padding-bottom: 15px;
}

.row.gap-y.gap-4 {
  margin-top: -15px;
  margin-bottom: -15px;
}

.row.gap-y.gap-4 > .col,
.row.gap-y.gap-4 > [class*="col-"] {
  padding-top: 15px;
  padding-bottom: 15px;
}

.row.gap-y.gap-5 {
  margin-top: -15px;
  margin-bottom: -15px;
}

.row.gap-y.gap-5 > .col,
.row.gap-y.gap-5 > [class*="col-"] {
  padding-top: 15px;
  padding-bottom: 15px;
}

.flexbox {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
          justify-content: space-between;
}

.flexbox > * {
  margin-left: 4px;
  margin-right: 4px;
}

.flexbox > *:first-child {
  margin-left: 0;
}

.flexbox > *:last-child {
  margin-right: 0;
}

.flexbox.no-gap > * {
  margin-left: 0;
  margin-right: 0;
}

ul.flexbox {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.flexbox-vertical {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  -webkit-box-pack: justify;
          justify-content: space-between;
}

.flexbox-vertical.no-gap > * {
  margin-top: 0;
  margin-bottom: 0;
}

.flex-grow-all > *,
.flex-cols-wide > *,
.flex-col-wide,
.flex-grow {
  -webkit-box-flex: 1;
          flex: 1 1 0%;
}

.flex-grow-1 {
  -webkit-box-flex: 1;
          flex-grow: 1;
}

.flex-grow-2 {
  -webkit-box-flex: 2;
          flex-grow: 2;
}

.flex-grow-3 {
  -webkit-box-flex: 3;
          flex-grow: 3;
}

.flex-grow-4 {
  -webkit-box-flex: 4;
          flex-grow: 4;
}

.flex-grow-5 {
  -webkit-box-flex: 5;
          flex-grow: 5;
}

.flex-grow-6 {
  -webkit-box-flex: 6;
          flex-grow: 6;
}

.flex-grow-7 {
  -webkit-box-flex: 7;
          flex-grow: 7;
}

.flex-grow-8 {
  -webkit-box-flex: 8;
          flex-grow: 8;
}

.flex-grow-9 {
  -webkit-box-flex: 9;
          flex-grow: 9;
}

.flex-grow-0 {
  -webkit-box-flex: 0;
          flex-grow: 0;
}

.masonry-grid {
  -webkit-column-count: 3;
     -moz-column-count: 3;
          column-count: 3;
  -webkit-column-gap: 30px;
     -moz-column-gap: 30px;
          column-gap: 30px;
}

.masonry-grid.no-gap {
  -webkit-column-gap: 0;
     -moz-column-gap: 0;
          column-gap: 0;
}

.masonry-grid.no-gap .masonry-item {
  padding-bottom: 0;
}

.masonry-grid.gap-1 {
  -webkit-column-gap: 8px;
     -moz-column-gap: 8px;
          column-gap: 8px;
}

.masonry-grid.gap-1 .masonry-item {
  padding-bottom: 8px;
}

.masonry-grid.gap-2 {
  -webkit-column-gap: 16px;
     -moz-column-gap: 16px;
          column-gap: 16px;
}

.masonry-grid.gap-2 .masonry-item {
  padding-bottom: 16px;
}

.masonry-grid.gap-3 {
  -webkit-column-gap: 30px;
     -moz-column-gap: 30px;
          column-gap: 30px;
}

.masonry-grid.gap-3 .masonry-item {
  padding-bottom: 30px;
}

.masonry-grid.gap-4 {
  -webkit-column-gap: 48px;
     -moz-column-gap: 48px;
          column-gap: 48px;
}

.masonry-grid.gap-4 .masonry-item {
  padding-bottom: 48px;
}

.masonry-grid.gap-5 {
  -webkit-column-gap: 64px;
     -moz-column-gap: 64px;
          column-gap: 64px;
}

.masonry-grid.gap-5 .masonry-item {
  padding-bottom: 64px;
}

.masonry-cols-2 {
  -webkit-column-count: 2;
     -moz-column-count: 2;
          column-count: 2;
}

.masonry-cols-3 {
  -webkit-column-count: 3;
     -moz-column-count: 3;
          column-count: 3;
}

.masonry-cols-4 {
  -webkit-column-count: 4;
     -moz-column-count: 4;
          column-count: 4;
}

.masonry-cols-5 {
  -webkit-column-count: 5;
     -moz-column-count: 5;
          column-count: 5;
}

.masonry-item {
  display: block;
  -webkit-column-break-inside: avoid;
     page-break-inside: avoid;
          break-inside: avoid;
  padding-bottom: 30px;
}

.topbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: transparent;
  height: 90px;
  border-radius: 0;
  z-index: 910;
  -webkit-transition: margin-left .5s;
  transition: margin-left .5s;
}

.body-scrolled .topbar.topbar-sticky {
  position: fixed;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.98);
  -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, 0.06);
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.06);
  -webkit-transition: margin-left .5s, background-color .5s;
  transition: margin-left .5s, background-color .5s;
}

.body-scrolled .topbar.topbar-sticky .btn-white.btn-outline {
  border-color: #f1f2f3;
  color: #b5b9bf;
}

.modal-open .topbar {
  right: 16px;
}

.topbar,
.topbar > .container,
.topbar > .container-fluid {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
          align-items: center;
}

.topbar > .container,
.topbar > .container-wide,
.topbar > .container-fluid {
  height: 90px;
}

.topbar > .container-fluid {
  width: 80%;
}

.topbar > .container-wide {
  width: 100%;
}

.topbar-toggler {
  align-self: flex-start;
  padding: .25rem .75rem;
  font-size: 1.25rem;
  line-height: 1;
  background: 0 0;
  border: 1px solid transparent;
  border-radius: .25rem;
  cursor: pointer;
  vertical-align: baseline;
  display: none;
}

.topbar-toggler:focus {
  outline: none;
}

.topbar-brand {
  float: none;
  font-size: 0.875rem;
  line-height: 1;
}

.topbar-brand img {
  max-height: 50px;
}

.topbar-brand .logo-inverse {
  display: none;
}

@media (max-width: 767px) {
  .topbar-brand {
    max-width: 150px;
  }
}

.topbar-left {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  flex-shrink: 0;
  margin-right: 16px;
}

.topbar-nav {
  display: -webkit-inline-box;
  display: inline-flex;
  flex-wrap: nowrap;
  font-weight: 600;
  font-size: .75rem;
  line-height: 90px;
  z-index: 910;
}

.topbar-nav .nav-item {
  position: relative;
  -webkit-transition: .4s;
  transition: .4s;
}

.topbar-nav .nav-item:hover > .nav-link,
.topbar-nav .nav-item.show > .nav-link {
  opacity: 1;
}

.topbar-nav .nav-item + .nav-item {
  margin-left: 1.5rem;
}

.topbar-nav .nav-link {
  color: #535353;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-weight: 600;
  opacity: .6;
  padding: 0;
  -webkit-transition: .3s;
  transition: .3s;
}

.topbar-nav .nav-link:hover, .topbar-nav .nav-link:focus, .topbar-nav .nav-link.active {
  opacity: 1;
}

.topbar-nav .nav-link.active {
  font-weight: 600;
}

.topbar-nav .nav-link i {
  margin-left: -3px;
  font-size: 10px;
  vertical-align: baseline;
}

.topbar-nav-centered .topbar-left,
.topbar-nav-centered .topbar-right {
  width: 200px;
}

.topbar-nav-centered .topbar-right {
  text-align: right;
}

.nav-submenu {
  position: absolute;
  top: 100%;
  background-color: rgba(255, 255, 255, 0.98);
  padding-left: 10px;
  line-height: 2.4;
  padding: 8px 16px;
  left: -16px;
  min-width: 150px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.05);
          box-shadow: 0 3px 9px rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translate(0, 10px);
          transform: translate(0, 10px);
  -webkit-transition: .5s;
  transition: .5s;
  z-index: 1;
}

.nav-submenu.align-right {
  left: auto;
  right: -16px;
}

.nav-submenu .nav-item + .nav-item {
  margin-left: 0;
}

.nav-submenu .nav-link {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
          justify-content: space-between;
  text-transform: none;
  font-weight: 400;
  opacity: .8;
}

.nav-submenu .nav-link.active {
  color: #0facf3;
}

.nav-submenu .nav-submenu {
  left: 100%;
  top: 0;
  -webkit-transform: translate(10px, 0);
          transform: translate(10px, 0);
}

.nav-submenu .nav-item i {
  line-height: 28px;
  margin-left: 8px;
}

.nav-submenu.cols-2 {
  display: -webkit-box;
  display: flex;
  flex-wrap: wrap;
  width: 320px;
}

.nav-submenu.cols-2 > .nav-link,
.nav-submenu.cols-2 > .nav-item {
  flex-basis: 50%;
}

.topbar-nav:not(.nav-toggle-click) .nav-item:hover > .nav-submenu,
.topbar-nav .nav-item.show > .nav-submenu {
  opacity: 1;
  visibility: visible;
}

.topbar-nav:not(.nav-toggle-click) > .nav-item:hover > .nav-submenu,
.topbar-nav > .nav-item.show > .nav-submenu {
  -webkit-transform: translate(0, -15px);
          transform: translate(0, -15px);
}

.topbar-nav:not(.nav-toggle-click) .nav-submenu > .nav-item:hover > .nav-submenu,
.topbar-nav .nav-submenu > .nav-item.show > .nav-submenu {
  -webkit-transform: translate(-15px, 0);
          transform: translate(-15px, 0);
}

.topbar.topbar-inverse {
  color: #fff;
}

.topbar.topbar-inverse .topbar-brand .logo-default {
  display: none;
}

.topbar.topbar-inverse .topbar-brand .logo-inverse {
  display: inline-block;
}

.topbar.topbar-inverse .topbar-nav > .nav-item > .nav-link,
.topbar.topbar-inverse .topbar-toggler,
.topbar.topbar-inverse .drawer-toggler {
  color: #fff;
}

.body-scrolled .topbar.topbar-inverse.topbar-sticky {
  color: #535353;
}

.body-scrolled .topbar.topbar-inverse.topbar-sticky .topbar-brand .logo-default {
  display: inline-block;
}

.body-scrolled .topbar.topbar-inverse.topbar-sticky .topbar-brand .logo-inverse {
  display: none;
}

.body-scrolled .topbar.topbar-inverse.topbar-sticky .topbar-nav > .nav-item > .nav-link,
.body-scrolled .topbar.topbar-inverse.topbar-sticky .topbar-toggler,
.body-scrolled .topbar.topbar-inverse.topbar-sticky .drawer-toggler {
  color: #535353;
}

.topbar-expand-xl.topbar-inverse .topbar-nav > .nav-item > .nav-link {
  color: #535353;
}

.topbar-expand-xl .topbar-nav {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  flex-wrap: nowrap;
  position: fixed;
  overflow: auto;
  left: -300px;
  top: 0;
  bottom: 0;
  width: 300px;
  background-color: #fff;
  margin: 0;
  padding: 30px;
  -webkit-transition: .3s;
  transition: .3s;
}

.topbar-expand-xl .topbar-nav .nav-item {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  -webkit-box-align: start;
          align-items: flex-start;
  flex-shrink: 0;
  line-height: 2.5;
  margin-bottom: 16px;
}

.topbar-expand-xl .topbar-nav .nav-item:hover .nav-submenu {
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}

.topbar-expand-xl .topbar-nav .nav-item + .nav-item {
  margin-left: 0;
}

.topbar-expand-xl .topbar-nav .nav-link {
  font-weight: 600;
  opacity: 1;
  width: 100%;
  color: #535353;
}

.topbar-expand-xl .topbar-nav .nav-link.active {
  color: #0facf3;
}

.topbar-expand-xl .topbar-nav .nav-link.active + .nav-submenu {
  display: block;
}

.topbar-expand-xl .nav-submenu {
  position: static;
  min-width: 100%;
  -webkit-box-shadow: none;
          box-shadow: none;
  opacity: .7;
  visibility: visible;
  -webkit-transform: translate(0, 0) !important;
          transform: translate(0, 0) !important;
  -webkit-transition: none;
  transition: none;
  display: none;
}

.topbar-expand-xl .nav-submenu.cols-2 {
  width: 200px;
}

.topbar-expand-xl .nav-submenu .nav-link {
  font-weight: 400;
}

.topbar-expand-xl .nav-submenu .nav-item {
  margin-bottom: 0;
}

.topbar-expand-xl .nav-submenu .nav-item i {
  float: none;
  margin-left: 4px;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}

.topbar-expand-xl .nav-link.active {
  color: #0facf3;
}

.topbar-expand-xl .topbar-toggler {
  display: inline-block;
}

@media (max-width: 1199px) {
  .topbar-expand-lg.topbar-inverse .topbar-nav > .nav-item > .nav-link {
    color: #535353;
  }
  .topbar-expand-lg .topbar-nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
    flex-wrap: nowrap;
    position: fixed;
    overflow: auto;
    left: -300px;
    top: 0;
    bottom: 0;
    width: 300px;
    background-color: #fff;
    margin: 0;
    padding: 30px;
    -webkit-transition: .3s;
    transition: .3s;
  }
  .topbar-expand-lg .topbar-nav .nav-item {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
    -webkit-box-align: start;
            align-items: flex-start;
    flex-shrink: 0;
    line-height: 2.5;
    margin-bottom: 16px;
  }
  .topbar-expand-lg .topbar-nav .nav-item:hover .nav-submenu {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
  }
  .topbar-expand-lg .topbar-nav .nav-item + .nav-item {
    margin-left: 0;
  }
  .topbar-expand-lg .topbar-nav .nav-link {
    font-weight: 600;
    opacity: 1;
    width: 100%;
    color: #535353;
  }
  .topbar-expand-lg .topbar-nav .nav-link.active {
    color: #0facf3;
  }
  .topbar-expand-lg .topbar-nav .nav-link.active + .nav-submenu {
    display: block;
  }
  .topbar-expand-lg .nav-submenu {
    position: static;
    min-width: 100%;
    -webkit-box-shadow: none;
            box-shadow: none;
    opacity: .7;
    visibility: visible;
    -webkit-transform: translate(0, 0) !important;
            transform: translate(0, 0) !important;
    -webkit-transition: none;
    transition: none;
    display: none;
  }
  .topbar-expand-lg .nav-submenu.cols-2 {
    width: 200px;
  }
  .topbar-expand-lg .nav-submenu .nav-link {
    font-weight: 400;
  }
  .topbar-expand-lg .nav-submenu .nav-item {
    margin-bottom: 0;
  }
  .topbar-expand-lg .nav-submenu .nav-item i {
    float: none;
    margin-left: 4px;
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .topbar-expand-lg .nav-link.active {
    color: #0facf3;
  }
  .topbar-expand-lg .topbar-toggler {
    display: inline-block;
  }
}

@media (max-width: 991px) {
  .topbar-expand-md.topbar-inverse .topbar-nav > .nav-item > .nav-link {
    color: #535353;
  }
  .topbar-expand-md .topbar-nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
    flex-wrap: nowrap;
    position: fixed;
    overflow: auto;
    left: -300px;
    top: 0;
    bottom: 0;
    width: 300px;
    background-color: #fff;
    margin: 0;
    padding: 30px;
    -webkit-transition: .3s;
    transition: .3s;
  }
  .topbar-expand-md .topbar-nav .nav-item {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
    -webkit-box-align: start;
            align-items: flex-start;
    flex-shrink: 0;
    line-height: 2.5;
    margin-bottom: 16px;
  }
  .topbar-expand-md .topbar-nav .nav-item:hover .nav-submenu {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
  }
  .topbar-expand-md .topbar-nav .nav-item + .nav-item {
    margin-left: 0;
  }
  .topbar-expand-md .topbar-nav .nav-link {
    font-weight: 600;
    opacity: 1;
    width: 100%;
    color: #535353;
  }
  .topbar-expand-md .topbar-nav .nav-link.active {
    color: #0facf3;
  }
  .topbar-expand-md .topbar-nav .nav-link.active + .nav-submenu {
    display: block;
  }
  .topbar-expand-md .nav-submenu {
    position: static;
    min-width: 100%;
    -webkit-box-shadow: none;
            box-shadow: none;
    opacity: .7;
    visibility: visible;
    -webkit-transform: translate(0, 0) !important;
            transform: translate(0, 0) !important;
    -webkit-transition: none;
    transition: none;
    display: none;
  }
  .topbar-expand-md .nav-submenu.cols-2 {
    width: 200px;
  }
  .topbar-expand-md .nav-submenu .nav-link {
    font-weight: 400;
  }
  .topbar-expand-md .nav-submenu .nav-item {
    margin-bottom: 0;
  }
  .topbar-expand-md .nav-submenu .nav-item i {
    float: none;
    margin-left: 4px;
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .topbar-expand-md .nav-link.active {
    color: #0facf3;
  }
  .topbar-expand-md .topbar-toggler {
    display: inline-block;
  }
}

@media (max-width: 767px) {
  .topbar-expand-sm.topbar-inverse .topbar-nav > .nav-item > .nav-link {
    color: #535353;
  }
  .topbar-expand-sm .topbar-nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
    flex-wrap: nowrap;
    position: fixed;
    overflow: auto;
    left: -300px;
    top: 0;
    bottom: 0;
    width: 300px;
    background-color: #fff;
    margin: 0;
    padding: 30px;
    -webkit-transition: .3s;
    transition: .3s;
  }
  .topbar-expand-sm .topbar-nav .nav-item {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
    -webkit-box-align: start;
            align-items: flex-start;
    flex-shrink: 0;
    line-height: 2.5;
    margin-bottom: 16px;
  }
  .topbar-expand-sm .topbar-nav .nav-item:hover .nav-submenu {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
  }
  .topbar-expand-sm .topbar-nav .nav-item + .nav-item {
    margin-left: 0;
  }
  .topbar-expand-sm .topbar-nav .nav-link {
    font-weight: 600;
    opacity: 1;
    width: 100%;
    color: #535353;
  }
  .topbar-expand-sm .topbar-nav .nav-link.active {
    color: #0facf3;
  }
  .topbar-expand-sm .topbar-nav .nav-link.active + .nav-submenu {
    display: block;
  }
  .topbar-expand-sm .nav-submenu {
    position: static;
    min-width: 100%;
    -webkit-box-shadow: none;
            box-shadow: none;
    opacity: .7;
    visibility: visible;
    -webkit-transform: translate(0, 0) !important;
            transform: translate(0, 0) !important;
    -webkit-transition: none;
    transition: none;
    display: none;
  }
  .topbar-expand-sm .nav-submenu.cols-2 {
    width: 200px;
  }
  .topbar-expand-sm .nav-submenu .nav-link {
    font-weight: 400;
  }
  .topbar-expand-sm .nav-submenu .nav-item {
    margin-bottom: 0;
  }
  .topbar-expand-sm .nav-submenu .nav-item i {
    float: none;
    margin-left: 4px;
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .topbar-expand-sm .nav-link.active {
    color: #0facf3;
  }
  .topbar-expand-sm .topbar-toggler {
    display: inline-block;
  }
}

@media (max-width: 575px) {
  .topbar-expand-xs.topbar-inverse .topbar-nav > .nav-item > .nav-link {
    color: #535353;
  }
  .topbar-expand-xs .topbar-nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
    flex-wrap: nowrap;
    position: fixed;
    overflow: auto;
    left: -300px;
    top: 0;
    bottom: 0;
    width: 300px;
    background-color: #fff;
    margin: 0;
    padding: 30px;
    -webkit-transition: .3s;
    transition: .3s;
  }
  .topbar-expand-xs .topbar-nav .nav-item {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
    -webkit-box-align: start;
            align-items: flex-start;
    flex-shrink: 0;
    line-height: 2.5;
    margin-bottom: 16px;
  }
  .topbar-expand-xs .topbar-nav .nav-item:hover .nav-submenu {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
  }
  .topbar-expand-xs .topbar-nav .nav-item + .nav-item {
    margin-left: 0;
  }
  .topbar-expand-xs .topbar-nav .nav-link {
    font-weight: 600;
    opacity: 1;
    width: 100%;
    color: #535353;
  }
  .topbar-expand-xs .topbar-nav .nav-link.active {
    color: #0facf3;
  }
  .topbar-expand-xs .topbar-nav .nav-link.active + .nav-submenu {
    display: block;
  }
  .topbar-expand-xs .nav-submenu {
    position: static;
    min-width: 100%;
    -webkit-box-shadow: none;
            box-shadow: none;
    opacity: .7;
    visibility: visible;
    -webkit-transform: translate(0, 0) !important;
            transform: translate(0, 0) !important;
    -webkit-transition: none;
    transition: none;
    display: none;
  }
  .topbar-expand-xs .nav-submenu.cols-2 {
    width: 200px;
  }
  .topbar-expand-xs .nav-submenu .nav-link {
    font-weight: 400;
  }
  .topbar-expand-xs .nav-submenu .nav-item {
    margin-bottom: 0;
  }
  .topbar-expand-xs .nav-submenu .nav-item i {
    float: none;
    margin-left: 4px;
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .topbar-expand-xs .nav-link.active {
    color: #0facf3;
  }
  .topbar-expand-xs .topbar-toggler {
    display: inline-block;
  }
}

.topbar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 909;
  cursor: pointer;
}

.topbar-backdrop::after {
  content: "\e646";
  font-family: themify;
  font-size: 20px;
  color: #b5b9bf;
  opacity: .7;
  position: absolute;
  top: 10px;
  right: 20px;
}

.topbar-reveal .topbar {
  height: 0;
}

.topbar-reveal .topbar-nav {
  left: 0;
  -webkit-box-shadow: 2px 0px 9px 2px rgba(0, 0, 0, 0.05);
          box-shadow: 2px 0px 9px 2px rgba(0, 0, 0, 0.05);
}

@media (max-width: 575px) {
  .topbar > .container {
    width: 100%;
  }
}

@media (max-width: 330px) {
  .topbar-nav {
    left: -260px;
    width: 260px !important;
  }
}

.mega-menu .nav-submenu {
  width: 560px;
  color: #535353;
  padding: 0;
  font-weight: 400;
  text-align: left;
}

@media (max-width: 991px) {
  .mega-menu .nav-submenu {
    width: 100%;
  }
  .mega-menu .nav-submenu [class*="col"] {
    max-width: 100%;
  }
}

.mm-header {
  padding: 12px 15px;
  margin-left: -15px;
  margin-right: -15px;
  border-bottom: 1px solid #f1f2f3;
  font-size: 1rem;
}

.mm-header h6 {
  margin-bottom: 0;
}

.mm-body {
  padding: 12px 0px;
  font-size: 0.8275rem;
}

.mm-footer {
  padding-top: 8px;
  padding-bottom: 8px;
  font-size: 0.8275rem;
  border-top: 1px solid #f1f2f3;
}

.header {
  position: relative;
  border-bottom: none;
  background-position: center;
  -webkit-background-size: cover;
          background-size: cover;
  background-repeat: no-repeat;
  padding: 160px 0 100px;
  z-index: 0;
  overflow: hidden;
}

.header > .container,
.header > .container-fluid,
.header > .container-wide {
  height: 100%;
}

@media (max-width: 991px) {
  .header {
    padding: 120px 0 60px;
  }
}

@media (max-width: 767px) {
  .header {
    padding: 100px 0 40px;
  }
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: .58;
}

.header[data-parallax] {
  background-color: transparent;
}

.drawer {
  position: fixed;
  top: 0;
  right: -300px;
  bottom: 0;
  width: 300px;
  overflow: auto;
  background-color: #fff;
  -webkit-box-shadow: -2px 0 20px rgba(0, 0, 0, 0.06);
          box-shadow: -2px 0 20px rgba(0, 0, 0, 0.06);
  -webkit-transition: .5s;
  transition: .5s;
  z-index: 980;
}

.drawer-content {
  padding: 30px;
}

.drawer-close {
  position: absolute;
  top: 26px;
  right: 15px;
  width: 34px;
  height: 34px;
  cursor: pointer;
  border: 1px solid #b5b9bf;
  border-radius: 50%;
  opacity: .6;
  background-color: transparent;
  -webkit-transition: .3s ease;
  transition: .3s ease;
}

.drawer-close::after {
  content: "\e646";
  font-family: themify;
  font-size: 16px;
  color: #b5b9bf;
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: center;
          justify-content: center;
}

.drawer-close:hover {
  opacity: 1;
}

.drawer-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 300px;
  bottom: 0;
  z-index: 981;
  display: none;
}

.drawer-toggler {
  border: none;
  background-color: transparent;
  cursor: pointer;
  font-size: 21px;
  line-height: 1.5;
  vertical-align: middle;
  color: #535353;
  opacity: .8;
  -webkit-transition: .3s;
  transition: .3s;
}

.drawer-toggler:hover {
  opacity: 1;
}

.drawer-open {
  position: relative;
  overflow: hidden;
  margin-left: -100px;
}

.drawer-open .topbar {
  margin-left: -100px;
}

.drawer-open .drawer {
  right: 0;
}

.drawer-open .drawer-backdrop {
  display: block;
}

.section {
  padding-top: 90px;
  padding-bottom: 90px;
  background-color: #fff;
}

@media (max-width: 991px) {
  .section {
    padding-top: 70px;
    padding-bottom: 70px;
  }
}

.section-inverse,
.header-inverse {
  background-color: #191919;
}

.section-inverse,
.section-inverse h1, .section-inverse h2, .section-inverse h3, .section-inverse h4, .section-inverse h5, .section-inverse h6,
.section-inverse .h1, .section-inverse .h2, .section-inverse .h3, .section-inverse .h4, .section-inverse .h5, .section-inverse .h6,
.header-inverse,
.header-inverse h1,
.header-inverse h2,
.header-inverse h3,
.header-inverse h4,
.header-inverse h5,
.header-inverse h6,
.header-inverse .h1,
.header-inverse .h2,
.header-inverse .h3,
.header-inverse .h4,
.header-inverse .h5,
.header-inverse .h6 {
  color: #fff;
}

.section-inverse hr,
.header-inverse hr {
  border-top-color: rgba(255, 255, 255, 0.15);
}

.section-inverse code,
.header-inverse code {
  background-color: rgba(255, 255, 255, 0.1);
  color: #eee;
}

.section-inverse .divider,
.header-inverse .divider {
  color: #fff;
}

.section-inverse .divider::before, .section-inverse .divider::after,
.header-inverse .divider::before,
.header-inverse .divider::after {
  border-top-color: rgba(255, 255, 255, 0.15);
}

.section-inverse .section-header small,
.header-inverse .section-header small {
  color: #fff;
}

.section-inverse .rating label.empty,
.header-inverse .rating label.empty {
  color: rgba(255, 255, 255, 0.3);
}

.section-inverse .card-inverse,
.header-inverse .card-inverse {
  background-color: rgba(255, 255, 255, 0.1);
}

.section-inverse .btn-outline.btn-white,
.header-inverse .btn-outline.btn-white {
  color: rgba(255, 255, 255, 0.6);
}

.section-inverse .btn-outline.btn-white.active, .section-inverse .btn-outline.btn-white:hover,
.header-inverse .btn-outline.btn-white.active,
.header-inverse .btn-outline.btn-white:hover {
  color: #535353;
}

.section-header {
  text-align: center;
  max-width: 70%;
  margin: 0 auto 70px;
}

.section-header small {
  display: inline-block;
  font-size: 0.6875rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 1rem;
  opacity: .6;
}

.section-header hr {
  width: 50px;
  margin-bottom: 1.5rem;
}

@media (max-width: 991px) {
  .section-header {
    max-width: 90%;
  }
}

.section[data-parallax] {
  background-color: transparent;
}

.parallax-slider {
  -webkit-animation: fadein 2s;
          animation: fadein 2s;
}

@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.section-dialog {
  margin-left: auto;
  margin-right: auto;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
}

@media (min-width: 576px) {
  .section-dialog {
    width: 70%;
  }
}

@media (min-width: 768px) {
  .section-dialog {
    width: 60%;
    padding: 20px 30px;
  }
}

@media (min-width: 992px) {
  .section-dialog {
    width: 45%;
    padding: 30px 40px;
  }
}

.section-inverse .section-dialog {
  background-color: #191919;
}

@media (min-width: 576px) {
  .section-dialog-sm {
    width: 65%;
  }
}

@media (min-width: 768px) {
  .section-dialog-sm {
    width: 50%;
  }
}

@media (min-width: 992px) {
  .section-dialog-sm {
    width: 35%;
  }
}

@media (min-width: 576px) {
  .section-dialog-lg {
    width: 80%;
  }
}

@media (min-width: 768px) {
  .section-dialog-lg {
    width: 70%;
  }
}

@media (min-width: 992px) {
  .section-dialog-lg {
    width: 55%;
  }
}

.section-dialog-left {
  margin-left: 5%;
}

.section-dialog-right {
  margin-right: 5%;
}

.sidebar {
  padding: 90px 20px;
}

.sidebar-sticky.stick {
  position: fixed;
  top: 20px;
}

.sidebar-title {
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-size: .8em;
  margin-bottom: 16px;
}

.site-footer {
  font-size: 0.75rem;
  border-top: 1px solid #f1f2f3;
  padding: 30px 0;
}

.site-footer p {
  margin-bottom: 0;
  font-weight: 400;
}

.b-0 {
  border: 0px solid #ebebeb;
}

.bt-0 {
  border-top: 0px solid #ebebeb;
}

.br-0 {
  border-right: 0px solid #ebebeb;
}

.bb-0 {
  border-bottom: 0px solid #ebebeb;
}

.bl-0 {
  border-left: 0px solid #ebebeb;
}

.bx-0 {
  border-right: 0px solid #ebebeb;
  border-left: 0px solid #ebebeb;
}

.by-0 {
  border-top: 0px solid #ebebeb;
  border-bottom: 0px solid #ebebeb;
}

.b-1 {
  border: 1px solid #ebebeb;
}

.bt-1 {
  border-top: 1px solid #ebebeb;
}

.br-1 {
  border-right: 1px solid #ebebeb;
}

.bb-1 {
  border-bottom: 1px solid #ebebeb;
}

.bl-1 {
  border-left: 1px solid #ebebeb;
}

.bx-1 {
  border-right: 1px solid #ebebeb;
  border-left: 1px solid #ebebeb;
}

.by-1 {
  border-top: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.b-2 {
  border: 2px solid #ebebeb;
}

.bt-2 {
  border-top: 2px solid #ebebeb;
}

.br-2 {
  border-right: 2px solid #ebebeb;
}

.bb-2 {
  border-bottom: 2px solid #ebebeb;
}

.bl-2 {
  border-left: 2px solid #ebebeb;
}

.bx-2 {
  border-right: 2px solid #ebebeb;
  border-left: 2px solid #ebebeb;
}

.by-2 {
  border-top: 2px solid #ebebeb;
  border-bottom: 2px solid #ebebeb;
}

.b-3 {
  border: 3px solid #ebebeb;
}

.bt-3 {
  border-top: 3px solid #ebebeb;
}

.br-3 {
  border-right: 3px solid #ebebeb;
}

.bb-3 {
  border-bottom: 3px solid #ebebeb;
}

.bl-3 {
  border-left: 3px solid #ebebeb;
}

.bx-3 {
  border-right: 3px solid #ebebeb;
  border-left: 3px solid #ebebeb;
}

.by-3 {
  border-top: 3px solid #ebebeb;
  border-bottom: 3px solid #ebebeb;
}

.border-primary {
  border-color: #0facf3;
}

.border-secondary {
  border-color: #e4e7ea;
}

.border-success {
  border-color: #46da60;
}

.border-info {
  border-color: #0e97ff;
}

.border-warning {
  border-color: #ffbe00;
}

.border-danger {
  border-color: #ff4954;
}

.border-dark {
  border-color: #000000;
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  border-color: #fff;
}

.border-light {
  border-color: #f1f2f3;
}

.border-fade {
  border-color: rgba(83, 83, 83, 0.07);
}

.bg-primary {
  background-color: #0facf3;
  color: #fff;
}

.bg-secondary {
  background-color: #e4e7ea;
  color: #fff;
}

.bg-success {
  background-color: #46da60;
  color: #fff;
}

.bg-info {
  background-color: #0e97ff;
  color: #fff;
}

.bg-warning {
  background-color: #ffbe00;
  color: #fff;
}

.bg-danger {
  background-color: #ff4954;
  color: #fff;
}

.bg-dark {
  background-color: #000000;
  color: #fff;
}

.bg-white {
  background-color: #fff;
}

.bg-inverse {
  background-color: #000000;
  color: #fff;
}

.bg-transparent {
  background-color: transparent;
}

.bg-secondary {
  color: #535353;
}

.bg-lightest {
  background-color: #fcfdfe;
}

.bg-lighter {
  background-color: #f9fafb;
}

.bg-light {
  background-color: #f5f6f7;
}

.bg-dark {
  background-color: #191919;
}

.bg-grey, .bg-gray {
  background-color: #f8f8f8;
}

.bg-pale-primary {
  background-color: #dcfcfa;
}

.bg-pale-secondary {
  background-color: #f7fafc;
}

.bg-pale-success {
  background-color: #e3fcf2;
}

.bg-pale-info {
  background-color: #e3f3fc;
}

.bg-pale-warning {
  background-color: #fcf0e3;
}

.bg-pale-danger {
  background-color: #fce3e3;
}

.bg-pale-dark {
  background-color: #c8c8c8;
}

.text-primary {
  color: #0facf3 !important;
}

.text-secondary {
  color: #e4e7ea !important;
}

.text-success {
  color: #46da60 !important;
}

.text-info {
  color: #0e97ff !important;
}

.text-warning {
  color: #ffbe00 !important;
}

.text-danger {
  color: #ff4954 !important;
}

.text-dark {
  color: #000000 !important;
}

.text-default {
  color: #535353 !important;
}

.text-muted {
  color: #b5b9bf !important;
}

.text-light {
  color: #788394 !important;
}

.text-lighter {
  color: #a5b3c7 !important;
}

.text-fade {
  color: rgba(83, 83, 83, 0.4) !important;
}

.text-transparent {
  color: transparent !important;
}

a.text-primary:hover, a.text-primary:focus {
  color: #0facf3 !important;
}

a.text-secondary:hover, a.text-secondary:focus {
  color: #e4e7ea !important;
}

a.text-info:hover, a.text-info:focus {
  color: #0e97ff !important;
}

a.text-success:hover, a.text-success:focus {
  color: #46da60 !important;
}

a.text-warning:hover, a.text-warning:focus {
  color: #ffbe00 !important;
}

a.text-danger:hover, a.text-danger:focus {
  color: #ff4954 !important;
}

.hover-primary:hover, .hover-primary:focus {
  color: #0facf3 !important;
}

.hover-secondary:hover, .hover-secondary:focus {
  color: #e4e7ea !important;
}

.hover-success:hover, .hover-success:focus {
  color: #46da60 !important;
}

.hover-info:hover, .hover-info:focus {
  color: #0e97ff !important;
}

.hover-warning:hover, .hover-warning:focus {
  color: #ffbe00 !important;
}

.hover-danger:hover, .hover-danger:focus {
  color: #ff4954 !important;
}

.hover-dark:hover, .hover-dark:focus {
  color: #000000 !important;
}

.hover-white:hover, .hover-white:focus {
  color: #fff !important;
}

.hover-muted:hover, .hover-muted:focus {
  color: #b5b9bf !important;
}

.hover-light:hover, .hover-light:focus {
  color: #788394 !important;
}

.hover-lighter:hover, .hover-lighter:focus {
  color: #a5b3c7 !important;
}

.hover-fade:hover, .hover-fade:focus {
  color: rgba(83, 83, 83, 0.4) !important;
}

.hover-transparent:hover, .hover-transparent:focus {
  color: transparent !important;
}

.bg-img {
  position: relative;
  border-bottom: none;
  background-position: center;
  -webkit-background-size: cover;
          background-size: cover;
  background-repeat: no-repeat;
  z-index: 0;
}

.bg-fixed {
  background-attachment: fixed;
}

@media (max-width: 640px) {
  .bg-fixed {
    background-attachment: initial;
  }
}

.bg-repeat {
  background-repeat: repeat;
  -webkit-background-size: auto auto;
          background-size: auto;
}

.bg-img-left {
  background-position: left center;
}

.bg-img-right {
  background-position: right center;
}

[data-overlay],
[data-overlay-light] {
  position: relative;
}

[data-overlay]::before,
[data-overlay-light]::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #191919;
}

[data-overlay-light]::before {
  background: #fff;
}

[data-overlay-primary]::before {
  background: #0facf3;
}

[data-overlay="1"]::before {
  opacity: 0.1;
}

[data-overlay="2"]::before {
  opacity: 0.2;
}

[data-overlay="3"]::before {
  opacity: 0.3;
}

[data-overlay="4"]::before {
  opacity: 0.4;
}

[data-overlay="5"]::before {
  opacity: 0.5;
}

[data-overlay="6"]::before {
  opacity: 0.6;
}

[data-overlay="7"]::before {
  opacity: 0.7;
}

[data-overlay="8"]::before {
  opacity: 0.8;
}

[data-overlay="9"]::before {
  opacity: 0.9;
}

[data-overlay-light="1"]::before {
  opacity: 0.1;
}

[data-overlay-light="2"]::before {
  opacity: 0.2;
}

[data-overlay-light="3"]::before {
  opacity: 0.3;
}

[data-overlay-light="4"]::before {
  opacity: 0.4;
}

[data-overlay-light="5"]::before {
  opacity: 0.5;
}

[data-overlay-light="6"]::before {
  opacity: 0.6;
}

[data-overlay-light="7"]::before {
  opacity: 0.7;
}

[data-overlay-light="8"]::before {
  opacity: 0.8;
}

[data-overlay-light="9"]::before {
  opacity: 0.9;
}

.d-none {
  display: none;
}

.bring-front {
  z-index: 1;
}

.transition-3s {
  -webkit-transition: .3s;
  transition: .3s;
}

.transition-5s {
  -webkit-transition: .5s;
  transition: .5s;
}

.center-v {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  -webkit-box-pack: center;
          justify-content: center;
  height: 100%;
}

.center-h {
  margin: 0 auto;
}

.center-vh {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: center;
          justify-content: center;
  -webkit-box-align: center;
          align-items: center;
  height: 100%;
}

.rotate-45 {
  display: inline-block;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}

.rotate-90 {
  display: inline-block;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}

.rotate-180 {
  display: inline-block;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.opacity-0 {
  opacity: 0;
}

.opacity-5 {
  opacity: 0.05;
}

.opacity-10 {
  opacity: 0.1;
}

.opacity-15 {
  opacity: 0.15;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-30 {
  opacity: 0.3;
}

.opacity-35 {
  opacity: 0.35;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-45 {
  opacity: 0.45;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-55 {
  opacity: 0.55;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-65 {
  opacity: 0.65;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-80 {
  opacity: 0.8;
}

.opacity-85 {
  opacity: 0.85;
}

.opacity-90 {
  opacity: 0.9;
}

.opacity-95 {
  opacity: 0.95;
}

.opacity-100 {
  opacity: 1;
}

.cursor-default {
  cursor: default;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-text {
  cursor: text;
}

.shadow-1 {
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
}

.shadow-2, .portfolio-2 img, .shop-item:hover {
  -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, 0.06);
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.06);
}

.shadow-3 {
  -webkit-box-shadow: 0 0 25px rgba(0, 0, 0, 0.08);
          box-shadow: 0 0 25px rgba(0, 0, 0, 0.08);
}

.shadow-4 {
  -webkit-box-shadow: 0 0 35px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 35px rgba(0, 0, 0, 0.1);
}

.shadow-5 {
  -webkit-box-shadow: 0 0 45px rgba(0, 0, 0, 0.12);
          box-shadow: 0 0 45px rgba(0, 0, 0, 0.12);
}

.hover-shadow-1,
.hover-shadow-2,
.hover-shadow-3,
.hover-shadow-4,
.hover-shadow-5,
.portfolio-2 img {
  -webkit-transition: -webkit-box-shadow .7s;
  transition: -webkit-box-shadow .7s;
  transition: box-shadow .7s;
  transition: box-shadow .7s, -webkit-box-shadow .7s;
}

.hover-shadow-1:hover {
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
}

.hover-shadow-2:hover {
  -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, 0.06);
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.06);
}

.hover-shadow-3:hover {
  -webkit-box-shadow: 0 0 25px rgba(0, 0, 0, 0.08);
          box-shadow: 0 0 25px rgba(0, 0, 0, 0.08);
}

.hover-shadow-4:hover {
  -webkit-box-shadow: 0 0 35px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 35px rgba(0, 0, 0, 0.1);
}

.hover-shadow-5:hover, .portfolio-2 img:hover {
  -webkit-box-shadow: 0 0 45px rgba(0, 0, 0, 0.12);
          box-shadow: 0 0 45px rgba(0, 0, 0, 0.12);
}

.shadow-material-1 {
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.shadow-material-2 {
  -webkit-box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.shadow-material-3 {
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}

.shadow-material-4 {
  -webkit-box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
          box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
}

.shadow-material-5 {
  -webkit-box-shadow: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22);
          box-shadow: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22);
}

.shadow-0,
.shadow-material-0 {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.text-hue-rotate {
  color: #f35626;
  background-image: -webkit-linear-gradient(92deg, #f35626, #feab3a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  -webkit-animation: hue-rotate 60s infinite linear;
}

.bg-hue-rotate {
  -webkit-animation: hue-rotate 30s linear infinite;
}

@-webkit-keyframes hue {
  from {
    -webkit-filter: hue-rotate(0deg);
  }
  to {
    -webkit-filter: hue-rotate(-360deg);
  }
}

.overflow-hidden {
  overflow: hidden;
}

.no-shadow {
  shadow: none;
}

.no-text-shadow {
  text-shadow: none;
}

.no-scroll {
  overflow: hidden;
}

.no-radius {
  border-radius: 0;
}

.no-shrink {
  flex-shrink: 0;
}

.no-grow {
  -webkit-box-flex: 0;
          flex-grow: 0;
}

.no-letter-spacing {
  letter-spacing: 0;
}

.no-underline:hover, .no-underline:focus {
  text-decoration: none;
}

.w-0 {
  width: 0px !important;
}

.h-0 {
  height: 0px !important;
}

.w-10 {
  width: 10px !important;
}

.h-10 {
  height: 10px !important;
}

.w-20 {
  width: 20px !important;
}

.h-20 {
  height: 20px !important;
}

.w-25 {
  width: 25px !important;
}

.h-25 {
  height: 25px !important;
}

.w-30 {
  width: 30px !important;
}

.h-30 {
  height: 30px !important;
}

.w-40 {
  width: 40px !important;
}

.h-40 {
  height: 40px !important;
}

.w-50 {
  width: 50px !important;
}

.h-50 {
  height: 50px !important;
}

.w-60 {
  width: 60px !important;
}

.h-60 {
  height: 60px !important;
}

.w-64 {
  width: 64px !important;
}

.h-64 {
  height: 64px !important;
}

.w-70 {
  width: 70px !important;
}

.h-70 {
  height: 70px !important;
}

.w-75 {
  width: 75px !important;
}

.h-75 {
  height: 75px !important;
}

.w-80 {
  width: 80px !important;
}

.h-80 {
  height: 80px !important;
}

.w-90 {
  width: 90px !important;
}

.h-90 {
  height: 90px !important;
}

.w-100 {
  width: 100px !important;
}

.h-100 {
  height: 100px !important;
}

.w-120 {
  width: 120px !important;
}

.h-120 {
  height: 120px !important;
}

.w-140 {
  width: 140px !important;
}

.h-140 {
  height: 140px !important;
}

.w-150 {
  width: 150px !important;
}

.h-150 {
  height: 150px !important;
}

.w-160 {
  width: 160px !important;
}

.h-160 {
  height: 160px !important;
}

.w-180 {
  width: 180px !important;
}

.h-180 {
  height: 180px !important;
}

.w-200 {
  width: 200px !important;
}

.h-200 {
  height: 200px !important;
}

.w-250 {
  width: 250px !important;
}

.h-250 {
  height: 250px !important;
}

.w-300 {
  width: 300px !important;
}

.h-300 {
  height: 300px !important;
}

.w-350 {
  width: 350px !important;
}

.h-350 {
  height: 350px !important;
}

.w-400 {
  width: 400px !important;
}

.h-400 {
  height: 400px !important;
}

.w-450 {
  width: 450px !important;
}

.h-450 {
  height: 450px !important;
}

.w-500 {
  width: 500px !important;
}

.h-500 {
  height: 500px !important;
}

.w-600 {
  width: 600px !important;
}

.h-600 {
  height: 600px !important;
}

.w-700 {
  width: 700px !important;
}

.h-700 {
  height: 700px !important;
}

.w-800 {
  width: 800px !important;
}

.h-800 {
  height: 800px !important;
}

.w-900 {
  width: 900px !important;
}

.h-900 {
  height: 900px !important;
}

.w-full {
  width: 100%;
}

.w-half {
  width: 50%;
}

.w-third {
  width: 33.333333%;
}

.w-fourth {
  width: 25%;
}

.w-fifth {
  width: 20%;
}

.h-full {
  height: 100%;
}

.h-half {
  height: 50%;
}

.h-third {
  height: 33.333333%;
}

.h-fourth {
  height: 25%;
}

.h-fifth {
  height: 20%;
}

.w-fullscreen {
  width: 100vw;
}

.h-fullscreen {
  height: 100vh;
}

.mw-fullscreen {
  min-width: 100vw;
}

.mh-fullscreen {
  min-height: 100vh;
}

.m-4 {
  margin: 4px !important;
}

.mt-4 {
  margin-top: 4px !important;
}

.mr-4 {
  margin-right: 4px !important;
}

.mb-4 {
  margin-bottom: 4px !important;
}

.ml-4 {
  margin-left: 4px !important;
}

.mx-4 {
  margin-right: 4px !important;
  margin-left: 4px !important;
}

.my-4 {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}

.m-5 {
  margin: 5px !important;
}

.mt-5 {
  margin-top: 5px !important;
}

.mr-5 {
  margin-right: 5px !important;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.ml-5 {
  margin-left: 5px !important;
}

.mx-5 {
  margin-right: 5px !important;
  margin-left: 5px !important;
}

.my-5 {
  margin-top: 5px !important;
  margin-bottom: 5px !important;
}

.m-8 {
  margin: 8px !important;
}

.mt-8 {
  margin-top: 8px !important;
}

.mr-8 {
  margin-right: 8px !important;
}

.mb-8 {
  margin-bottom: 8px !important;
}

.ml-8 {
  margin-left: 8px !important;
}

.mx-8 {
  margin-right: 8px !important;
  margin-left: 8px !important;
}

.my-8 {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

.m-10 {
  margin: 10px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.mx-10 {
  margin-right: 10px !important;
  margin-left: 10px !important;
}

.my-10 {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

.m-12 {
  margin: 12px !important;
}

.mt-12 {
  margin-top: 12px !important;
}

.mr-12 {
  margin-right: 12px !important;
}

.mb-12 {
  margin-bottom: 12px !important;
}

.ml-12 {
  margin-left: 12px !important;
}

.mx-12 {
  margin-right: 12px !important;
  margin-left: 12px !important;
}

.my-12 {
  margin-top: 12px !important;
  margin-bottom: 12px !important;
}

.m-15 {
  margin: 15px !important;
}

.mt-15 {
  margin-top: 15px !important;
}

.mr-15 {
  margin-right: 15px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}

.ml-15 {
  margin-left: 15px !important;
}

.mx-15 {
  margin-right: 15px !important;
  margin-left: 15px !important;
}

.my-15 {
  margin-top: 15px !important;
  margin-bottom: 15px !important;
}

.m-16 {
  margin: 16px !important;
}

.mt-16 {
  margin-top: 16px !important;
}

.mr-16 {
  margin-right: 16px !important;
}

.mb-16 {
  margin-bottom: 16px !important;
}

.ml-16 {
  margin-left: 16px !important;
}

.mx-16 {
  margin-right: 16px !important;
  margin-left: 16px !important;
}

.my-16 {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
}

.m-20 {
  margin: 20px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mr-20 {
  margin-right: 20px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.ml-20 {
  margin-left: 20px !important;
}

.mx-20 {
  margin-right: 20px !important;
  margin-left: 20px !important;
}

.my-20 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

.m-24 {
  margin: 24px !important;
}

.mt-24 {
  margin-top: 24px !important;
}

.mr-24 {
  margin-right: 24px !important;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.ml-24 {
  margin-left: 24px !important;
}

.mx-24 {
  margin-right: 24px !important;
  margin-left: 24px !important;
}

.my-24 {
  margin-top: 24px !important;
  margin-bottom: 24px !important;
}

.m-25 {
  margin: 25px !important;
}

.mt-25 {
  margin-top: 25px !important;
}

.mr-25 {
  margin-right: 25px !important;
}

.mb-25 {
  margin-bottom: 25px !important;
}

.ml-25 {
  margin-left: 25px !important;
}

.mx-25 {
  margin-right: 25px !important;
  margin-left: 25px !important;
}

.my-25 {
  margin-top: 25px !important;
  margin-bottom: 25px !important;
}

.m-30 {
  margin: 30px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mr-30 {
  margin-right: 30px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.ml-30 {
  margin-left: 30px !important;
}

.mx-30 {
  margin-right: 30px !important;
  margin-left: 30px !important;
}

.my-30 {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
}

.m-35 {
  margin: 35px !important;
}

.mt-35 {
  margin-top: 35px !important;
}

.mr-35 {
  margin-right: 35px !important;
}

.mb-35 {
  margin-bottom: 35px !important;
}

.ml-35 {
  margin-left: 35px !important;
}

.mx-35 {
  margin-right: 35px !important;
  margin-left: 35px !important;
}

.my-35 {
  margin-top: 35px !important;
  margin-bottom: 35px !important;
}

.m-40 {
  margin: 40px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mr-40 {
  margin-right: 40px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.ml-40 {
  margin-left: 40px !important;
}

.mx-40 {
  margin-right: 40px !important;
  margin-left: 40px !important;
}

.my-40 {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

.m-45 {
  margin: 45px !important;
}

.mt-45 {
  margin-top: 45px !important;
}

.mr-45 {
  margin-right: 45px !important;
}

.mb-45 {
  margin-bottom: 45px !important;
}

.ml-45 {
  margin-left: 45px !important;
}

.mx-45 {
  margin-right: 45px !important;
  margin-left: 45px !important;
}

.my-45 {
  margin-top: 45px !important;
  margin-bottom: 45px !important;
}

.m-50 {
  margin: 50px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mr-50 {
  margin-right: 50px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.ml-50 {
  margin-left: 50px !important;
}

.mx-50 {
  margin-right: 50px !important;
  margin-left: 50px !important;
}

.my-50 {
  margin-top: 50px !important;
  margin-bottom: 50px !important;
}

.m-60 {
  margin: 60px !important;
}

.mt-60 {
  margin-top: 60px !important;
}

.mr-60 {
  margin-right: 60px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.ml-60 {
  margin-left: 60px !important;
}

.mx-60 {
  margin-right: 60px !important;
  margin-left: 60px !important;
}

.my-60 {
  margin-top: 60px !important;
  margin-bottom: 60px !important;
}

.m-70 {
  margin: 70px !important;
}

.mt-70 {
  margin-top: 70px !important;
}

.mr-70 {
  margin-right: 70px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.ml-70 {
  margin-left: 70px !important;
}

.mx-70 {
  margin-right: 70px !important;
  margin-left: 70px !important;
}

.my-70 {
  margin-top: 70px !important;
  margin-bottom: 70px !important;
}

.m-80 {
  margin: 80px !important;
}

.mt-80 {
  margin-top: 80px !important;
}

.mr-80 {
  margin-right: 80px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.ml-80 {
  margin-left: 80px !important;
}

.mx-80 {
  margin-right: 80px !important;
  margin-left: 80px !important;
}

.my-80 {
  margin-top: 80px !important;
  margin-bottom: 80px !important;
}

.m-90 {
  margin: 90px !important;
}

.mt-90 {
  margin-top: 90px !important;
}

.mr-90 {
  margin-right: 90px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.ml-90 {
  margin-left: 90px !important;
}

.mx-90 {
  margin-right: 90px !important;
  margin-left: 90px !important;
}

.my-90 {
  margin-top: 90px !important;
  margin-bottom: 90px !important;
}

.m-100 {
  margin: 100px !important;
}

.mt-100 {
  margin-top: 100px !important;
}

.mr-100 {
  margin-right: 100px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.ml-100 {
  margin-left: 100px !important;
}

.mx-100 {
  margin-right: 100px !important;
  margin-left: 100px !important;
}

.my-100 {
  margin-top: 100px !important;
  margin-bottom: 100px !important;
}

.m-120 {
  margin: 120px !important;
}

.mt-120 {
  margin-top: 120px !important;
}

.mr-120 {
  margin-right: 120px !important;
}

.mb-120 {
  margin-bottom: 120px !important;
}

.ml-120 {
  margin-left: 120px !important;
}

.mx-120 {
  margin-right: 120px !important;
  margin-left: 120px !important;
}

.my-120 {
  margin-top: 120px !important;
  margin-bottom: 120px !important;
}

.m-140 {
  margin: 140px !important;
}

.mt-140 {
  margin-top: 140px !important;
}

.mr-140 {
  margin-right: 140px !important;
}

.mb-140 {
  margin-bottom: 140px !important;
}

.ml-140 {
  margin-left: 140px !important;
}

.mx-140 {
  margin-right: 140px !important;
  margin-left: 140px !important;
}

.my-140 {
  margin-top: 140px !important;
  margin-bottom: 140px !important;
}

.m-150 {
  margin: 150px !important;
}

.mt-150 {
  margin-top: 150px !important;
}

.mr-150 {
  margin-right: 150px !important;
}

.mb-150 {
  margin-bottom: 150px !important;
}

.ml-150 {
  margin-left: 150px !important;
}

.mx-150 {
  margin-right: 150px !important;
  margin-left: 150px !important;
}

.my-150 {
  margin-top: 150px !important;
  margin-bottom: 150px !important;
}

.m-160 {
  margin: 160px !important;
}

.mt-160 {
  margin-top: 160px !important;
}

.mr-160 {
  margin-right: 160px !important;
}

.mb-160 {
  margin-bottom: 160px !important;
}

.ml-160 {
  margin-left: 160px !important;
}

.mx-160 {
  margin-right: 160px !important;
  margin-left: 160px !important;
}

.my-160 {
  margin-top: 160px !important;
  margin-bottom: 160px !important;
}

.m-180 {
  margin: 180px !important;
}

.mt-180 {
  margin-top: 180px !important;
}

.mr-180 {
  margin-right: 180px !important;
}

.mb-180 {
  margin-bottom: 180px !important;
}

.ml-180 {
  margin-left: 180px !important;
}

.mx-180 {
  margin-right: 180px !important;
  margin-left: 180px !important;
}

.my-180 {
  margin-top: 180px !important;
  margin-bottom: 180px !important;
}

.m-200 {
  margin: 200px !important;
}

.mt-200 {
  margin-top: 200px !important;
}

.mr-200 {
  margin-right: 200px !important;
}

.mb-200 {
  margin-bottom: 200px !important;
}

.ml-200 {
  margin-left: 200px !important;
}

.mx-200 {
  margin-right: 200px !important;
  margin-left: 200px !important;
}

.my-200 {
  margin-top: 200px !important;
  margin-bottom: 200px !important;
}

.m-250 {
  margin: 250px !important;
}

.mt-250 {
  margin-top: 250px !important;
}

.mr-250 {
  margin-right: 250px !important;
}

.mb-250 {
  margin-bottom: 250px !important;
}

.ml-250 {
  margin-left: 250px !important;
}

.mx-250 {
  margin-right: 250px !important;
  margin-left: 250px !important;
}

.my-250 {
  margin-top: 250px !important;
  margin-bottom: 250px !important;
}

.m-300 {
  margin: 300px !important;
}

.mt-300 {
  margin-top: 300px !important;
}

.mr-300 {
  margin-right: 300px !important;
}

.mb-300 {
  margin-bottom: 300px !important;
}

.ml-300 {
  margin-left: 300px !important;
}

.mx-300 {
  margin-right: 300px !important;
  margin-left: 300px !important;
}

.my-300 {
  margin-top: 300px !important;
  margin-bottom: 300px !important;
}

.p-4 {
  padding: 4px !important;
}

.pt-4 {
  padding-top: 4px !important;
}

.pr-4 {
  padding-right: 4px !important;
}

.pb-4 {
  padding-bottom: 4px !important;
}

.pl-4 {
  padding-left: 4px !important;
}

.px-4 {
  padding-right: 4px !important;
  padding-left: 4px !important;
}

.py-4 {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

.p-5 {
  padding: 5px !important;
}

.pt-5 {
  padding-top: 5px !important;
}

.pr-5 {
  padding-right: 5px !important;
}

.pb-5 {
  padding-bottom: 5px !important;
}

.pl-5 {
  padding-left: 5px !important;
}

.px-5 {
  padding-right: 5px !important;
  padding-left: 5px !important;
}

.py-5 {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

.p-8 {
  padding: 8px !important;
}

.pt-8 {
  padding-top: 8px !important;
}

.pr-8 {
  padding-right: 8px !important;
}

.pb-8 {
  padding-bottom: 8px !important;
}

.pl-8 {
  padding-left: 8px !important;
}

.px-8 {
  padding-right: 8px !important;
  padding-left: 8px !important;
}

.py-8 {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

.p-10 {
  padding: 10px !important;
}

.pt-10 {
  padding-top: 10px !important;
}

.pr-10 {
  padding-right: 10px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.pl-10 {
  padding-left: 10px !important;
}

.px-10 {
  padding-right: 10px !important;
  padding-left: 10px !important;
}

.py-10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.p-12 {
  padding: 12px !important;
}

.pt-12 {
  padding-top: 12px !important;
}

.pr-12 {
  padding-right: 12px !important;
}

.pb-12 {
  padding-bottom: 12px !important;
}

.pl-12 {
  padding-left: 12px !important;
}

.px-12 {
  padding-right: 12px !important;
  padding-left: 12px !important;
}

.py-12 {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.p-15 {
  padding: 15px !important;
}

.pt-15 {
  padding-top: 15px !important;
}

.pr-15 {
  padding-right: 15px !important;
}

.pb-15 {
  padding-bottom: 15px !important;
}

.pl-15 {
  padding-left: 15px !important;
}

.px-15 {
  padding-right: 15px !important;
  padding-left: 15px !important;
}

.py-15 {
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}

.p-16 {
  padding: 16px !important;
}

.pt-16 {
  padding-top: 16px !important;
}

.pr-16 {
  padding-right: 16px !important;
}

.pb-16 {
  padding-bottom: 16px !important;
}

.pl-16 {
  padding-left: 16px !important;
}

.px-16 {
  padding-right: 16px !important;
  padding-left: 16px !important;
}

.py-16 {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

.p-20 {
  padding: 20px !important;
}

.pt-20 {
  padding-top: 20px !important;
}

.pr-20 {
  padding-right: 20px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.pl-20 {
  padding-left: 20px !important;
}

.px-20 {
  padding-right: 20px !important;
  padding-left: 20px !important;
}

.py-20 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.p-24 {
  padding: 24px !important;
}

.pt-24 {
  padding-top: 24px !important;
}

.pr-24 {
  padding-right: 24px !important;
}

.pb-24 {
  padding-bottom: 24px !important;
}

.pl-24 {
  padding-left: 24px !important;
}

.px-24 {
  padding-right: 24px !important;
  padding-left: 24px !important;
}

.py-24 {
  padding-top: 24px !important;
  padding-bottom: 24px !important;
}

.p-25 {
  padding: 25px !important;
}

.pt-25 {
  padding-top: 25px !important;
}

.pr-25 {
  padding-right: 25px !important;
}

.pb-25 {
  padding-bottom: 25px !important;
}

.pl-25 {
  padding-left: 25px !important;
}

.px-25 {
  padding-right: 25px !important;
  padding-left: 25px !important;
}

.py-25 {
  padding-top: 25px !important;
  padding-bottom: 25px !important;
}

.p-30 {
  padding: 30px !important;
}

.pt-30 {
  padding-top: 30px !important;
}

.pr-30 {
  padding-right: 30px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.pl-30 {
  padding-left: 30px !important;
}

.px-30 {
  padding-right: 30px !important;
  padding-left: 30px !important;
}

.py-30 {
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.p-35 {
  padding: 35px !important;
}

.pt-35 {
  padding-top: 35px !important;
}

.pr-35 {
  padding-right: 35px !important;
}

.pb-35 {
  padding-bottom: 35px !important;
}

.pl-35 {
  padding-left: 35px !important;
}

.px-35 {
  padding-right: 35px !important;
  padding-left: 35px !important;
}

.py-35 {
  padding-top: 35px !important;
  padding-bottom: 35px !important;
}

.p-40 {
  padding: 40px !important;
}

.pt-40 {
  padding-top: 40px !important;
}

.pr-40 {
  padding-right: 40px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.pl-40 {
  padding-left: 40px !important;
}

.px-40 {
  padding-right: 40px !important;
  padding-left: 40px !important;
}

.py-40 {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.p-45 {
  padding: 45px !important;
}

.pt-45 {
  padding-top: 45px !important;
}

.pr-45 {
  padding-right: 45px !important;
}

.pb-45 {
  padding-bottom: 45px !important;
}

.pl-45 {
  padding-left: 45px !important;
}

.px-45 {
  padding-right: 45px !important;
  padding-left: 45px !important;
}

.py-45 {
  padding-top: 45px !important;
  padding-bottom: 45px !important;
}

.p-50 {
  padding: 50px !important;
}

.pt-50 {
  padding-top: 50px !important;
}

.pr-50 {
  padding-right: 50px !important;
}

.pb-50 {
  padding-bottom: 50px !important;
}

.pl-50 {
  padding-left: 50px !important;
}

.px-50 {
  padding-right: 50px !important;
  padding-left: 50px !important;
}

.py-50 {
  padding-top: 50px !important;
  padding-bottom: 50px !important;
}

.p-60 {
  padding: 60px !important;
}

.pt-60 {
  padding-top: 60px !important;
}

.pr-60 {
  padding-right: 60px !important;
}

.pb-60 {
  padding-bottom: 60px !important;
}

.pl-60 {
  padding-left: 60px !important;
}

.px-60 {
  padding-right: 60px !important;
  padding-left: 60px !important;
}

.py-60 {
  padding-top: 60px !important;
  padding-bottom: 60px !important;
}

.p-70 {
  padding: 70px !important;
}

.pt-70 {
  padding-top: 70px !important;
}

.pr-70 {
  padding-right: 70px !important;
}

.pb-70 {
  padding-bottom: 70px !important;
}

.pl-70 {
  padding-left: 70px !important;
}

.px-70 {
  padding-right: 70px !important;
  padding-left: 70px !important;
}

.py-70 {
  padding-top: 70px !important;
  padding-bottom: 70px !important;
}

.p-80 {
  padding: 80px !important;
}

.pt-80 {
  padding-top: 80px !important;
}

.pr-80 {
  padding-right: 80px !important;
}

.pb-80 {
  padding-bottom: 80px !important;
}

.pl-80 {
  padding-left: 80px !important;
}

.px-80 {
  padding-right: 80px !important;
  padding-left: 80px !important;
}

.py-80 {
  padding-top: 80px !important;
  padding-bottom: 80px !important;
}

.p-90 {
  padding: 90px !important;
}

.pt-90 {
  padding-top: 90px !important;
}

.pr-90 {
  padding-right: 90px !important;
}

.pb-90 {
  padding-bottom: 90px !important;
}

.pl-90 {
  padding-left: 90px !important;
}

.px-90 {
  padding-right: 90px !important;
  padding-left: 90px !important;
}

.py-90 {
  padding-top: 90px !important;
  padding-bottom: 90px !important;
}

.p-100 {
  padding: 100px !important;
}

.pt-100 {
  padding-top: 100px !important;
}

.pr-100 {
  padding-right: 100px !important;
}

.pb-100 {
  padding-bottom: 100px !important;
}

.pl-100 {
  padding-left: 100px !important;
}

.px-100 {
  padding-right: 100px !important;
  padding-left: 100px !important;
}

.py-100 {
  padding-top: 100px !important;
  padding-bottom: 100px !important;
}

.p-120 {
  padding: 120px !important;
}

.pt-120 {
  padding-top: 120px !important;
}

.pr-120 {
  padding-right: 120px !important;
}

.pb-120 {
  padding-bottom: 120px !important;
}

.pl-120 {
  padding-left: 120px !important;
}

.px-120 {
  padding-right: 120px !important;
  padding-left: 120px !important;
}

.py-120 {
  padding-top: 120px !important;
  padding-bottom: 120px !important;
}

.p-140 {
  padding: 140px !important;
}

.pt-140 {
  padding-top: 140px !important;
}

.pr-140 {
  padding-right: 140px !important;
}

.pb-140 {
  padding-bottom: 140px !important;
}

.pl-140 {
  padding-left: 140px !important;
}

.px-140 {
  padding-right: 140px !important;
  padding-left: 140px !important;
}

.py-140 {
  padding-top: 140px !important;
  padding-bottom: 140px !important;
}

.p-150 {
  padding: 150px !important;
}

.pt-150 {
  padding-top: 150px !important;
}

.pr-150 {
  padding-right: 150px !important;
}

.pb-150 {
  padding-bottom: 150px !important;
}

.pl-150 {
  padding-left: 150px !important;
}

.px-150 {
  padding-right: 150px !important;
  padding-left: 150px !important;
}

.py-150 {
  padding-top: 150px !important;
  padding-bottom: 150px !important;
}

.p-160 {
  padding: 160px !important;
}

.pt-160 {
  padding-top: 160px !important;
}

.pr-160 {
  padding-right: 160px !important;
}

.pb-160 {
  padding-bottom: 160px !important;
}

.pl-160 {
  padding-left: 160px !important;
}

.px-160 {
  padding-right: 160px !important;
  padding-left: 160px !important;
}

.py-160 {
  padding-top: 160px !important;
  padding-bottom: 160px !important;
}

.p-180 {
  padding: 180px !important;
}

.pt-180 {
  padding-top: 180px !important;
}

.pr-180 {
  padding-right: 180px !important;
}

.pb-180 {
  padding-bottom: 180px !important;
}

.pl-180 {
  padding-left: 180px !important;
}

.px-180 {
  padding-right: 180px !important;
  padding-left: 180px !important;
}

.py-180 {
  padding-top: 180px !important;
  padding-bottom: 180px !important;
}

.p-200 {
  padding: 200px !important;
}

.pt-200 {
  padding-top: 200px !important;
}

.pr-200 {
  padding-right: 200px !important;
}

.pb-200 {
  padding-bottom: 200px !important;
}

.pl-200 {
  padding-left: 200px !important;
}

.px-200 {
  padding-right: 200px !important;
  padding-left: 200px !important;
}

.py-200 {
  padding-top: 200px !important;
  padding-bottom: 200px !important;
}

.p-250 {
  padding: 250px !important;
}

.pt-250 {
  padding-top: 250px !important;
}

.pr-250 {
  padding-right: 250px !important;
}

.pb-250 {
  padding-bottom: 250px !important;
}

.pl-250 {
  padding-left: 250px !important;
}

.px-250 {
  padding-right: 250px !important;
  padding-left: 250px !important;
}

.py-250 {
  padding-top: 250px !important;
  padding-bottom: 250px !important;
}

.p-300 {
  padding: 300px !important;
}

.pt-300 {
  padding-top: 300px !important;
}

.pr-300 {
  padding-right: 300px !important;
}

.pb-300 {
  padding-bottom: 300px !important;
}

.pl-300 {
  padding-left: 300px !important;
}

.px-300 {
  padding-right: 300px !important;
  padding-left: 300px !important;
}

.py-300 {
  padding-top: 300px !important;
  padding-bottom: 300px !important;
}

.gap-items > *,
.gap-items-3 > * {
  margin-left: 8px;
  margin-right: 8px;
}

.gap-items > *:first-child,
.gap-items-3 > *:first-child {
  margin-left: 0;
}

.gap-items > *:last-child,
.gap-items-3 > *:last-child {
  margin-right: 0;
}

.gap-items-1 > * {
  margin-left: 2px;
  margin-right: 2px;
}

.gap-items-1 > *:first-child {
  margin-left: 0;
}

.gap-items-1 > *:last-child {
  margin-right: 0;
}

.gap-items-2 > * {
  margin-left: 4px;
  margin-right: 4px;
}

.gap-items-2 > *:first-child {
  margin-left: 0;
}

.gap-items-2 > *:last-child {
  margin-right: 0;
}

.gap-items-4 > * {
  margin-left: 12px;
  margin-right: 12px;
}

.gap-items-4 > *:first-child {
  margin-left: 0;
}

.gap-items-4 > *:last-child {
  margin-right: 0;
}

.gap-items-5 > * {
  margin-left: 16px;
  margin-right: 16px;
}

.gap-items-5 > *:first-child {
  margin-left: 0;
}

.gap-items-5 > *:last-child {
  margin-right: 0;
}

.gap-multiline-items,
.gap-multiline-items-3 {
  margin: -8px;
}

.gap-multiline-items > *,
.gap-multiline-items-3 > * {
  margin: 8px;
}

.gap-multiline-items-1 {
  margin: -2px;
}

.gap-multiline-items-1 > * {
  margin: 2px;
}

.gap-multiline-items-2 {
  margin: -4px;
}

.gap-multiline-items-2 > * {
  margin: 4px;
}

.gap-multiline-items-4 {
  margin: -12px;
}

.gap-multiline-items-4 > * {
  margin: 12px;
}

.gap-multiline-items-5 {
  margin: -16px;
}

.gap-multiline-items-5 > * {
  margin: 16px;
}

.font-body,
.font-opensans {
  font-family: "Open Sans", sans-serif;
}

.font-title,
.font-raleway {
  font-family: "Dosis", "Open Sans", sans-serif;
}

.fs-8 {
  font-size: 8px !important;
}

.fs-9 {
  font-size: 9px !important;
}

.fs-10 {
  font-size: 10px !important;
}

.fs-11 {
  font-size: 11px !important;
}

.fs-12 {
  font-size: 12px !important;
}

.fs-13 {
  font-size: 13px !important;
}

.fs-14 {
  font-size: 14px !important;
}

.fs-15 {
  font-size: 15px !important;
}

.fs-16 {
  font-size: 16px !important;
}

.fs-17 {
  font-size: 17px !important;
}

.fs-18 {
  font-size: 18px !important;
}

.fs-19 {
  font-size: 19px !important;
}

.fs-20 {
  font-size: 20px !important;
}

.fs-22 {
  font-size: 22px !important;
}

.fs-24 {
  font-size: 24px !important;
}

.fs-25 {
  font-size: 25px !important;
}

.fs-26 {
  font-size: 26px !important;
}

.fs-28 {
  font-size: 28px !important;
}

.fs-30 {
  font-size: 30px !important;
  line-height: 1.2;
}

.fs-35 {
  font-size: 35px !important;
  line-height: 1.2;
}

.fs-40 {
  font-size: 40px !important;
  line-height: 1.2;
}

.fs-45 {
  font-size: 45px !important;
  line-height: 1.2;
}

.fs-50 {
  font-size: 50px !important;
  line-height: 1.2;
}

.fs-60 {
  font-size: 60px !important;
  line-height: 1.2;
}

.fs-70 {
  font-size: 70px !important;
  line-height: 1.2;
}

.fs-80 {
  font-size: 80px !important;
  line-height: 1.2;
}

.fs-90 {
  font-size: 90px !important;
  line-height: 1.2;
}

.fw-100 {
  font-weight: 100 !important;
}

.fw-200 {
  font-weight: 200 !important;
}

.fw-300 {
  font-weight: 300 !important;
}

.fw-400 {
  font-weight: 400 !important;
}

.fw-500 {
  font-weight: 500 !important;
}

.fw-600 {
  font-weight: 600 !important;
}

.fw-700 {
  font-weight: 700 !important;
}

.fw-800 {
  font-weight: 800 !important;
}

.fw-900 {
  font-weight: 900 !important;
}

.lh-0 {
  line-height: 0;
}

.lh-1 {
  line-height: 1;
}

.lh-11 {
  line-height: 1.1;
}

.lh-12 {
  line-height: 1.2;
}

.lh-13 {
  line-height: 1.3;
}

.lh-14 {
  line-height: 1.4;
}

.lh-15 {
  line-height: 1.5;
}

.lh-16 {
  line-height: 1.6;
}

.lh-17 {
  line-height: 1.7;
}

.lh-18 {
  line-height: 1.8;
}

.lh-19 {
  line-height: 1.9;
}

.lh-2 {
  line-height: 2;
}

.lh-22 {
  line-height: 2.2;
}

.lh-24 {
  line-height: 2.4;
}

.lh-25 {
  line-height: 2.5;
}

.lh-26 {
  line-height: 2.6;
}

.lh-28 {
  line-height: 2.8;
}

.lh-3 {
  line-height: 3;
}

.lh-35 {
  line-height: 3.5;
}

.lh-4 {
  line-height: 4;
}

.lh-45 {
  line-height: 4.5;
}

.lh-5 {
  line-height: 5;
}

.ls-0,
.letter-spacing-0 {
  letter-spacing: 0px;
}

.ls-1,
.letter-spacing-1 {
  letter-spacing: 1px;
}

.ls-2,
.letter-spacing-2 {
  letter-spacing: 2px;
}

.ls-3,
.letter-spacing-3 {
  letter-spacing: 3px;
}

.ls-4,
.letter-spacing-4 {
  letter-spacing: 4px;
}

.ls-5,
.letter-spacing-5 {
  letter-spacing: 5px;
}

.text-truncate {
  width: auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
