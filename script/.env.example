APP_NAME=WORKSUITE-SAAS
APP_KEY=base64:wID/CO/WkfE3yoWpQOoy15de8FtOpvC3dM/z5FuHiEU=

# Do not change APP_ENV to anything else. It will affect emailing from admin panel
# and other configuration from databse and system may crash
APP_ENV=codecanyon
APP_DEBUG=false

APP_URL=http://localhost

#For redirect to https
REDIRECT_HTTPS=false

MAIL_FROM_VERIFIED_EMAIL=true

# Add here if you main app is installed on some subdomain
MAIN_APPLICATION_SUBDOMAIN=

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
