{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel", "worksuite"], "license": "MIT", "require": {"php": "^8.1", "authorizenet/authorizenet": "^2.0", "barryvdh/laravel-dompdf": "^2.0.0", "barryvdh/laravel-translation-manager": "^0.6.3", "billowapp/payfast": "^0.6.2", "billowapp/show-me-the-money": "0.4.4", "craftsys/msg91-laravel-notification-channel": "^0.7.0", "doctrine/dbal": "^3.0", "dompdf/dompdf": "2.0.4", "edujugon/push-notification": "^5.2", "eluceo/ical": "^0.16.1", "endroid/qr-code": "^5.0", "froiden/envato": "^5.0", "froiden/laravel-installer": "^1.6", "froiden/laravel-rest-api": "^10.0", "google/apiclient": "^2.10", "graham-campbell/gitlab": "^7.0", "graham-campbell/markdown": "^15.0", "guzzlehttp/guzzle": "^7.2", "hisorange/browser-detect": "^5.0", "http-interop/http-factory-guzzle": "^1.0", "intervention/image": "^2.5", "ivanomatteo/laravel-device-tracking": "dev-master as 1.0.1", "kingflamez/laravelrave": "^4.5", "laravel-lang/lang": "~6.1", "laravel-notification-channels/onesignal": "^2.5", "laravel-notification-channels/telegram": "^4.0", "laravel-notification-channels/twilio": "^3.3", "laravel/cashier": "^14.5", "laravel/fortify": "^1.7", "laravel/framework": "^10.0", "laravel/helpers": "^1.4", "laravel/sanctum": "^3.2", "laravel/slack-notification-channel": "^3.2", "laravel/socialite": "^5.1", "laravel/tinker": "^2.8", "laravel/vonage-notification-channel": "^3.0", "laravelcollective/html": "^6.2", "league/flysystem": "~3.0", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1.41", "macellan/laravel-zip": "^1.0", "macsidigital/laravel-zoom": "8.0.1", "mailchimp/marketing": "^3.0", "mitchbred/entrust": "^2.1", "mollie/laravel-mollie": "^2.0", "nwidart/laravel-modules": "10.0.6", "opcodesio/log-viewer": "^3.8", "paragonie/random_compat": "^9.99", "paypal/rest-api-sdk-php": "dev-laravel10", "pcinaglia/laraupdater": "^1.0", "psr/http-factory-implementation": "*", "pusher/pusher-php-server": "^7.2", "pusher/pusher-push-notifications": "^2.0", "quickbooks/v3-php-sdk": "^6.1", "razorpay/razorpay": "^2.5", "saloonphp/xml-wrangler": "^0.2.0", "sentry/sentry-laravel": "^4.2", "setasign/fpdf": "^1.8", "spatie/laravel-backup": "8.*", "square/square": "16.0.0.20211117", "stevebauman/location": "^7.2", "stichoza/google-translate-php": "^4.1", "stripe/stripe-php": "^7.66", "tanmuhittin/laravel-google-translate": "^2.1", "twilio/sdk": "^6.13", "unicodeveloper/laravel-paystack": "^1.0", "webklex/laravel-imap": "5.3.0", "webklex/laravel-pdfmerger": "^1.3", "yajra/laravel-datatables-buttons": "^10.0", "yajra/laravel-datatables-html": "^10.8", "yajra/laravel-datatables-oracle": "^10.8"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.10", "barryvdh/laravel-debugbar": "^3.5", "larastan/larastan": "^2.8", "phpro/grumphp": "^1.4", "laravel/pint": "^1.0", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "repositories": [{"type": "vcs", "url": "https://github.com/Froiden/payfast"}, {"type": "vcs", "url": "https://github.com/Froiden/laravel-device-tracking"}, {"type": "vcs", "url": "https://github.com/Froiden/show-me-the-money"}, {"type": "vcs", "url": "https://github.com/Froiden/laravelrave"}, {"type": "vcs", "url": "https://github.com/Froiden/laravel-dompdf"}, {"type": "vcs", "url": "https://github.com/Froiden/dompdf"}, {"type": "vcs", "url": "https://github.com/Froiden/PayPal-PHP-SDK"}], "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "phpro/grumphp": true, "pestphp/pest-plugin": true, "php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": ["barryvdh/laravel-ide-helper"]}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/", "Froiden\\Envato\\": "froiden-verify/src"}, "files": ["app/Helper/start.php"], "exclude-from-classmap": ["Modules/UniversalBundle/Modules/*"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"]}}