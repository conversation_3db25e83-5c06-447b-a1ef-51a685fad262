#!/bin/bash

# WorkSuite SAAS Docker Stop Script
# This script helps you stop the WorkSuite SAAS application

set -e

echo "🛑 Stopping WorkSuite SAAS Docker containers..."

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
elif docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    echo "❌ Docker Compose is not available."
    exit 1
fi

# Stop and remove containers
echo "📦 Stopping containers..."
$COMPOSE_CMD down

# Optional: Remove volumes (uncomment if you want to remove data)
# echo "🗑️  Removing volumes..."
# $COMPOSE_CMD down -v

echo "✅ WorkSuite SAAS containers stopped successfully!"
echo ""
echo "💡 Tips:"
echo "   - To start again: ./docker-start.sh"
echo "   - To remove all data: $COMPOSE_CMD down -v"
echo "   - To view stopped containers: docker ps -a"
