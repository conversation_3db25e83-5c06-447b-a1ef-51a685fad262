# WorkSuite SAAS Docker Setup

Đây là setup Docker hoàn chỉnh cho ứng dụng WorkSuite SAAS với tất cả các services cần thiết.

## Cấu trúc Services

- **App**: <PERSON>vel application (PHP 8.2-FPM)
- **Nginx**: Web server
- **MySQL**: Database server
- **Redis**: Cache và session storage
- **Queue**: <PERSON><PERSON> queue worker
- **Scheduler**: <PERSON><PERSON> task scheduler
- **phpMyAdmin**: Database management interface

## Ports

- **Web Application**: http://localhost:1080
- **phpMyAdmin**: http://localhost:1081
- **MySQL**: localhost:1306
- **Redis**: localhost:1379

## Cài đặt và Sử dụng

### Cài đặt lần đầu

```bash
# Cài đặt hoàn chỉnh với mô hình freemium (khuyến nghị)
./setup-freemium-worksuite.sh

# Hoặc cài đặt thông thường
./worksuite.sh install

# Hoặc sử dụng script setup trực tiếp
./setup.sh
```

### Mô hình Freemium

Dự án đã được cấu hình với mô hình freemium:
- **Miễn phí**: 5 nhân viên, 3 dự án, 10 khách hàng, 100MB lưu trữ
- **Trả phí**: Không giới hạn + tính năng premium
- **Không kiểm tra license**: Loại bỏ hoàn toàn kiểm tra bản quyền Envato

### Quản lý hệ thống

```bash
# Sử dụng script quản lý chính (khuyến nghị)
./worksuite.sh

# Hoặc các lệnh cụ thể:
./worksuite.sh start      # Khởi động
./worksuite.sh stop       # Dừng
./worksuite.sh restart    # Khởi động lại
./worksuite.sh status     # Kiểm tra trạng thái
```

### Scripts tiện ích

```bash
# Khởi động/dừng nhanh
./docker-start.sh
./docker-stop.sh

# Xem logs
./docker-logs.sh [service]

# Backup nhanh
./docker-backup.sh
```

## Thông tin đăng nhập

### Database
- **Host**: mysql (trong container) / localhost:1306 (từ host)
- **Database**: worksuite_saas
- **Username**: worksuite_user
- **Password**: SecurePass123!

### Redis
- **Host**: redis (trong container) / localhost:1379 (từ host)
- **Password**: RedisPass123!

### phpMyAdmin
- **URL**: http://localhost:1081
- **Username**: worksuite_user
- **Password**: SecurePass123!

## Lệnh hữu ích

### Sử dụng script quản lý chính

```bash
# Menu tương tác
./worksuite.sh

# Lệnh cụ thể
./worksuite.sh logs [service]        # Xem logs
./worksuite.sh shell [service]       # Vào container
./worksuite.sh artisan migrate       # Chạy artisan
./worksuite.sh backup               # Tạo backup
./worksuite.sh troubleshoot         # Khắc phục sự cố
./worksuite.sh maintenance          # Bảo trì hệ thống
```

### Troubleshooting và Maintenance

```bash
# Khắc phục sự cố tự động
./troubleshoot.sh --auto-fix

# Kiểm tra hệ thống
./troubleshoot.sh --check

# Bảo trì hệ thống
./maintenance.sh backup            # Backup
./maintenance.sh update             # Cập nhật
./maintenance.sh optimize           # Tối ưu hóa
./maintenance.sh clean              # Dọn dẹp
```

### Docker commands trực tiếp

```bash
# Xem logs
docker-compose logs -f [service]

# Truy cập container
docker-compose exec app bash

# Chạy artisan commands
docker-compose exec app php artisan [command]

# Backup/Restore database
docker-compose exec mysql mysqldump -u worksuite_user -pSecurePass123! worksuite_saas > backup.sql
docker-compose exec -T mysql mysql -u worksuite_user -pSecurePass123! worksuite_saas < backup.sql
```

## Cấu trúc thư mục

```
.
├── docker/
│   ├── nginx/
│   │   └── nginx.conf
│   ├── php/
│   │   ├── local.ini
│   │   └── opcache.ini
│   ├── mysql/
│   │   └── my.cnf
│   ├── supervisor/
│   │   └── supervisord.conf
│   └── entrypoint.sh
├── backups/                     # Thư mục backup tự động
├── docker-compose.yml
├── Dockerfile
├── worksuite.sh                 # Script quản lý chính
├── setup.sh                     # Script cài đặt
├── create-docker-configs.sh     # Script tạo config
├── troubleshoot.sh              # Script khắc phục sự cố
├── maintenance.sh               # Script bảo trì
├── docker-start.sh              # Script khởi động nhanh
├── docker-stop.sh               # Script dừng nhanh
├── docker-logs.sh               # Script xem logs
├── docker-backup.sh             # Script backup nhanh
└── README.md
```

## Mô tả Scripts

### worksuite.sh - Script Quản lý Chính
- Menu tương tác để quản lý toàn bộ hệ thống
- Tích hợp tất cả chức năng từ các script khác
- Hỗ trợ cả command line và interactive mode

### setup.sh - Script Cài đặt Hoàn chỉnh
- Kiểm tra system requirements
- Tạo tất cả file cấu hình Docker
- Cài đặt và khởi động hệ thống
- Chạy migrations và seeders
- Tạo utility scripts

### troubleshoot.sh - Script Khắc phục Sự cố
- Kiểm tra trạng thái containers
- Test kết nối database và Redis
- Kiểm tra file permissions
- Auto-fix các vấn đề thường gặp
- Interactive troubleshooting menu

### maintenance.sh - Script Bảo trì
- Backup/restore database và files
- Update application
- Optimize performance
- Clean system
- Monitor resources
- Security checks

### remove-license-check.sh - Loại bỏ Kiểm tra License
- Thay thế kiểm tra license Envato
- Tạo mô hình freemium
- Cấu hình giới hạn miễn phí
- Tạo giao diện billing

### setup-freemium-worksuite.sh - Setup Tổng hợp Freemium
- Cài đặt hoàn chỉnh WorkSuite SAAS
- Loại bỏ tất cả kiểm tra license
- Triển khai mô hình freemium
- Cấu hình billing và thanh toán

## Troubleshooting

### Nếu gặp lỗi permission
```bash
docker-compose exec app chown -R worksuite:www-data storage bootstrap/cache
docker-compose exec app chmod -R 775 storage bootstrap/cache
```

### Nếu gặp lỗi database connection
```bash
# Kiểm tra MySQL có chạy không
docker-compose ps mysql

# Kiểm tra logs MySQL
docker-compose logs mysql

# Test connection
docker-compose exec app php artisan tinker
# Trong tinker: DB::connection()->getPdo();
```

### Nếu gặp lỗi Redis connection
```bash
# Kiểm tra Redis có chạy không
docker-compose ps redis

# Test Redis connection
docker-compose exec redis redis-cli ping
```

## Cập nhật ứng dụng

```bash
# Pull code mới
git pull

# Rebuild containers
docker-compose down
docker-compose up -d --build

# Chạy migrations nếu có
docker-compose exec app php artisan migrate

# Clear cache
docker-compose exec app php artisan cache:clear
docker-compose exec app php artisan config:clear
```

## Backup và Restore

### Backup
```bash
# Backup database
docker-compose exec mysql mysqldump -u worksuite_user -pSecurePass123! worksuite_saas > backup_$(date +%Y%m%d_%H%M%S).sql

# Backup files
tar -czf files_backup_$(date +%Y%m%d_%H%M%S).tar.gz storage/ public/uploads/
```

### Restore
```bash
# Restore database
docker-compose exec -T mysql mysql -u worksuite_user -pSecurePass123! worksuite_saas < backup.sql

# Restore files
tar -xzf files_backup.tar.gz
```

## Monitoring

```bash
# Xem resource usage
docker stats

# Xem logs realtime
docker-compose logs -f

# Kiểm tra health của services
docker-compose ps
```

## Security Notes

- Đổi passwords mặc định trong production
- Sử dụng HTTPS trong production
- Cấu hình firewall để chỉ mở các ports cần thiết
- Backup định kỳ
- Cập nhật Docker images thường xuyên

## Support

Nếu gặp vấn đề, hãy kiểm tra:
1. Logs của các services: `docker-compose logs`
2. Status của containers: `docker-compose ps`
3. Resource usage: `docker stats`
4. Network connectivity giữa containers
