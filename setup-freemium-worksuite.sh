#!/bin/bash

# Script tổng hợp để setup WorkSuite SAAS với mô hình freemium
# Loại bỏ kiểm tra bản quyền và cho phép sử dụng miễn phí

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    WorkSuite SAAS Freemium Setup"
    echo "    Complete Installation & License Removal"
    echo "=================================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

check_requirements() {
    print_step "Checking requirements..."
    
    # Check if all required scripts exist
    local missing_scripts=()
    
    [ ! -f "setup.sh" ] && missing_scripts+=("setup.sh")
    [ ! -f "remove-license-check.sh" ] && missing_scripts+=("remove-license-check.sh")
    [ ! -f "customize-freemium.sh" ] && missing_scripts+=("customize-freemium.sh")
    
    if [ ${#missing_scripts[@]} -gt 0 ]; then
        print_error "Missing required scripts: ${missing_scripts[*]}"
        print_info "Please ensure all scripts are in the same directory."
        return 1
    fi
    
    # Make scripts executable
    chmod +x setup.sh remove-license-check.sh customize-freemium.sh
    
    print_success "All requirements met!"
}

run_initial_setup() {
    print_step "Running initial WorkSuite SAAS setup..."
    
    # Run the main setup script
    ./setup.sh
    
    print_success "Initial setup completed!"
}

remove_license_verification() {
    print_step "Removing license verification and implementing freemium model..."
    
    # Run license removal script
    ./remove-license-check.sh
    
    print_success "License verification removed!"
}

customize_freemium_features() {
    print_step "Customizing freemium features and billing..."
    
    # Run freemium customization script
    ./customize-freemium.sh
    
    print_success "Freemium features customized!"
}

setup_freemium_database() {
    print_step "Setting up freemium database..."
    
    # Run freemium database setup
    if [ -f "setup-freemium-db.sh" ]; then
        chmod +x setup-freemium-db.sh
        ./setup-freemium-db.sh
    fi
    
    print_success "Freemium database setup completed!"
}

restart_services() {
    print_step "Restarting services to apply changes..."
    
    docker-compose restart
    
    # Wait for services to be ready
    print_info "Waiting for services to restart..."
    sleep 30
    
    print_success "Services restarted!"
}

test_freemium_setup() {
    print_step "Testing freemium setup..."
    
    # Test web application
    local web_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1080 2>/dev/null || echo "000")
    if [[ "$web_status" =~ ^(200|302)$ ]]; then
        print_success "Web application is responding (HTTP $web_status)"
    else
        print_warning "Web application might not be ready yet (HTTP $web_status)"
    fi
    
    # Test database connection
    if docker-compose exec -T mysql mysql -uworksuite_user -pSecurePass123! -e "SELECT 1" >/dev/null 2>&1; then
        print_success "Database connection is working"
    else
        print_warning "Database connection issues detected"
    fi
    
    print_success "Freemium setup testing completed!"
}

create_freemium_documentation() {
    print_step "Creating freemium documentation..."
    
    cat > FREEMIUM-GUIDE.md << 'EOF'
# WorkSuite SAAS Freemium Model Guide

## Overview
This WorkSuite SAAS installation has been configured with a freemium model that allows users to use the system for free with limitations, and upgrade to premium plans for full features.

## Freemium Features

### Free Plan Limitations
- **Employees**: Maximum 5 employees
- **Projects**: Maximum 3 projects  
- **Clients**: Maximum 10 clients
- **Storage**: 100MB storage limit
- **Features**: Basic CRM and task management only

### Premium Features (Require Upgrade)
- Time tracking
- Advanced invoicing
- Detailed reports and analytics
- API access
- Custom fields
- Third-party integrations
- White labeling
- Priority support

## How It Works

### 1. License Check Removal
- Removed Envato purchase code verification
- Replaced with custom freemium logic
- No more license expiration blocks

### 2. Package-Based Limitations
- Users start with free package automatically
- Soft limits with upgrade prompts
- Graceful degradation for premium features

### 3. Billing Integration
- Professional billing interface
- Multiple payment gateway support
- Automatic plan upgrades

## Configuration

### Freemium Limits
Edit `config/freemium.php` to customize:
```php
'limits' => [
    'employees' => 5,
    'projects' => 3,
    'clients' => 10,
    'storage_mb' => 100,
],
```

### Premium Features
Edit `config/freemium.php` to define premium features:
```php
'premium_features' => [
    'time_tracking' => true,
    'invoicing' => true,
    'advanced_reports' => true,
    // ...
],
```

## Usage

### For Users
1. Sign up and get free access immediately
2. Use basic features without restrictions
3. Hit limits and see upgrade prompts
4. Upgrade to premium for full access

### For Administrators
1. Monitor usage in billing dashboard
2. Customize limits and features
3. Set up payment gateways
4. Track conversions and revenue

## Billing Routes
- `/freemium/billing` - Main billing dashboard
- `/freemium/billing/upgrade` - Plan upgrade page
- `/freemium/billing/usage` - Usage statistics

## Payment Integration

### Supported Gateways
- Stripe (recommended)
- PayPal
- Authorize.net
- Razorpay
- And more...

### Setup Payment Gateway
1. Edit `app/Http/Controllers/FreemiumBillingController.php`
2. Implement payment processing in `processPayment()` method
3. Configure webhook endpoints
4. Test payment flow

## Customization

### Branding
- Edit views in `resources/views/freemium/`
- Customize CSS and styling
- Add your company branding

### Limits and Features
- Modify `app/Helper/freemium.php`
- Update `config/freemium.php`
- Adjust database seeders

### Upgrade Messages
- Edit messages in `config/freemium.php`
- Customize upgrade prompts
- Add feature comparison tables

## Monitoring

### Usage Analytics
- Track user engagement
- Monitor conversion rates
- Analyze feature usage

### Revenue Tracking
- Monthly recurring revenue (MRR)
- Customer lifetime value (CLV)
- Churn rate analysis

## Security Considerations

### Data Protection
- Ensure GDPR compliance
- Implement data retention policies
- Secure payment processing

### Access Control
- Validate feature access
- Prevent privilege escalation
- Audit user actions

## Troubleshooting

### Common Issues
1. **License errors**: Ensure all license checks are removed
2. **Payment failures**: Check payment gateway configuration
3. **Limit bypassing**: Validate middleware implementation
4. **Database errors**: Run migrations and seeders

### Debug Mode
Enable debug mode in `.env`:
```
APP_DEBUG=true
FREEMIUM_DEBUG=true
```

## Support

For technical support and customization:
1. Check logs in `storage/logs/`
2. Review freemium configuration
3. Test payment integration
4. Monitor user feedback

## Legal Considerations

### Terms of Service
- Update terms for freemium model
- Define usage limitations
- Specify upgrade requirements

### Privacy Policy
- Data collection practices
- Payment information handling
- User rights and obligations

---

**Note**: This freemium model removes all license restrictions and allows unlimited free usage of basic features. Ensure this complies with your business model and legal requirements.
EOF

    print_success "Freemium documentation created!"
}

print_final_summary() {
    echo
    echo -e "${GREEN}"
    echo "=================================================="
    echo "    WorkSuite SAAS Freemium Setup Complete!"
    echo "=================================================="
    echo -e "${NC}"
    echo
    echo -e "${CYAN}🎉 Installation Summary:${NC}"
    echo "  ✅ WorkSuite SAAS installed successfully"
    echo "  ✅ License verification completely removed"
    echo "  ✅ Freemium model implemented"
    echo "  ✅ Professional billing interface created"
    echo "  ✅ Database configured with free packages"
    echo "  ✅ All services running and tested"
    echo
    echo -e "${CYAN}🌐 Access URLs:${NC}"
    echo "  • Main Application: http://localhost:1080"
    echo "  • phpMyAdmin: http://localhost:1081"
    echo "  • Billing Dashboard: http://localhost:1080/freemium/billing"
    echo
    echo -e "${CYAN}📊 Freemium Limits:${NC}"
    echo "  • Free Plan: 5 employees, 3 projects, 10 clients, 100MB storage"
    echo "  • Premium features locked behind paywall"
    echo "  • Graceful upgrade prompts for users"
    echo
    echo -e "${CYAN}🔧 Next Steps:${NC}"
    echo "  1. Configure payment gateway in FreemiumBillingController"
    echo "  2. Customize branding and styling"
    echo "  3. Set up email notifications"
    echo "  4. Test the complete user journey"
    echo "  5. Deploy to production"
    echo
    echo -e "${CYAN}📚 Documentation:${NC}"
    echo "  • Read FREEMIUM-GUIDE.md for detailed information"
    echo "  • Check config/freemium.php for configuration"
    echo "  • Review app/Helper/freemium.php for logic"
    echo
    echo -e "${YELLOW}⚠️  Important Notes:${NC}"
    echo "  • All license checks have been removed"
    echo "  • Users can now use the system for free"
    echo "  • Implement proper payment processing before production"
    echo "  • Ensure compliance with your business model"
    echo
    echo -e "${GREEN}🚀 Your freemium WorkSuite SAAS is ready to use!${NC}"
    echo
}

# Main execution
main() {
    print_header
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root. Consider running as a regular user."
    fi
    
    check_requirements
    run_initial_setup
    remove_license_verification
    customize_freemium_features
    setup_freemium_database
    restart_services
    test_freemium_setup
    create_freemium_documentation
    print_final_summary
}

# Show usage if --help
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    echo "Usage: $0"
    echo
    echo "This script will:"
    echo "  1. Install WorkSuite SAAS with Docker"
    echo "  2. Remove all license verification"
    echo "  3. Implement freemium model"
    echo "  4. Set up billing interface"
    echo "  5. Configure free and premium packages"
    echo
    echo "Requirements:"
    echo "  - Docker and Docker Compose installed"
    echo "  - All setup scripts in the same directory"
    echo "  - WorkSuite SAAS source code"
    echo
    echo "The installation will be accessible at:"
    echo "  - Web App: http://localhost:1080"
    echo "  - phpMyAdmin: http://localhost:1081"
    echo
    exit 0
fi

# Run main function
main "$@"
