# WorkSuite SAAS - Docker Deployment Guide

This guide will help you deploy WorkSuite SAAS using Docker Compose with PHP, Nginx, MySQL, and Redis.

## 🚀 Quick Start

### Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)
- At least 2GB of available RAM
- At least 5GB of available disk space

### Installation

1. **Clone or navigate to your WorkSuite SAAS directory**
   ```bash
   cd /path/to/worksuite-saas
   ```

2. **Start the application**
   ```bash
   chmod +x docker-start.sh
   ./docker-start.sh
   ```

3. **Access the application**
   - Application: http://localhost:1080
   - PhpMyAdmin: http://localhost:1081

## 📋 Services & Ports

| Service | Internal Port | External Port | Description |
|---------|---------------|---------------|-------------|
| Nginx | 80/443 | 1080/1443 | Web server |
| MySQL | 3306 | 1306 | Database |
| Redis | 6379 | 1379 | Cache & Sessions |
| PhpMyAdmin | 80 | 1081 | Database management |

## 🔐 Security Features

### Generated Credentials
The setup automatically generates secure random passwords for:
- MySQL database user
- MySQL root user  
- Redis authentication
- Laravel application key

### Security Headers
Nginx is configured with security headers:
- X-Frame-Options
- X-XSS-Protection
- X-Content-Type-Options
- Content Security Policy

### Network Isolation
All services run in an isolated Docker network for enhanced security.

## 🛠️ Management Commands

### Start Services
```bash
./docker-start.sh
```

### Stop Services
```bash
./docker-stop.sh
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f app
docker-compose logs -f nginx
docker-compose logs -f mysql
```

### Access Container Shell
```bash
# PHP/Laravel container
docker-compose exec app bash

# MySQL container
docker-compose exec mysql bash

# Nginx container
docker-compose exec nginx sh
```

### Database Operations
```bash
# Run migrations
docker-compose exec app php artisan migrate

# Seed database
docker-compose exec app php artisan db:seed

# Clear cache
docker-compose exec app php artisan cache:clear

# Generate application key
docker-compose exec app php artisan key:generate
```

## 📁 Directory Structure

```
.
├── docker-compose.yml          # Main Docker Compose configuration
├── Dockerfile                  # PHP container configuration
├── .env.docker                 # Environment template
├── docker-start.sh            # Startup script
├── docker-stop.sh             # Stop script
└── docker/
    ├── nginx/
    │   └── nginx.conf          # Nginx configuration
    ├── php/
    │   ├── local.ini           # PHP configuration
    │   └── opcache.ini         # OPcache configuration
    ├── mysql/
    │   ├── my.cnf              # MySQL configuration
    │   └── init/               # Database initialization scripts
    ├── supervisor/
    │   └── supervisord.conf    # Process management
    └── entrypoint.sh           # Container startup script
```

## 🔧 Configuration

### Environment Variables
Key environment variables in `.env`:

```bash
# Database
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=worksuite_saas
DB_USERNAME=worksuite_user
DB_PASSWORD=<generated>

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=<generated>

# Application
APP_URL=http://localhost:1080
```

### Custom Configuration
- **PHP Settings**: Edit `docker/php/local.ini`
- **Nginx Settings**: Edit `docker/nginx/nginx.conf`
- **MySQL Settings**: Edit `docker/mysql/my.cnf`

## 🚨 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   lsof -i :1080
   
   # Change ports in docker-compose.yml if needed
   ```

2. **Permission Issues**
   ```bash
   # Fix storage permissions
   docker-compose exec app chown -R worksuite:www-data storage bootstrap/cache
   docker-compose exec app chmod -R 775 storage bootstrap/cache
   ```

3. **Database Connection Issues**
   ```bash
   # Check if MySQL is ready
   docker-compose exec app nc -z mysql 3306
   
   # View MySQL logs
   docker-compose logs mysql
   ```

4. **Application Not Loading**
   ```bash
   # Clear all caches
   docker-compose exec app php artisan cache:clear
   docker-compose exec app php artisan config:clear
   docker-compose exec app php artisan route:clear
   docker-compose exec app php artisan view:clear
   ```

### Performance Optimization

1. **Enable OPcache** (already configured)
2. **Use Redis for sessions and cache** (already configured)
3. **Optimize MySQL** (configured in `my.cnf`)

### Backup & Restore

#### Backup Database
```bash
docker-compose exec mysql mysqldump -u worksuite_user -p worksuite_saas > backup.sql
```

#### Restore Database
```bash
docker-compose exec -T mysql mysql -u worksuite_user -p worksuite_saas < backup.sql
```

## 🔄 Updates

To update the application:

1. Pull latest code
2. Rebuild containers:
   ```bash
   docker-compose down
   docker-compose up -d --build
   ```

## 📞 Support

For issues related to:
- **Docker setup**: Check this README and Docker logs
- **WorkSuite SAAS**: Refer to the main application documentation
- **Laravel framework**: Check Laravel documentation

## 🔒 Production Deployment

For production deployment:

1. **Change default passwords** in `.env`
2. **Enable HTTPS** by configuring SSL certificates
3. **Set up proper domain** in `APP_URL`
4. **Configure email settings** for notifications
5. **Set up backup strategy** for database and files
6. **Monitor logs** and set up log rotation
7. **Configure firewall** to restrict access to necessary ports only

## 📝 License

This Docker configuration is provided as-is for WorkSuite SAAS deployment.
