# WorkSuite SAAS - Quick Start Guide

## 🚀 Cài đặt nhanh (3 bước)

### Bước 1: <PERSON><PERSON><PERSON> bị
```bash
# Tải về và giải nén source code WorkSuite SAAS
# Đặt tất cả files vào thư mục dự án

# Làm cho scripts có thể thực thi
./make-executable.sh
```

### Bước 2: Cài đặt
```bash
# Cài đặt hoàn chỉnh (tự động)
./worksuite.sh install

# Hoặc sử dụng setup script trực tiếp
./setup.sh
```

### Bước 3: Truy cập
- **Web Application**: http://localhost:1080
- **phpMyAdmin**: http://localhost:1081

## 🎯 Sử dụng cơ bản

### Script quản lý chính
```bash
./worksuite.sh                    # Menu tương tác
./worksuite.sh start              # Khởi động
./worksuite.sh stop               # Dừng
./worksuite.sh status             # Kiểm tra trạng thái
./worksuite.sh logs               # Xem logs
./worksuite.sh backup             # Tạo backup
```

### Scripts nhanh
```bash
./docker-start.sh                 # Khởi động nhanh
./docker-stop.sh                  # Dừng nhanh
./docker-logs.sh                  # Xem logs
./docker-backup.sh                # Backup nhanh
```

## 🔧 Khắc phục sự cố

### Nếu gặp lỗi
```bash
./troubleshoot.sh                 # Menu khắc phục sự cố
./troubleshoot.sh --check         # Kiểm tra hệ thống
./troubleshoot.sh --auto-fix      # Sửa lỗi tự động
```

### Lỗi thường gặp

#### 1. Port đã được sử dụng
```bash
# Kiểm tra port nào đang sử dụng
lsof -i :1080
lsof -i :1081
lsof -i :1306
lsof -i :1379

# Dừng service đang sử dụng port hoặc thay đổi port trong docker-compose.yml
```

#### 2. Permission denied
```bash
./troubleshoot.sh
# Chọn option "Fix file permissions"
```

#### 3. Database connection error
```bash
./troubleshoot.sh
# Chọn option "Check database connection"
```

#### 4. Container không khởi động
```bash
# Xem logs để tìm lỗi
./worksuite.sh logs [service_name]

# Rebuild containers
docker-compose down
docker-compose up -d --build
```

## 🛠 Bảo trì

### Backup
```bash
./maintenance.sh backup          # Tạo backup
./worksuite.sh backup            # Backup nhanh
```

### Update
```bash
./maintenance.sh update          # Cập nhật ứng dụng
```

### Optimize
```bash
./maintenance.sh optimize        # Tối ưu hóa performance
```

### Clean
```bash
./maintenance.sh clean           # Dọn dẹp hệ thống
```

## 📊 Monitoring

### Kiểm tra trạng thái
```bash
./worksuite.sh status            # Trạng thái tổng quan
./maintenance.sh monitor         # Monitor chi tiết
docker stats                     # Resource usage
```

### Xem logs
```bash
./worksuite.sh logs              # Tất cả logs
./worksuite.sh logs app          # Logs của app
./worksuite.sh logs nginx        # Logs của nginx
./worksuite.sh logs mysql        # Logs của mysql
```

## 🔐 Thông tin đăng nhập

### Database
- **Host**: localhost:1306
- **Database**: worksuite_saas
- **Username**: worksuite_user
- **Password**: SecurePass123!

### Redis
- **Host**: localhost:1379
- **Password**: RedisPass123!

### phpMyAdmin
- **URL**: http://localhost:1081
- **Username**: worksuite_user
- **Password**: SecurePass123!

## 🚨 Lưu ý bảo mật

### Trong môi trường production:
1. **Đổi passwords mặc định**
2. **Sử dụng HTTPS**
3. **Cấu hình firewall**
4. **Backup định kỳ**
5. **Cập nhật thường xuyên**

### Đổi passwords:
```bash
# Chỉnh sửa file .env
nano .env

# Cập nhật docker-compose.yml
nano docker-compose.yml

# Restart services
./worksuite.sh restart
```

## 📞 Hỗ trợ

### Nếu gặp vấn đề:
1. Chạy `./troubleshoot.sh --check` để kiểm tra
2. Xem logs: `./worksuite.sh logs`
3. Thử auto-fix: `./troubleshoot.sh --auto-fix`
4. Restart services: `./worksuite.sh restart`

### Useful commands:
```bash
# Vào container để debug
./worksuite.sh shell

# Chạy artisan commands
./worksuite.sh artisan migrate
./worksuite.sh artisan cache:clear
./worksuite.sh artisan queue:work

# Kiểm tra database
./worksuite.sh shell
php artisan tinker
DB::connection()->getPdo();
```

## 🎉 Hoàn thành!

Bây giờ bạn có thể:
- Truy cập ứng dụng tại: http://localhost:1080
- Quản lý database tại: http://localhost:1081
- Sử dụng các scripts để quản lý hệ thống

**Chúc bạn sử dụng WorkSuite SAAS thành công!** 🚀
