# WorkSuite SAAS Docker Environment Configuration
# Generated on: $(date)

# Database Configuration
DB_DATABASE=worksuite_saas
DB_USERNAME=worksuite_user
DB_PASSWORD=8iQHlM7p3VrPbJNYNCf1vkD4Y
DB_ROOT_PASSWORD=YQdHhsoInUYfOORegX4m0m2iw

# Redis Configuration
REDIS_PASSWORD=jFBqNFxMEnl0cMivIlFxa3ygW

# Application Configuration
APP_NAME=WORKSUITE-SAAS-DOCKER
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:1080

# Security Keys (Generate new ones for production)
APP_KEY=base64:VUZmZXHOfb1CEttWVqxMZx9MEe5pdACwNsQF9THbR2s=

# Mail Configuration (Update with your SMTP settings)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Cache and Session Configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120
QUEUE_CONNECTION=redis

# File Storage Configuration
FILESYSTEM_DISK=local

# Logging Configuration
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Broadcasting Configuration
BROADCAST_DRIVER=log
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Additional Security Settings
SANCTUM_STATEFUL_DOMAINS=localhost:1080
SESSION_DOMAIN=localhost
