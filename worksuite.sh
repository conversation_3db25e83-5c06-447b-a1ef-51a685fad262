#!/bin/bash

# WorkSuite SAAS Master Management Script
# This is the main script to manage all WorkSuite SAAS operations

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    WorkSuite SAAS Management Console"
    echo "=================================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

check_prerequisites() {
    local missing=()
    
    # Check if required scripts exist
    [ ! -f "setup.sh" ] && missing+=("setup.sh")
    [ ! -f "create-docker-configs.sh" ] && missing+=("create-docker-configs.sh")
    [ ! -f "troubleshoot.sh" ] && missing+=("troubleshoot.sh")
    [ ! -f "maintenance.sh" ] && missing+=("maintenance.sh")
    
    if [ ${#missing[@]} -gt 0 ]; then
        print_error "Missing required scripts: ${missing[*]}"
        print_info "Please ensure all scripts are in the same directory."
        return 1
    fi
    
    # Make scripts executable
    chmod +x setup.sh create-docker-configs.sh troubleshoot.sh maintenance.sh
    chmod +x docker-start.sh docker-stop.sh docker-logs.sh docker-backup.sh 2>/dev/null || true
    
    return 0
}

show_status() {
    print_step "WorkSuite SAAS Status"
    
    # Check if docker-compose.yml exists
    if [ ! -f "docker-compose.yml" ]; then
        print_warning "System not installed. Run 'install' first."
        return 1
    fi
    
    # Check container status
    echo -e "${CYAN}Container Status:${NC}"
    if docker-compose ps 2>/dev/null; then
        echo
    else
        print_warning "Docker containers are not running"
        return 1
    fi
    
    # Check application health
    echo -e "${CYAN}Application Health:${NC}"
    local web_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1080 2>/dev/null || echo "000")
    if [[ "$web_status" =~ ^(200|302)$ ]]; then
        print_success "Web application is responding (HTTP $web_status)"
    else
        print_error "Web application is not responding (HTTP $web_status)"
    fi
    
    local pma_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1081 2>/dev/null || echo "000")
    if [[ "$pma_status" == "200" ]]; then
        print_success "phpMyAdmin is responding"
    else
        print_warning "phpMyAdmin is not responding (HTTP $pma_status)"
    fi
    
    # Show access URLs
    echo
    echo -e "${CYAN}Access URLs:${NC}"
    echo "  • Web Application: http://localhost:1080"
    echo "  • phpMyAdmin:      http://localhost:1081"
    echo
}

install_system() {
    print_step "Installing WorkSuite SAAS..."
    
    if [ -f "docker-compose.yml" ]; then
        read -p "System appears to be already installed. Reinstall? (y/N): " confirm
        if [[ ! $confirm =~ ^[Yy]$ ]]; then
            print_info "Installation cancelled"
            return 0
        fi
    fi
    
    ./setup.sh
}

start_system() {
    print_step "Starting WorkSuite SAAS..."
    
    if [ ! -f "docker-compose.yml" ]; then
        print_error "System not installed. Run 'install' first."
        return 1
    fi
    
    if [ -f "docker-start.sh" ]; then
        ./docker-start.sh
    else
        docker-compose up -d
        print_success "WorkSuite SAAS started successfully!"
    fi
}

stop_system() {
    print_step "Stopping WorkSuite SAAS..."
    
    if [ -f "docker-stop.sh" ]; then
        ./docker-stop.sh
    else
        docker-compose down
        print_success "WorkSuite SAAS stopped successfully!"
    fi
}

restart_system() {
    print_step "Restarting WorkSuite SAAS..."
    
    stop_system
    sleep 5
    start_system
}

show_logs() {
    local service=$1
    
    if [ -f "docker-logs.sh" ]; then
        ./docker-logs.sh $service
    else
        if [ -z "$service" ]; then
            docker-compose logs -f
        else
            docker-compose logs -f $service
        fi
    fi
}

run_troubleshoot() {
    print_step "Running troubleshooting..."
    ./troubleshoot.sh "$@"
}

run_maintenance() {
    print_step "Running maintenance..."
    ./maintenance.sh "$@"
}

quick_backup() {
    print_step "Creating quick backup..."
    ./maintenance.sh backup
}

enter_container() {
    local service=${1:-app}
    print_step "Entering $service container..."
    docker-compose exec $service bash
}

run_artisan() {
    local command="$*"
    print_step "Running artisan command: $command"
    docker-compose exec app php artisan $command
}

show_help() {
    echo "WorkSuite SAAS Management Console"
    echo
    echo "Usage: $0 <command> [options]"
    echo
    echo "System Management:"
    echo "  install                 Install WorkSuite SAAS system"
    echo "  start                   Start all services"
    echo "  stop                    Stop all services"
    echo "  restart                 Restart all services"
    echo "  status                  Show system status"
    echo
    echo "Monitoring & Logs:"
    echo "  logs [service]          Show logs (all or specific service)"
    echo "  monitor                 Monitor system resources"
    echo
    echo "Maintenance:"
    echo "  backup                  Create backup"
    echo "  maintenance [cmd]       Run maintenance operations"
    echo "  troubleshoot [cmd]      Run troubleshooting"
    echo "  update                  Update application"
    echo "  optimize                Optimize application"
    echo "  clean                   Clean system"
    echo
    echo "Development:"
    echo "  shell [service]         Enter container shell (default: app)"
    echo "  artisan <command>       Run artisan command"
    echo
    echo "Examples:"
    echo "  $0 install             # Install the system"
    echo "  $0 start               # Start all services"
    echo "  $0 logs app            # Show app container logs"
    echo "  $0 shell               # Enter app container"
    echo "  $0 artisan migrate     # Run migrations"
    echo "  $0 backup              # Create backup"
    echo
}

interactive_menu() {
    while true; do
        echo
        echo -e "${CYAN}WorkSuite SAAS Management Console${NC}"
        echo "1.  Install system"
        echo "2.  Start services"
        echo "3.  Stop services"
        echo "4.  Restart services"
        echo "5.  Show status"
        echo "6.  Show logs"
        echo "7.  Troubleshoot"
        echo "8.  Maintenance"
        echo "9.  Create backup"
        echo "10. Enter container shell"
        echo "11. Run artisan command"
        echo "12. Exit"
        echo
        read -p "Select an option (1-12): " choice
        
        case $choice in
            1)
                install_system
                ;;
            2)
                start_system
                ;;
            3)
                stop_system
                ;;
            4)
                restart_system
                ;;
            5)
                show_status
                ;;
            6)
                read -p "Enter service name (or press Enter for all): " service
                show_logs $service
                ;;
            7)
                run_troubleshoot
                ;;
            8)
                run_maintenance
                ;;
            9)
                quick_backup
                ;;
            10)
                read -p "Enter service name (default: app): " service
                enter_container ${service:-app}
                ;;
            11)
                read -p "Enter artisan command: " command
                run_artisan $command
                ;;
            12)
                echo "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please try again."
                ;;
        esac
    done
}

# Main execution
main() {
    print_header
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    case "$1" in
        "install")
            install_system
            ;;
        "start")
            start_system
            ;;
        "stop")
            stop_system
            ;;
        "restart")
            restart_system
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs $2
            ;;
        "troubleshoot")
            shift
            run_troubleshoot "$@"
            ;;
        "maintenance")
            shift
            run_maintenance "$@"
            ;;
        "backup")
            quick_backup
            ;;
        "update")
            run_maintenance update
            ;;
        "optimize")
            run_maintenance optimize
            ;;
        "clean")
            run_maintenance clean
            ;;
        "monitor")
            run_maintenance monitor
            ;;
        "shell")
            enter_container $2
            ;;
        "artisan")
            shift
            run_artisan "$@"
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "")
            interactive_menu
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 help' for usage information."
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
