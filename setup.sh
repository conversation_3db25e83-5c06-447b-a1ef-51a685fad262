#!/bin/bash

# WorkSuite SAAS Docker Setup Script
# Author: AI Assistant
# Version: 1.0

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="worksuite-saas"
DB_NAME="worksuite_saas"
DB_USER="worksuite_user"
DB_PASSWORD="SecurePass123!"
REDIS_PASSWORD="RedisPass123!"
APP_PORT="1080"
PHPMYADMIN_PORT="1081"
MYSQL_PORT="1306"
REDIS_PORT="1379"

# Functions
print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    WorkSuite SAAS Docker Setup Script"
    echo "=================================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

check_requirements() {
    print_step "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if ports are available
    check_port() {
        if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_error "Port $1 is already in use. Please stop the service using this port."
            exit 1
        fi
    }
    
    check_port $APP_PORT
    check_port $PHPMYADMIN_PORT
    check_port $MYSQL_PORT
    check_port $REDIS_PORT
    
    print_success "All requirements met!"
}

create_directories() {
    print_step "Creating directory structure..."
    
    mkdir -p docker/nginx
    mkdir -p docker/php
    mkdir -p docker/supervisor
    mkdir -p storage/logs
    mkdir -p storage/framework/cache
    mkdir -p storage/framework/sessions
    mkdir -p storage/framework/views
    mkdir -p bootstrap/cache
    
    print_success "Directory structure created!"
}

create_docker_files() {
    print_step "Creating Docker configuration files..."

    # Check if create-docker-configs.sh exists
    if [ ! -f "create-docker-configs.sh" ]; then
        print_error "create-docker-configs.sh not found! Please ensure it's in the same directory."
        exit 1
    fi

    # Make it executable and run it
    chmod +x create-docker-configs.sh
    ./create-docker-configs.sh

    print_success "Docker configuration files created!"
}

setup_environment() {
    print_step "Setting up environment configuration..."
    
    # Create .env file
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_info ".env file created from .env.example"
        else
            print_warning ".env.example not found, creating basic .env file"
            cat > .env << EOF
APP_NAME=WORKSUITE-SAAS
APP_ENV=codecanyon
APP_KEY=
APP_DEBUG=false
APP_URL=http://localhost:${APP_PORT}

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=${DB_NAME}
DB_USERNAME=${DB_USER}
DB_PASSWORD=${DB_PASSWORD}

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=${REDIS_PASSWORD}
EOF
        fi
    fi
    
    # Update .env with correct values
    sed -i.bak "s/DB_HOST=.*/DB_HOST=mysql/" .env
    sed -i.bak "s/DB_DATABASE=.*/DB_DATABASE=${DB_NAME}/" .env
    sed -i.bak "s/DB_USERNAME=.*/DB_USERNAME=${DB_USER}/" .env
    sed -i.bak "s/DB_PASSWORD=.*/DB_PASSWORD=${DB_PASSWORD}/" .env
    sed -i.bak "s/CACHE_DRIVER=.*/CACHE_DRIVER=redis/" .env
    sed -i.bak "s/SESSION_DRIVER=.*/SESSION_DRIVER=redis/" .env
    sed -i.bak "s/QUEUE_CONNECTION=.*/QUEUE_CONNECTION=redis/" .env
    
    # Add Redis configuration if not exists
    if ! grep -q "REDIS_HOST" .env; then
        echo "REDIS_HOST=redis" >> .env
        echo "REDIS_PORT=6379" >> .env
        echo "REDIS_PASSWORD=${REDIS_PASSWORD}" >> .env
    fi
    
    # Remove backup file
    rm -f .env.bak
    
    print_success "Environment configuration completed!"
}

build_containers() {
    print_step "Building Docker containers..."
    
    docker-compose build --no-cache
    
    print_success "Docker containers built successfully!"
}

start_services() {
    print_step "Starting Docker services..."
    
    docker-compose up -d
    
    # Wait for services to be ready
    print_info "Waiting for services to start..."
    sleep 30
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_success "Docker services started successfully!"
    else
        print_error "Some services failed to start. Check logs with: docker-compose logs"
        exit 1
    fi
}

setup_application() {
    print_step "Setting up Laravel application..."
    
    # Wait for MySQL to be ready
    print_info "Waiting for MySQL to be ready..."
    until docker-compose exec -T mysql mysql -u${DB_USER} -p${DB_PASSWORD} -e "SELECT 1" >/dev/null 2>&1; do
        sleep 5
        print_info "Still waiting for MySQL..."
    done
    
    # Generate application key if not set
    if ! grep -q "APP_KEY=base64:" .env; then
        print_info "Generating application key..."
        docker-compose exec -T app php artisan key:generate --no-interaction
    fi
    
    # Clear cache
    print_info "Clearing application cache..."
    docker-compose exec -T app php artisan config:clear || true
    docker-compose exec -T app php artisan cache:clear || true
    docker-compose exec -T app php artisan route:clear || true
    docker-compose exec -T app php artisan view:clear || true
    
    # Run migrations
    print_info "Running database migrations..."
    docker-compose exec -T app php artisan migrate --force --no-interaction
    
    # Run seeders
    print_info "Running database seeders..."
    docker-compose exec -T app php artisan db:seed --force --no-interaction
    
    # Set permissions
    print_info "Setting file permissions..."
    docker-compose exec -T app chown -R worksuite:www-data storage bootstrap/cache || true
    docker-compose exec -T app chmod -R 775 storage bootstrap/cache || true
    
    print_success "Laravel application setup completed!"
}

create_utility_scripts() {
    print_step "Creating utility scripts..."
    
    # Create start script
    cat > docker-start.sh << 'EOF'
#!/bin/bash
echo "Starting WorkSuite SAAS Docker containers..."
docker-compose up -d
echo "Containers started successfully!"
echo "Access the application at: http://localhost:1080"
echo "Access phpMyAdmin at: http://localhost:1081"
EOF
    chmod +x docker-start.sh
    
    # Create stop script
    cat > docker-stop.sh << 'EOF'
#!/bin/bash
echo "Stopping WorkSuite SAAS Docker containers..."
docker-compose down
echo "Containers stopped successfully!"
EOF
    chmod +x docker-stop.sh
    
    # Create logs script
    cat > docker-logs.sh << 'EOF'
#!/bin/bash
if [ -z "$1" ]; then
    echo "Showing logs for all services..."
    docker-compose logs -f
else
    echo "Showing logs for service: $1"
    docker-compose logs -f $1
fi
EOF
    chmod +x docker-logs.sh
    
    # Create backup script
    cat > docker-backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

echo "Creating database backup..."
docker-compose exec -T mysql mysqldump -uworksuite_user -pSecurePass123! worksuite_saas > $BACKUP_DIR/database_$DATE.sql

echo "Creating files backup..."
tar -czf $BACKUP_DIR/files_$DATE.tar.gz storage/ public/uploads/ || true

echo "Backup completed!"
echo "Database backup: $BACKUP_DIR/database_$DATE.sql"
echo "Files backup: $BACKUP_DIR/files_$DATE.tar.gz"
EOF
    chmod +x docker-backup.sh
    
    print_success "Utility scripts created!"
}

test_installation() {
    print_step "Testing installation..."
    
    # Test web application
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:${APP_PORT} | grep -q "302\|200"; then
        print_success "Web application is responding!"
    else
        print_warning "Web application might not be ready yet. Please check logs."
    fi
    
    # Test phpMyAdmin
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:${PHPMYADMIN_PORT} | grep -q "200"; then
        print_success "phpMyAdmin is responding!"
    else
        print_warning "phpMyAdmin might not be ready yet. Please check logs."
    fi
    
    print_success "Installation test completed!"
}

print_summary() {
    echo -e "${GREEN}"
    echo "=================================================="
    echo "    WorkSuite SAAS Setup Completed Successfully!"
    echo "=================================================="
    echo -e "${NC}"
    echo
    echo -e "${CYAN}Access URLs:${NC}"
    echo "  • Web Application: http://localhost:${APP_PORT}"
    echo "  • phpMyAdmin:      http://localhost:${PHPMYADMIN_PORT}"
    echo
    echo -e "${CYAN}Database Information:${NC}"
    echo "  • Host:     localhost:${MYSQL_PORT}"
    echo "  • Database: ${DB_NAME}"
    echo "  • Username: ${DB_USER}"
    echo "  • Password: ${DB_PASSWORD}"
    echo
    echo -e "${CYAN}Redis Information:${NC}"
    echo "  • Host:     localhost:${REDIS_PORT}"
    echo "  • Password: ${REDIS_PASSWORD}"
    echo
    echo -e "${CYAN}Utility Scripts:${NC}"
    echo "  • Start:    ./docker-start.sh"
    echo "  • Stop:     ./docker-stop.sh"
    echo "  • Logs:     ./docker-logs.sh [service]"
    echo "  • Backup:   ./docker-backup.sh"
    echo
    echo -e "${CYAN}Useful Commands:${NC}"
    echo "  • View logs:        docker-compose logs -f"
    echo "  • Access container: docker-compose exec app bash"
    echo "  • Run artisan:      docker-compose exec app php artisan [command]"
    echo
    echo -e "${YELLOW}Note: If you encounter any issues, check the logs with:${NC}"
    echo "  docker-compose logs"
    echo
}

# Main execution
main() {
    print_header
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root. Consider running as a regular user."
    fi
    
    check_requirements
    create_directories
    create_docker_files
    setup_environment
    build_containers
    start_services
    setup_application
    create_utility_scripts
    test_installation
    print_summary
}

# Run main function
main "$@"
