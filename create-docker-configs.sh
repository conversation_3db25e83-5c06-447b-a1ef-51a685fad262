#!/bin/bash

# Script to create all Docker configuration files
# This script is called by setup.sh

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

create_docker_compose() {
    print_info "Creating docker-compose.yml..."
    
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    image: worksuite-saas-app
    container_name: worksuite_app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
      - ./docker/php/opcache.ini:/usr/local/etc/php/conf.d/opcache.ini
      - ./docker/supervisor/supervisord.conf:/etc/supervisor/conf.d/supervisord.conf
    networks:
      - worksuite-network
    depends_on:
      - mysql
      - redis
    environment:
      - DB_HOST=mysql
      - DB_DATABASE=worksuite_saas
      - DB_USERNAME=worksuite_user
      - DB_PASSWORD=SecurePass123!
      - REDIS_HOST=redis
      - REDIS_PASSWORD=RedisPass123!

  queue:
    build:
      context: .
      dockerfile: Dockerfile
    image: worksuite-saas-queue
    container_name: worksuite_queue
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - worksuite-network
    depends_on:
      - mysql
      - redis
    command: php artisan queue:work --sleep=3 --tries=3 --timeout=90
    environment:
      - DB_HOST=mysql
      - DB_DATABASE=worksuite_saas
      - DB_USERNAME=worksuite_user
      - DB_PASSWORD=SecurePass123!
      - REDIS_HOST=redis
      - REDIS_PASSWORD=RedisPass123!

  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    image: worksuite-saas-scheduler
    container_name: worksuite_scheduler
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - worksuite-network
    depends_on:
      - mysql
      - redis
    command: php artisan schedule:work
    environment:
      - DB_HOST=mysql
      - DB_DATABASE=worksuite_saas
      - DB_USERNAME=worksuite_user
      - DB_PASSWORD=SecurePass123!
      - REDIS_HOST=redis
      - REDIS_PASSWORD=RedisPass123!

  nginx:
    image: nginx:alpine
    container_name: worksuite_nginx
    restart: unless-stopped
    ports:
      - "1080:80"
      - "1443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
    networks:
      - worksuite-network
    depends_on:
      - app

  mysql:
    image: mysql:8.0
    container_name: worksuite_mysql
    restart: unless-stopped
    ports:
      - "1306:3306"
    environment:
      MYSQL_DATABASE: worksuite_saas
      MYSQL_USER: worksuite_user
      MYSQL_PASSWORD: SecurePass123!
      MYSQL_ROOT_PASSWORD: RootPass123!
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - worksuite-network
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    container_name: worksuite_redis
    restart: unless-stopped
    ports:
      - "1379:6379"
    command: redis-server --requirepass RedisPass123! --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - worksuite-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: worksuite_phpmyadmin
    restart: unless-stopped
    ports:
      - "1081:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: worksuite_user
      PMA_PASSWORD: SecurePass123!
      MYSQL_ROOT_PASSWORD: RootPass123!
    networks:
      - worksuite-network
    depends_on:
      - mysql

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  worksuite-network:
    driver: bridge
EOF
    
    print_success "docker-compose.yml created!"
}

create_dockerfile() {
    print_info "Creating Dockerfile..."
    
    cat > Dockerfile << 'EOF'
FROM php:8.2-fpm

# Set working directory
WORKDIR /var/www/html

# Arguments
ARG user=worksuite
ARG uid=1000

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libmcrypt-dev \
    libgd-dev \
    jpegoptim optipng pngquant gifsicle \
    vim \
    nano \
    supervisor \
    cron \
    libicu-dev \
    libpq-dev \
    libmagickwand-dev \
    netcat-traditional \
    default-mysql-client \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        mbstring \
        exif \
        pcntl \
        bcmath \
        gd \
        zip \
        intl \
        opcache \
        sockets

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install ImageMagick extension
RUN pecl install imagick && docker-php-ext-enable imagick

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Create system user
RUN useradd -G www-data,root -u $uid -d /home/<USER>
RUN mkdir -p /home/<USER>/.composer && \
    chown -R $user:$user /home/<USER>

# Copy application files
COPY . /var/www/html

# Copy entrypoint script
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set permissions
RUN chown -R $user:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage \
    && chmod -R 755 /var/www/html/bootstrap/cache

# Switch to user
USER $user

# Expose port 9000 and start php-fpm server
EXPOSE 9000

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["php-fpm"]
EOF
    
    print_success "Dockerfile created!"
}

create_nginx_config() {
    print_info "Creating Nginx configuration..."
    
    mkdir -p docker/nginx
    
    cat > docker/nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;
    
    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_proxied expired no-cache no-store private auth;
    gzip_comp_level 6;
    gzip_min_length 1000;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    server {
        listen 80;
        server_name localhost;
        root /var/www/html/public;
        index index.php index.html index.htm;
        
        # Security
        server_tokens off;
        
        # Handle favicon
        location = /favicon.ico {
            access_log off;
            log_not_found off;
        }
        
        # Handle robots.txt
        location = /robots.txt {
            access_log off;
            log_not_found off;
        }
        
        # Main location
        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }
        
        # PHP handling
        location ~ \.php$ {
            fastcgi_pass app:9000;
            fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
            include fastcgi_params;
            fastcgi_hide_header X-Powered-By;
            
            # Increase timeouts
            fastcgi_connect_timeout 60s;
            fastcgi_send_timeout 60s;
            fastcgi_read_timeout 60s;
        }
        
        # Static files caching
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|gz)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
        
        # Deny access to hidden files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # Deny access to sensitive files
        location ~* \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
            deny all;
        }
    }
}
EOF
    
    print_success "Nginx configuration created!"
}

create_php_configs() {
    print_info "Creating PHP configurations..."
    
    mkdir -p docker/php
    
    # PHP local configuration
    cat > docker/php/local.ini << 'EOF'
upload_max_filesize=100M
post_max_size=100M
memory_limit=512M
max_execution_time=300
max_input_vars=3000
date.timezone=UTC

# Error reporting
display_errors=Off
log_errors=On
error_log=/var/log/php_errors.log

# Session
session.cookie_httponly=On
session.cookie_secure=Off
session.use_strict_mode=On

# Security
expose_php=Off
allow_url_fopen=On
allow_url_include=Off
EOF
    
    # PHP OPcache configuration
    cat > docker/php/opcache.ini << 'EOF'
opcache.enable=1
opcache.enable_cli=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.revalidate_freq=2
opcache.save_comments=1
opcache.validate_timestamps=1
opcache.fast_shutdown=1
EOF
    
    print_success "PHP configurations created!"
}

create_supervisor_config() {
    print_info "Creating Supervisor configuration..."
    
    mkdir -p docker/supervisor
    
    cat > docker/supervisor/supervisord.conf << 'EOF'
[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:php-fpm]
command=php-fpm
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/php-fpm.err.log
stdout_logfile=/var/log/supervisor/php-fpm.out.log
user=root

[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work --sleep=3 --tries=3 --timeout=90
autostart=true
autorestart=true
user=worksuite
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/supervisor/worker.log
stopwaitsecs=3600

[program:laravel-scheduler]
command=php /var/www/html/artisan schedule:work
autostart=true
autorestart=true
user=worksuite
redirect_stderr=true
stdout_logfile=/var/log/supervisor/scheduler.log
EOF
    
    print_success "Supervisor configuration created!"
}

create_mysql_config() {
    print_info "Creating MySQL configuration..."
    
    mkdir -p docker/mysql
    
    cat > docker/mysql/my.cnf << 'EOF'
[mysqld]
# Basic Settings
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
lc-messages-dir = /usr/share/mysql

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Performance
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Connection
max_connections = 200
max_connect_errors = 1000000

# Query Cache
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 2M

# Logging
general_log = 0
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Security
local-infile = 0

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
EOF
    
    print_success "MySQL configuration created!"
}

create_entrypoint_script() {
    print_info "Creating entrypoint script..."
    
    mkdir -p docker
    
    cat > docker/entrypoint.sh << 'EOF'
#!/bin/bash

# Exit on any error
set -e

echo "Starting WorkSuite SAAS Docker Container..."

# Simple wait for services (non-blocking)
echo "Waiting for services to be ready..."
sleep 10

# Change to application directory
cd /var/www/html

# Set proper permissions
echo "Setting permissions..."
chown -R worksuite:www-data storage bootstrap/cache || echo "Permission setting failed, continuing..."
chmod -R 775 storage bootstrap/cache || echo "Permission setting failed, continuing..."

echo "WorkSuite SAAS container is starting..."
echo "To run setup manually:"
echo "  docker-compose exec app php artisan migrate"
echo "  docker-compose exec app php artisan key:generate"

# Execute the main command
exec "$@"
EOF
    
    chmod +x docker/entrypoint.sh
    
    print_success "Entrypoint script created!"
}

# Main execution
main() {
    echo "Creating Docker configuration files..."
    
    create_docker_compose
    create_dockerfile
    create_nginx_config
    create_php_configs
    create_supervisor_config
    create_mysql_config
    create_entrypoint_script
    
    echo -e "${GREEN}All Docker configuration files created successfully!${NC}"
}

# Run if called directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
