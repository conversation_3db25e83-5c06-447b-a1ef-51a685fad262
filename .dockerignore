# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
README-Docker.md
documentation/

# Docker files (avoid recursion)
Dockerfile
docker-compose.yml
.dockerignore

# Environment files
.env
.env.example
.env.docker
.env.local
.env.*.local

# Dependencies
script/node_modules/
script/vendor/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
script/storage/logs/

# Cache
script/bootstrap/cache/
script/storage/framework/cache/
script/storage/framework/sessions/
script/storage/framework/views/

# Temporary files
*.tmp
*.temp

# Build artifacts
script/public/hot
script/public/storage
script/public/mix-manifest.json

# Testing
script/tests/
script/phpunit.xml
script/.phpunit.result.cache

# Development tools
script/grumphp-example.yml
script/phpstan.neon.dist
